{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# Linear Regression Analysis with Regularization Options - UNCLUBBED\n", "\n", "## Overview\n", "This notebook compares three regression approaches to reduce overfitting:\n", "- **Linear Regression** (Baseline)\n", "- **Ridge Regression** (L2 Regularization)\n", "- **Lasso Regression** (L1 Regularization)\n", "\n", "**Target Variable:** ABI MS Promo Uplift - rel\n", "Removed Weighted Distribution, removed with Numeric Dist, added ABI Coverage\n", "\n", "**Features:**\n", "    'ABI_Duration_Days',\n", "    'ABI Mechanic',\n", "    'Same Week',\n", "    'Before',\n", "    'After',\n", "    'Avg Temp',\n", "    'KSM',\n", "    'ABI vs Segment PTC Index Agg',\n", "    'ABI Coverage'\n", "\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 1. Data Loading and Preparation\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.linear_model import LinearRegression, Lasso, Ridge\n", "from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV\n", "from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error\n", "from sklearn.preprocessing import StandardScaler\n", "import statsmodels.api as sm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully\")\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading dataset...\n", "Dataset shape: (762, 21)\n"]}, {"data": {"text/plain": ["['ABI Start',\n", " 'ABI End',\n", " 'ABI Coverage',\n", " 'ABI Mechanic',\n", " 'ABI Rounded',\n", " 'Overlapping',\n", " 'Same Week',\n", " '1 wk after',\n", " '2 wk after',\n", " '1 wk before',\n", " '2 wk before',\n", " 'Avg Temp',\n", " 'KSM',\n", " 'ABI MS Promo Uplift - rel',\n", " 'ABI Base W_Distribution',\n", " 'ABI Base Num_Distribution',\n", " 'ABI_Promo_W_W_Distribution',\n", " 'ABI_Promo_W_Num_Distribution',\n", " 'ABI vs Segment PTC Index Agg',\n", " 'ABI_Duration_Days']"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the dataset\n", "print(\"Loading dataset...\")\n", "df = pd.read_csv('clean_new_test_output_ads_v3.csv')\n", "print(f\"Dataset shape: {df.shape}\")\n", "df.drop(columns=['Unnamed: 0'], inplace=True)\n", "df.columns.tolist()\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Available features: 11\n", "Missing features: 0\n", "Initial shapes - X: (762, 11), y: (762,)\n", "\n", "Clubbing timing categories...\n", "Before category distribution:\n", "Before\n", "0    483\n", "1    279\n", "Name: count, dtype: int64\n", "\n", "After category distribution:\n", "After\n", "0    459\n", "1    303\n", "Name: count, dtype: int64\n", "\n", "Final feature columns after clubbing: ['ABI_Duration_Days', 'ABI Mechanic', 'Same Week', 'Avg Temp', 'KSM', 'ABI vs Segment PTC Index Agg', 'ABI Coverage', 'Before', 'After']\n", "Final shapes - X: (762, 9), y: (762,)\n"]}], "source": ["# Define the target variable and features\n", "target_variable = 'ABI MS Promo Uplift - rel'\n", "\n", "requested_features = [\n", "    'ABI_Duration_Days',\n", "    'ABI Mechanic',\n", "    'Same Week',\n", "    '1 wk after',\n", "    '2 wk after',\n", "    '1 wk before',\n", "    '2 wk before',\n", "    'Avg Temp',\n", "    'KSM',\n", "    'ABI vs Segment PTC Index Agg',\n", "    'ABI Coverage'\n", "]\n", "\n", "# Check feature availability\n", "available_features = [f for f in requested_features if f in df.columns]\n", "missing_features = [f for f in requested_features if f not in df.columns]\n", "\n", "print(f\"Available features: {len(available_features)}\")\n", "print(f\"Missing features: {len(missing_features)}\")\n", "\n", "if missing_features:\n", "    print(f\"Missing: {missing_features}\")\n", "\n", "# Prepare data\n", "feature_columns = available_features\n", "X = df[feature_columns].copy()\n", "y = df[target_variable].copy()\n", "\n", "print(f\"Initial shapes - X: {X.shape}, y: {y.shape}\")\n", "\n", "# Club timing categories together\n", "print(\"\\nClubbing timing categories...\")\n", "\n", "# Create combined \"Before\" column (1 wk before OR 2 wk before)\n", "X['Before'] = ((X['1 wk before'] == 1) | (X['2 wk before'] == 1)).astype(int)\n", "\n", "# Create combined \"After\" column (1 wk after OR 2 wk after)\n", "X['After'] = ((X['1 wk after'] == 1) | (X['2 wk after'] == 1)).astype(int)\n", "\n", "print(f\"Before category distribution:\")\n", "print(X['Before'].value_counts())\n", "print(f\"\\nAfter category distribution:\")\n", "print(X['After'].value_counts())\n", "\n", "# Remove individual timing columns and update feature list\n", "columns_to_remove = ['1 wk before', '2 wk before', '1 wk after', '2 wk after']\n", "X = X.drop(columns=columns_to_remove)\n", "\n", "# Update feature columns list\n", "feature_columns = X.columns.tolist()\n", "print(f\"\\nFinal feature columns after clubbing: {feature_columns}\")\n", "print(f\"Final shapes - X: {X.shape}, y: {y.shape}\")\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values analysis:\n", "ABI vs Segment PTC Index Agg    49\n", "dtype: int64\n", "\n", "Categorical columns: ['ABI Mechanic', 'Same Week', 'Before', 'After']\n", "Numeric columns: ['ABI_Duration_Days', 'Avg Temp', 'KSM', 'ABI vs Segment PTC Index Agg', 'ABI Coverage']\n", "\n", "Numeric missing values: {'ABI vs Segment PTC Index Agg': 49}\n", "Numeric missing values filled with median.\n", "\n", "Applying dummy encoding to categorical variables...\n", "Unique values in ABI Mechanic: ['FID' 'Immediate' 'LV' 'No NIP']\n", "Using 'No NIP' as base category for ABI Mechanic\n", "Unique values in Same Week: [ True False]\n", "'No NIP' not found in Same Week, using first category as base\n", "Unique values in Before: [1 0]\n", "'No NIP' not found in Before, using first category as base\n", "Unique values in After: [1 0]\n", "'No NIP' not found in After, using first category as base\n", "Dummy encoding completed. New shape: (762, 11)\n", "Final feature columns: ['ABI_Duration_Days', 'Avg Temp', 'KSM', 'ABI vs Segment PTC Index Agg', 'ABI Coverage', 'ABI Mechanic_FID', 'ABI Mechanic_Immediate', 'ABI Mechanic_LV', 'Same Week_True', 'Before_1', 'After_1']\n", "\n", "Target variable statistics:\n", "count    762.000000\n", "mean       3.282874\n", "std        3.957150\n", "min        0.635801\n", "25%        1.642080\n", "50%        2.512538\n", "75%        3.721450\n", "max       68.992889\n", "Name: ABI MS Promo Uplift - rel, dtype: float64\n", "\n", "Training samples: 685\n", "Test samples: 77\n", "Data preparation completed!\n"]}], "source": ["# Handle missing values and prepare data\n", "print(\"Missing values analysis:\")\n", "missing_info = X.isnull().sum()\n", "print(missing_info[missing_info > 0] if missing_info.sum() > 0 else \"No missing values found.\")\n", "\n", "# Explicitly define categorical and numeric columns\n", "# Categorical columns include timing indicators and mechanic type\n", "categorical_columns = [\n", "    'ABI Mechanic',\n", "    'Same Week', \n", "    'Before',    # Clubbed: 1 wk before + 2 wk before\n", "    'After'      # Clubbed: 1 wk after + 2 wk after\n", "]\n", "\n", "# Filter to only include categorical columns that exist in the dataset\n", "categorical_columns = [col for col in categorical_columns if col in X.columns]\n", "\n", "# Numeric columns are all the remaining columns\n", "numeric_columns = [col for col in X.columns if col not in categorical_columns]\n", "\n", "print(f\"\\nCategorical columns: {categorical_columns}\")\n", "print(f\"Numeric columns: {numeric_columns}\")\n", "\n", "# Handle missing values separately for categorical and numeric columns\n", "X_processed = X.copy()\n", "\n", "# For numeric columns: fill with median\n", "if len(numeric_columns) > 0:\n", "    numeric_missing = X_processed[numeric_columns].isnull().sum()\n", "    if numeric_missing.sum() > 0:\n", "        print(f\"\\nNumeric missing values: {numeric_missing[numeric_missing > 0].to_dict()}\")\n", "        X_processed[numeric_columns] = X_processed[numeric_columns].fillna(X_processed[numeric_columns].median())\n", "        print(\"Numeric missing values filled with median.\")\n", "\n", "# For categorical columns: fill with mode (most frequent value)\n", "if len(categorical_columns) > 0:\n", "    categorical_missing = X_processed[categorical_columns].isnull().sum()\n", "    if categorical_missing.sum() > 0:\n", "        print(f\"\\nCategorical missing values: {categorical_missing[categorical_missing > 0].to_dict()}\")\n", "        for col in categorical_columns:\n", "            if X_processed[col].isnull().sum() > 0:\n", "                mode_value = X_processed[col].mode()[0] if len(X_processed[col].mode()) > 0 else 'Unknown'\n", "                X_processed[col] = X_processed[col].fillna(mode_value)\n", "        print(\"Categorical missing values filled with mode.\")\n", "\n", "# Apply dummy encoding to categorical variables with 'No NIP' as base category\n", "if len(categorical_columns) > 0:\n", "    print(f\"\\nApplying dummy encoding to categorical variables...\")\n", "    for col in categorical_columns:\n", "        print(f\"Unique values in {col}: {X_processed[col].unique()}\")\n", "        \n", "        # Create dummy variables, dropping 'No NIP' as base category\n", "        dummy_vars = pd.get_dummies(X_processed[col], prefix=col, drop_first=False)\n", "        \n", "        # If 'No NIP' exists, drop it to use as base category\n", "        no_nip_col = f\"{col}_No NIP\"\n", "        if no_nip_col in dummy_vars.columns:\n", "            dummy_vars = dummy_vars.drop(no_nip_col, axis=1)\n", "            print(f\"Using 'No NIP' as base category for {col}\")\n", "        else:\n", "            # If 'No NIP' doesn't exist, drop the first category\n", "            dummy_vars = dummy_vars.iloc[:, 1:]\n", "            print(f\"'No NIP' not found in {col}, using first category as base\")\n", "        \n", "        # Add dummy variables to the dataset\n", "        X_processed = pd.concat([X_processed, dummy_vars], axis=1)\n", "    \n", "    # Remove original categorical columns\n", "    X_processed = X_processed.drop(categorical_columns, axis=1)\n", "    print(f\"Dummy encoding completed. New shape: {X_processed.shape}\")\n", "\n", "# Update feature columns list\n", "feature_columns = X_processed.columns.tolist()\n", "print(f\"Final feature columns: {feature_columns}\")\n", "\n", "# Basic statistics\n", "print(\"\\nTarget variable statistics:\")\n", "print(y.describe())\n", "\n", "# Split and scale data\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X_processed, y, test_size=0.1, random_state=42\n", ")\n", "\n", "print(f\"\\nTraining samples: {len(X_train)}\")\n", "print(f\"Test samples: {len(X_test)}\")\n", "\n", "# Scale features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# Storage for results\n", "models = {}\n", "results_summary = []\n", "\n", "print(\"Data preparation completed!\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 2. Linear Regression (Baseline Model)\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "LINEAR REGRESSION (BASELINE)\n", "============================================================\n", "Training R²: 0.1368\n", "Test R²: -0.0657\n", "Training RMSE: 2.5413\n", "Test RMSE: 9.5811\n", "Cross-validation R² (mean ± std): 0.0727 ± 0.0969\n"]}], "source": ["print(\"=\"*60)\n", "print(\"LINEAR REGRESSION (BASELINE)\")\n", "print(\"=\"*60)\n", "\n", "# Train Linear Regression\n", "lr_model = LinearRegression()\n", "lr_model.fit(X_train_scaled, y_train)\n", "\n", "# Predictions\n", "y_train_pred_lr = lr_model.predict(X_train_scaled)\n", "y_test_pred_lr = lr_model.predict(X_test_scaled)\n", "\n", "# Performance metrics\n", "train_r2_lr = r2_score(y_train, y_train_pred_lr)\n", "test_r2_lr = r2_score(y_test, y_test_pred_lr)\n", "train_rmse_lr = np.sqrt(mean_squared_error(y_train, y_train_pred_lr))\n", "test_rmse_lr = np.sqrt(mean_squared_error(y_test, y_test_pred_lr))\n", "cv_scores_lr = cross_val_score(lr_model, X_train_scaled, y_train, cv=5, scoring='r2')\n", "\n", "print(f\"Training R²: {train_r2_lr:.4f}\")\n", "print(f\"Test R²: {test_r2_lr:.4f}\")\n", "print(f\"Training RMSE: {train_rmse_lr:.4f}\")\n", "print(f\"Test RMSE: {test_rmse_lr:.4f}\")\n", "print(f\"Cross-validation R² (mean ± std): {cv_scores_lr.mean():.4f} ± {cv_scores_lr.std():.4f}\")\n", "\n", "# Store results\n", "models['Linear Regression'] = {\n", "    'model': lr_model,\n", "    'predictions_train': y_train_pred_lr,\n", "    'predictions_test': y_test_pred_lr\n", "}\n", "\n", "results_summary.append({\n", "    'Method': 'Linear Regression',\n", "    'Train R²': train_r2_lr,\n", "    'Test R²': test_r2_lr,\n", "    'Train RMSE': train_rmse_lr,\n", "    'Test RMSE': test_rmse_lr,\n", "    'CV R² Mean': cv_scores_lr.mean(),\n", "    'CV R² Std': cv_scores_lr.std()\n", "})\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Linear Regression - OLS Statistical Analysis\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OLS STATISTICAL ANALYSIS - LINEAR REGRESSION:\n", "                                OLS Regression Results                               \n", "=====================================================================================\n", "Dep. Variable:     ABI MS Promo Uplift - rel   R-squared:                       0.137\n", "Model:                                   OLS   Adj. R-squared:                  0.123\n", "Method:                        Least Squares   F-statistic:                     9.692\n", "Date:                       Tue, 01 Jul 2025   Prob (F-statistic):           2.12e-16\n", "Time:                               16:39:03   Log-Likelihood:                -1610.9\n", "No. Observations:                        685   AIC:                             3246.\n", "Df Residuals:                            673   BIC:                             3300.\n", "Df Model:                                 11                                         \n", "Covariance Type:                   nonrobust                                         \n", "================================================================================================\n", "                                   coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------------------------\n", "const                            3.1296      0.098     31.947      0.000       2.937       3.322\n", "ABI_Duration_Days               -0.4839      0.120     -4.044      0.000      -0.719      -0.249\n", "Avg Temp                         0.1575      0.102      1.547      0.122      -0.042       0.358\n", "KSM                              0.0157      0.102      0.153      0.878      -0.185       0.217\n", "ABI vs Segment PTC Index Agg    -0.0941      0.103     -0.915      0.361      -0.296       0.108\n", "ABI Coverage                     0.6579      0.101      6.507      0.000       0.459       0.856\n", "ABI Mechanic_FID                 0.4138      0.162      2.548      0.011       0.095       0.733\n", "ABI Mechanic_Immediate           0.5144      0.178      2.890      0.004       0.165       0.864\n", "ABI Mechanic_LV                  0.6209      0.201      3.087      0.002       0.226       1.016\n", "Same Week_True                   0.3386      0.110      3.065      0.002       0.122       0.556\n", "Before_1                        -0.1027      0.105     -0.980      0.328      -0.308       0.103\n", "After_1                          0.0695      0.104      0.667      0.505      -0.135       0.274\n", "==============================================================================\n", "Omnibus:                      600.817   <PERSON><PERSON><PERSON>-Watson:                   1.902\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            19330.066\n", "Skew:                           3.819   Prob(JB):                         0.00\n", "Kurtosis:                      27.878   Cond. No.                         4.16\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\n", "Linear Regression Feature Importance:\n", "                         Feature  Coefficient  Abs_Coefficient\n", "4                   ABI Coverage     0.657869         0.657869\n", "7                ABI Mechanic_LV     0.620892         0.620892\n", "6         ABI Mechanic_Immediate     0.514405         0.514405\n", "0              ABI_Duration_Days    -0.483916         0.483916\n", "5               ABI Mechanic_FID     0.413802         0.413802\n", "8                 Same Week_True     0.338595         0.338595\n", "1                       Avg Temp     0.157534         0.157534\n", "9                       Before_1    -0.102678         0.102678\n", "3   ABI vs Segment PTC Index Agg    -0.094081         0.094081\n", "10                       After_1     0.069518         0.069518\n", "2                            KSM     0.015701         0.015701\n", "\n", "OLS Coefficient Mapping:\n", "                    Feature_Name  Coefficient  P_Value Significant\n", "0                      Intercept       3.1296   0.0000         Yes\n", "1              ABI_Duration_Days      -0.4839   0.0001         Yes\n", "2                       Avg Temp       0.1575   0.1224          No\n", "3                            KSM       0.0157   0.8782          No\n", "4   ABI vs Segment PTC Index Agg      -0.0941   0.3607          No\n", "5                   ABI Coverage       0.6579   0.0000         Yes\n", "6               ABI Mechanic_FID       0.4138   0.0111         Yes\n", "7         ABI Mechanic_Immediate       0.5144   0.0040         Yes\n", "8                ABI Mechanic_LV       0.6209   0.0021         Yes\n", "9                 Same Week_True       0.3386   0.0023         Yes\n", "10                      Before_1      -0.1027   0.3276          No\n", "11                       After_1       0.0695   0.5049          No\n"]}], "source": ["# OLS Analysis for Linear Regression\n", "print(\"OLS STATISTICAL ANALYSIS - LINEAR REGRESSION:\")\n", "\n", "# Create DataFrame with proper feature names for OLS\n", "# Reset index to ensure alignment between y_train and X_train_df\n", "X_train_df = pd.DataFrame(X_train_scaled, columns=feature_columns, index=y_train.index)\n", "X_train_sm = sm.add_constant(X_train_df)\n", "ols_model_lr = sm.OLS(y_train, X_train_sm).fit()\n", "print(ols_model_lr.summary())\n", "\n", "# Feature importance\n", "feature_importance_lr = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Coefficient': lr_model.coef_,\n", "    'Abs_Coefficient': np.abs(lr_model.coef_)\n", "}).sort_values('Abs_Coefficient', ascending=False)\n", "\n", "print(\"\\nLinear Regression Feature Importance:\")\n", "print(feature_importance_lr)\n", "\n", "# Create a mapping table to show which features correspond to which coefficients\n", "print(\"\\nOLS Coefficient Mapping:\")\n", "coef_mapping = pd.DataFrame({\n", "    'Feature_Name': ['Intercept'] + list(feature_columns),\n", "    'Coefficient': [ols_model_lr.params[0]] + list(ols_model_lr.params[1:]),\n", "    'P_Value': [ols_model_lr.pvalues[0]] + list(ols_model_lr.pvalues[1:]),\n", "    'Significant': ['Yes' if p < 0.05 else 'No' for p in ([ols_model_lr.pvalues[0]] + list(ols_model_lr.pvalues[1:]))]\n", "}).round(4)\n", "print(coef_mapping)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 3. <PERSON> Regression (L2 Regularization)\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "RIDGE REGRESSION (L2 REGULARIZATION)\n", "============================================================\n", "Best Ridge alpha: 112.8838\n", "Training R²: 0.1304\n", "Test R²: -0.0507\n", "Training RMSE: 2.5507\n", "Test RMSE: 9.5135\n", "Cross-validation R² (mean ± std): 0.0808 ± 0.0795\n"]}], "source": ["print(\"=\"*60)\n", "print(\"RIDGE REGRESSION (L2 REGULARIZATION)\")\n", "print(\"=\"*60)\n", "\n", "# Hyperparameter tuning for Ridge\n", "ridge_alphas = np.logspace(-3, 3, 20)\n", "ridge_grid = GridSearchCV(Ridge(), {'alpha': ridge_alphas}, cv=5, scoring='r2')\n", "ridge_grid.fit(X_train_scaled, y_train)\n", "best_alpha_ridge = ridge_grid.best_params_['alpha']\n", "\n", "print(f\"Best Ridge alpha: {best_alpha_ridge:.4f}\")\n", "\n", "# Train Ridge model\n", "ridge_model = Ridge(alpha=best_alpha_ridge)\n", "ridge_model.fit(X_train_scaled, y_train)\n", "\n", "# Predictions\n", "y_train_pred_ridge = ridge_model.predict(X_train_scaled)\n", "y_test_pred_ridge = ridge_model.predict(X_test_scaled)\n", "\n", "# Performance metrics\n", "train_r2_ridge = r2_score(y_train, y_train_pred_ridge)\n", "test_r2_ridge = r2_score(y_test, y_test_pred_ridge)\n", "train_rmse_ridge = np.sqrt(mean_squared_error(y_train, y_train_pred_ridge))\n", "test_rmse_ridge = np.sqrt(mean_squared_error(y_test, y_test_pred_ridge))\n", "cv_scores_ridge = cross_val_score(ridge_model, X_train_scaled, y_train, cv=5, scoring='r2')\n", "\n", "print(f\"Training R²: {train_r2_ridge:.4f}\")\n", "print(f\"Test R²: {test_r2_ridge:.4f}\")\n", "print(f\"Training RMSE: {train_rmse_ridge:.4f}\")\n", "print(f\"Test RMSE: {test_rmse_ridge:.4f}\")\n", "print(f\"Cross-validation R² (mean ± std): {cv_scores_ridge.mean():.4f} ± {cv_scores_ridge.std():.4f}\")\n", "\n", "# Store results\n", "models['Ridge Regression'] = {\n", "    'model': ridge_model,\n", "    'predictions_train': y_train_pred_ridge,\n", "    'predictions_test': y_test_pred_ridge,\n", "    'alpha': best_alpha_ridge\n", "}\n", "\n", "results_summary.append({\n", "    'Method': 'Ridge Regression',\n", "    'Train R²': train_r2_ridge,\n", "    'Test R²': test_r2_ridge,\n", "    'Train RMSE': train_rmse_ridge,\n", "    'Test RMSE': test_rmse_ridge,\n", "    'CV R² Mean': cv_scores_ridge.mean(),\n", "    'CV R² Std': cv_scores_ridge.std()\n", "})\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RIDGE REGRESSION COEFFICIENTS ANALYSIS:\n", "                         Feature  Coefficient  Abs_Coefficient\n", "4                   ABI Coverage     0.558054         0.558054\n", "0              ABI_Duration_Days    -0.457846         0.457846\n", "8                 Same Week_True     0.294072         0.294072\n", "7                ABI Mechanic_LV     0.280654         0.280654\n", "6         ABI Mechanic_Immediate     0.235545         0.235545\n", "5               ABI Mechanic_FID     0.180960         0.180960\n", "1                       Avg Temp     0.144179         0.144179\n", "9                       Before_1    -0.078682         0.078682\n", "10                       After_1     0.063084         0.063084\n", "3   ABI vs Segment PTC Index Agg    -0.060906         0.060906\n", "2                            KSM     0.002005         0.002005\n"]}], "source": ["# Ridge coefficient analysis\n", "print(\"RIDGE REGRESSION COEFFICIENTS ANALYSIS:\")\n", "ridge_coef_df = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Coefficient': ridge_model.coef_,\n", "    'Abs_Coefficient': np.abs(ridge_model.coef_)\n", "}).sort_values('Abs_Coefficient', ascending=False)\n", "\n", "print(ridge_coef_df)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 4. <PERSON><PERSON> Regression (L1 Regularization)\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "LASSO REGRESSION (L1 REGULARIZATION)\n", "============================================================\n", "Best Lasso alpha: 0.0785\n", "Training R²: 0.1197\n", "Test R²: -0.0412\n", "Training RMSE: 2.5664\n", "Test RMSE: 9.4701\n", "Cross-validation R² (mean ± std): 0.0776 ± 0.0784\n"]}], "source": ["print(\"=\"*60)\n", "print(\"LASSO REGRESSION (L1 REGULARIZATION)\")\n", "print(\"=\"*60)\n", "\n", "# Hyperparameter tuning for <PERSON><PERSON>\n", "lasso_alphas = np.logspace(-3, 1, 20)\n", "lasso_grid = GridSearchCV(Lasso(max_iter=2000), {'alpha': lasso_alphas}, cv=5, scoring='r2')\n", "lasso_grid.fit(X_train_scaled, y_train)\n", "best_alpha_lasso = lasso_grid.best_params_['alpha']\n", "\n", "print(f\"Best Lasso alpha: {best_alpha_lasso:.4f}\")\n", "\n", "# Train Lasso model\n", "lasso_model = Lasso(alpha=best_alpha_lasso, max_iter=2000)\n", "lasso_model.fit(X_train_scaled, y_train)\n", "\n", "# Predictions\n", "y_train_pred_lasso = lasso_model.predict(X_train_scaled)\n", "y_test_pred_lasso = lasso_model.predict(X_test_scaled)\n", "\n", "# Performance metrics\n", "train_r2_lasso = r2_score(y_train, y_train_pred_lasso)\n", "test_r2_lasso = r2_score(y_test, y_test_pred_lasso)\n", "train_rmse_lasso = np.sqrt(mean_squared_error(y_train, y_train_pred_lasso))\n", "test_rmse_lasso = np.sqrt(mean_squared_error(y_test, y_test_pred_lasso))\n", "cv_scores_lasso = cross_val_score(lasso_model, X_train_scaled, y_train, cv=5, scoring='r2')\n", "\n", "print(f\"Training R²: {train_r2_lasso:.4f}\")\n", "print(f\"Test R²: {test_r2_lasso:.4f}\")\n", "print(f\"Training RMSE: {train_rmse_lasso:.4f}\")\n", "print(f\"Test RMSE: {test_rmse_lasso:.4f}\")\n", "print(f\"Cross-validation R² (mean ± std): {cv_scores_lasso.mean():.4f} ± {cv_scores_lasso.std():.4f}\")\n", "\n", "# Store results\n", "models['Lasso Regression'] = {\n", "    'model': lasso_model,\n", "    'predictions_train': y_train_pred_lasso,\n", "    'predictions_test': y_test_pred_lasso,\n", "    'alpha': best_alpha_lasso\n", "}\n", "\n", "results_summary.append({\n", "    'Method': 'Lasso Regression',\n", "    'Train R²': train_r2_lasso,\n", "    'Test R²': test_r2_lasso,\n", "    'Train RMSE': train_rmse_lasso,\n", "    'Test RMSE': test_rmse_lasso,\n", "    'CV R² Mean': cv_scores_lasso.mean(),\n", "    'CV R² Std': cv_scores_lasso.std()\n", "})\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LASSO FEATURE SELECTION ANALYSIS:\n", "Features selected by <PERSON><PERSON>: 6 out of 11\n", "\n", "Selected features:\n", "             Feature  Coefficient  Abs_Coefficient\n", "4       ABI Coverage     0.581276         0.581276\n", "0  ABI_Duration_Days    -0.534092         0.534092\n", "8     Same Week_True     0.246423         0.246423\n", "1           Avg Temp     0.084746         0.084746\n", "7    ABI Mechanic_LV     0.020675         0.020675\n", "9           Before_1    -0.015629         0.015629\n", "\n", "Features eliminated by <PERSON><PERSON>: 5\n", "['KSM', 'ABI vs Segment PTC Index Agg', 'ABI Mechanic_FID', 'ABI Mechanic_Immediate', 'After_1']\n"]}], "source": ["# Lasso feature selection analysis\n", "print(\"LASSO FEATURE SELECTION ANALYSIS:\")\n", "lasso_coef_df = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Coefficient': lasso_model.coef_,\n", "    'Abs_Coefficient': np.abs(lasso_model.coef_)\n", "}).sort_values('Abs_Coefficient', ascending=False)\n", "\n", "selected_features = lasso_coef_df[lasso_coef_df['Coefficient'] != 0]\n", "eliminated_features = lasso_coef_df[lasso_coef_df['Coefficient'] == 0]\n", "\n", "print(f\"Features selected by <PERSON><PERSON>: {len(selected_features)} out of {len(feature_columns)}\")\n", "print(\"\\nSelected features:\")\n", "print(selected_features)\n", "\n", "if len(eliminated_features) > 0:\n", "    print(f\"\\nFeatures eliminated by <PERSON><PERSON>: {len(eliminated_features)}\")\n", "    print(eliminated_features['Feature'].tolist())\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 5. Model Comparison Summary\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "MODEL COMPARISON SUMMARY\n", "============================================================\n", "              Method  Train R²  Test R²  Train RMSE  Test RMSE  CV R² Mean  \\\n", "0  Linear Regression    0.1368  -0.0657      2.5413     9.5811      0.0727   \n", "1   Ridge Regression    0.1304  -0.0507      2.5507     9.5135      0.0808   \n", "2   Lasso Regression    0.1197  -0.0412      2.5664     9.4701      0.0776   \n", "\n", "   CV R² Std  \n", "0     0.0969  \n", "1     0.0795  \n", "2     0.0784  \n", "\n", "Best performing model (Test R²): Lasso Regression\n", "\n", "Overfitting Analysis (Train R² - Test R²):\n", "Linear Regression: 0.2025\n", "Ridge Regression: 0.1811\n", "Lasso Regression: 0.1608\n", "\n", "Note: Lower overfitting score indicates better generalization\n"]}], "source": ["# Summary comparison\n", "print(\"=\"*60)\n", "print(\"MODEL COMPARISON SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "comparison_df = pd.DataFrame(results_summary)\n", "print(comparison_df.round(4))\n", "\n", "# Best model identification\n", "best_test_r2 = max([r['Test R²'] for r in results_summary])\n", "best_model_name = [r['Method'] for r in results_summary if r['Test R²'] == best_test_r2][0]\n", "print(f\"\\nBest performing model (Test R²): {best_model_name}\")\n", "\n", "# Overfitting analysis\n", "print(\"\\nOverfitting Analysis (Train R² - Test R²):\")\n", "for result in results_summary:\n", "    method = result['Method']\n", "    train_r2 = result['Train R²']\n", "    test_r2 = result['Test R²']\n", "    overfitting_score = train_r2 - test_r2\n", "    print(f\"{method}: {overfitting_score:.4f}\")\n", "    \n", "print(\"\\nNote: Lower overfitting score indicates better generalization\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 6. Comprehensive Visualizations\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2000x1500 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Model comparison visualization\n", "plt.figure(figsize=(20, 15))\n", "\n", "# Actual vs Predicted for all models\n", "for i, (model_name, model_data) in enumerate(models.items()):\n", "    # Training set\n", "    plt.subplot(3, 3, i*3 + 1)\n", "    plt.scatter(y_train, model_data['predictions_train'], alpha=0.6, s=50, color='blue')\n", "    plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)\n", "    plt.xlabel('Actual')\n", "    plt.ylabel('Predicted')\n", "    train_r2 = r2_score(y_train, model_data['predictions_train'])\n", "    plt.title(f'{model_name} - Training\\nR² = {train_r2:.3f}')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Test set\n", "    plt.subplot(3, 3, i*3 + 2)\n", "    plt.scatter(y_test, model_data['predictions_test'], alpha=0.6, s=50, color='green')\n", "    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "    plt.xlabel('Actual')\n", "    plt.ylabel('Predicted')\n", "    test_r2 = r2_score(y_test, model_data['predictions_test'])\n", "    plt.title(f'{model_name} - Test\\nR² = {test_r2:.3f}')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Residuals\n", "    plt.subplot(3, 3, i*3 + 3)\n", "    residuals = y_test - model_data['predictions_test']\n", "    plt.hist(residuals, bins=20, alpha=0.7, edgecolor='black')\n", "    plt.xlabel('Residuals')\n", "    plt.ylabel('Frequency')\n", "    plt.title(f'{model_name} - Residuals')\n", "    plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('updated_models_comparison.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Performance comparison charts\n", "plt.figure(figsize=(15, 10))\n", "\n", "methods = [r['Method'] for r in results_summary]\n", "train_r2s = [r['Train R²'] for r in results_summary]\n", "test_r2s = [r['Test R²'] for r in results_summary]\n", "cv_means = [r['CV R² Mean'] for r in results_summary]\n", "\n", "x = np.arange(len(methods))\n", "width = 0.25\n", "\n", "# R² comparison\n", "plt.subplot(2, 2, 1)\n", "plt.bar(x - width, train_r2s, width, label='Train R²', alpha=0.8)\n", "plt.bar(x, test_r2s, width, label='Test R²', alpha=0.8)\n", "plt.bar(x + width, cv_means, width, label='CV R² Mean', alpha=0.8)\n", "plt.xlabel('Method')\n", "plt.ylabel('R² Score')\n", "plt.title('R² Comparison Across Methods')\n", "plt.xticks(x, methods, rotation=45)\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# RMSE comparison\n", "train_rmses = [r['Train RMSE'] for r in results_summary]\n", "test_rmses = [r['Test RMSE'] for r in results_summary]\n", "\n", "plt.subplot(2, 2, 2)\n", "plt.bar(x - width/2, train_rmses, width, label='Train RMSE', alpha=0.8)\n", "plt.bar(x + width/2, test_rmses, width, label='Test RMSE', alpha=0.8)\n", "plt.xlabel('Method')\n", "plt.ylabel('RMSE')\n", "plt.title('RMSE Comparison Across Methods')\n", "plt.xticks(x, methods, rotation=45)\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Cross-validation with error bars\n", "plt.subplot(2, 2, 3)\n", "cv_stds = [r['CV R² Std'] for r in results_summary]\n", "plt.bar(x, cv_means, yerr=cv_stds, capsize=5, alpha=0.8)\n", "plt.xlabel('Method')\n", "plt.ylabel('CV R² Score')\n", "plt.title('Cross-Validation R² (with std)')\n", "plt.xticks(x, methods, rotation=45)\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Overfitting comparison\n", "plt.subplot(2, 2, 4)\n", "overfitting_scores = [r['Train R²'] - r['Test R²'] for r in results_summary]\n", "colors = ['red' if score > 0.1 else 'orange' if score > 0.05 else 'green' for score in overfitting_scores]\n", "plt.bar(x, overfitting_scores, color=colors, alpha=0.8)\n", "plt.xlabel('Method')\n", "plt.ylabel('Overfitting Score (Train R² - Test R²)')\n", "plt.title('Overfitting Analysis')\n", "plt.xticks(x, methods, rotation=45)\n", "plt.axhline(y=0.1, color='red', linestyle='--', alpha=0.5, label='High overfitting')\n", "plt.axhline(y=0.05, color='orange', linestyle='--', alpha=0.5, label='Moderate overfitting')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('updated_performance_comparison.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 7. Feature Importance Comparison\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"image/png": "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***********************************************************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", "text/plain": ["<Figure size 1500x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Coefficient Comparison:\n", "                         Feature  Linear   Ridge   Lasso\n", "0              ABI_Duration_Days -0.4839 -0.4578 -0.5341\n", "1                       Avg Temp  0.1575  0.1442  0.0847\n", "2                            KSM  0.0157  0.0020  0.0000\n", "3   ABI vs Segment PTC Index Agg -0.0941 -0.0609 -0.0000\n", "4                   ABI Coverage  0.6579  0.5581  0.5813\n", "5               ABI Mechanic_FID  0.4138  0.1810  0.0000\n", "6         ABI Mechanic_Immediate  0.5144  0.2355  0.0000\n", "7                ABI Mechanic_LV  0.6209  0.2807  0.0207\n", "8                 Same Week_True  0.3386  0.2941  0.2464\n", "9                       Before_1 -0.1027 -0.0787 -0.0156\n", "10                       After_1  0.0695  0.0631  0.0000\n"]}], "source": ["# Feature coefficients comparison\n", "plt.figure(figsize=(15, 8))\n", "\n", "# Create coefficient comparison dataframe\n", "coef_comparison = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Linear': lr_model.coef_,\n", "    'Ridge': ridge_model.coef_,\n", "    'Lasso': lasso_model.coef_\n", "})\n", "\n", "# Plot coefficients for each method\n", "x_pos = np.arange(len(feature_columns))\n", "width = 0.25\n", "\n", "plt.bar(x_pos - width, coef_comparison['Linear'], width, label='Linear Regression', alpha=0.8)\n", "plt.bar(x_pos, coef_comparison['Ridge'], width, label='Ridge Regression', alpha=0.8)\n", "plt.bar(x_pos + width, coef_comparison['Lasso'], width, label='Lasso Regression', alpha=0.8)\n", "\n", "plt.xlabel('Features')\n", "plt.ylabel('Coefficient Value')\n", "plt.title('Feature Coefficients Comparison Across Methods')\n", "plt.xticks(x_pos, feature_columns, rotation=45, ha='right')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('updated_feature_coefficients.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"\\nCoefficient Comparison:\")\n", "print(coef_comparison.round(4))\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}