{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# Linear Regression Analysis with Regularization Options - UNCLUBBED\n", "\n", "## Overview\n", "This notebook compares three regression approaches to reduce overfitting:\n", "- **Linear Regression** (Baseline)\n", "- **Ridge Regression** (L2 Regularization)\n", "- **Lasso Regression** (L1 Regularization)\n", "\n", "**Target Variable:** ABI MS Promo Uplift - rel\n", "Removed Weighted Distribution, replaced with Numeric Dist\n", "\n", "**Features:**\n", "    'ABI_Duration_Days',\n", "    'ABI Mechanic',\n", "    'Same Week',\n", "    'Before',\n", "    'After',\n", "    'Avg Temp',\n", "    'KSM',\n", "    'ABI vs Segment PTC Index Agg',\n", "    'ABI_Promo_W_Num_Distribution'\n", "\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 1. Data Loading and Preparation\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.linear_model import LinearRegression, Lasso, Ridge\n", "from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV\n", "from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error\n", "from sklearn.preprocessing import StandardScaler\n", "import statsmodels.api as sm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully\")\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading dataset...\n", "Dataset shape: (762, 21)\n"]}, {"data": {"text/plain": ["['ABI Start',\n", " 'ABI End',\n", " 'ABI Coverage',\n", " 'ABI Mechanic',\n", " 'ABI Rounded',\n", " 'Overlapping',\n", " 'Same Week',\n", " '1 wk after',\n", " '2 wk after',\n", " '1 wk before',\n", " '2 wk before',\n", " 'Avg Temp',\n", " 'KSM',\n", " 'ABI MS Promo Uplift - rel',\n", " 'ABI Base W_Distribution',\n", " 'ABI Base Num_Distribution',\n", " 'ABI_Promo_W_W_Distribution',\n", " 'ABI_Promo_W_Num_Distribution',\n", " 'ABI vs Segment PTC Index Agg',\n", " 'ABI_Duration_Days']"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the dataset\n", "print(\"Loading dataset...\")\n", "df = pd.read_csv('clean_new_test_output_ads_v3.csv')\n", "print(f\"Dataset shape: {df.shape}\")\n", "df.drop(columns=['Unnamed: 0'], inplace=True)\n", "df.columns.tolist()\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Available features: 11\n", "Missing features: 0\n", "Initial shapes - X: (762, 11), y: (762,)\n", "\n", "Clubbing timing categories...\n", "Before category distribution:\n", "Before\n", "0    483\n", "1    279\n", "Name: count, dtype: int64\n", "\n", "After category distribution:\n", "After\n", "0    459\n", "1    303\n", "Name: count, dtype: int64\n", "\n", "Final feature columns after clubbing: ['ABI_Duration_Days', 'ABI Mechanic', 'Same Week', 'Avg Temp', 'KSM', 'ABI vs Segment PTC Index Agg', 'ABI_Promo_W_Num_Distribution', 'Before', 'After']\n", "Final shapes - X: (762, 9), y: (762,)\n"]}], "source": ["# Define the target variable and features\n", "target_variable = 'ABI MS Promo Uplift - rel'\n", "\n", "requested_features = [\n", "    'ABI_Duration_Days',\n", "    'ABI Mechanic',\n", "    'Same Week',\n", "    '1 wk after',\n", "    '2 wk after',\n", "    '1 wk before',\n", "    '2 wk before',\n", "    'Avg Temp',\n", "    'KSM',\n", "    'ABI vs Segment PTC Index Agg',\n", "    'ABI_Promo_W_Num_Distribution'\n", "]\n", "\n", "# Check feature availability\n", "available_features = [f for f in requested_features if f in df.columns]\n", "missing_features = [f for f in requested_features if f not in df.columns]\n", "\n", "print(f\"Available features: {len(available_features)}\")\n", "print(f\"Missing features: {len(missing_features)}\")\n", "\n", "if missing_features:\n", "    print(f\"Missing: {missing_features}\")\n", "\n", "# Prepare data\n", "feature_columns = available_features\n", "X = df[feature_columns].copy()\n", "y = df[target_variable].copy()\n", "\n", "print(f\"Initial shapes - X: {X.shape}, y: {y.shape}\")\n", "\n", "# Club timing categories together\n", "print(\"\\nClubbing timing categories...\")\n", "\n", "# Create combined \"Before\" column (1 wk before OR 2 wk before)\n", "X['Before'] = ((X['1 wk before'] == 1) | (X['2 wk before'] == 1)).astype(int)\n", "\n", "# Create combined \"After\" column (1 wk after OR 2 wk after)\n", "X['After'] = ((X['1 wk after'] == 1) | (X['2 wk after'] == 1)).astype(int)\n", "\n", "print(f\"Before category distribution:\")\n", "print(X['Before'].value_counts())\n", "print(f\"\\nAfter category distribution:\")\n", "print(X['After'].value_counts())\n", "\n", "# Remove individual timing columns and update feature list\n", "columns_to_remove = ['1 wk before', '2 wk before', '1 wk after', '2 wk after']\n", "X = X.drop(columns=columns_to_remove)\n", "\n", "# Update feature columns list\n", "feature_columns = X.columns.tolist()\n", "print(f\"\\nFinal feature columns after clubbing: {feature_columns}\")\n", "print(f\"Final shapes - X: {X.shape}, y: {y.shape}\")\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values analysis:\n", "ABI vs Segment PTC Index Agg    49\n", "dtype: int64\n", "\n", "Categorical columns: ['ABI Mechanic', 'Same Week', 'Before', 'After']\n", "Numeric columns: ['ABI_Duration_Days', 'Avg Temp', 'KSM', 'ABI vs Segment PTC Index Agg', 'ABI_Promo_W_Num_Distribution']\n", "\n", "Numeric missing values: {'ABI vs Segment PTC Index Agg': 49}\n", "Numeric missing values filled with median.\n", "\n", "Applying dummy encoding to categorical variables...\n", "Unique values in ABI Mechanic: ['FID' 'Immediate' 'LV' 'No NIP']\n", "Using 'No NIP' as base category for ABI Mechanic\n", "Unique values in Same Week: [ True False]\n", "'No NIP' not found in Same Week, using first category as base\n", "Unique values in Before: [1 0]\n", "'No NIP' not found in Before, using first category as base\n", "Unique values in After: [1 0]\n", "'No NIP' not found in After, using first category as base\n", "Dummy encoding completed. New shape: (762, 11)\n", "Final feature columns: ['ABI_Duration_Days', 'Avg Temp', 'KSM', 'ABI vs Segment PTC Index Agg', 'ABI_Promo_W_Num_Distribution', 'ABI Mechanic_FID', 'ABI Mechanic_Immediate', 'ABI Mechanic_LV', 'Same Week_True', 'Before_1', 'After_1']\n", "\n", "Target variable statistics:\n", "count    762.000000\n", "mean       3.282874\n", "std        3.957150\n", "min        0.635801\n", "25%        1.642080\n", "50%        2.512538\n", "75%        3.721450\n", "max       68.992889\n", "Name: ABI MS Promo Uplift - rel, dtype: float64\n", "\n", "Training samples: 685\n", "Test samples: 77\n", "Data preparation completed!\n"]}], "source": ["# Handle missing values and prepare data\n", "print(\"Missing values analysis:\")\n", "missing_info = X.isnull().sum()\n", "print(missing_info[missing_info > 0] if missing_info.sum() > 0 else \"No missing values found.\")\n", "\n", "# Explicitly define categorical and numeric columns\n", "# Categorical columns include timing indicators and mechanic type\n", "categorical_columns = [\n", "    'ABI Mechanic',\n", "    'Same Week', \n", "    'Before',    # Clubbed: 1 wk before + 2 wk before\n", "    'After'      # Clubbed: 1 wk after + 2 wk after\n", "]\n", "\n", "# Filter to only include categorical columns that exist in the dataset\n", "categorical_columns = [col for col in categorical_columns if col in X.columns]\n", "\n", "# Numeric columns are all the remaining columns\n", "numeric_columns = [col for col in X.columns if col not in categorical_columns]\n", "\n", "print(f\"\\nCategorical columns: {categorical_columns}\")\n", "print(f\"Numeric columns: {numeric_columns}\")\n", "\n", "# Handle missing values separately for categorical and numeric columns\n", "X_processed = X.copy()\n", "\n", "# For numeric columns: fill with median\n", "if len(numeric_columns) > 0:\n", "    numeric_missing = X_processed[numeric_columns].isnull().sum()\n", "    if numeric_missing.sum() > 0:\n", "        print(f\"\\nNumeric missing values: {numeric_missing[numeric_missing > 0].to_dict()}\")\n", "        X_processed[numeric_columns] = X_processed[numeric_columns].fillna(X_processed[numeric_columns].median())\n", "        print(\"Numeric missing values filled with median.\")\n", "\n", "# For categorical columns: fill with mode (most frequent value)\n", "if len(categorical_columns) > 0:\n", "    categorical_missing = X_processed[categorical_columns].isnull().sum()\n", "    if categorical_missing.sum() > 0:\n", "        print(f\"\\nCategorical missing values: {categorical_missing[categorical_missing > 0].to_dict()}\")\n", "        for col in categorical_columns:\n", "            if X_processed[col].isnull().sum() > 0:\n", "                mode_value = X_processed[col].mode()[0] if len(X_processed[col].mode()) > 0 else 'Unknown'\n", "                X_processed[col] = X_processed[col].fillna(mode_value)\n", "        print(\"Categorical missing values filled with mode.\")\n", "\n", "# Apply dummy encoding to categorical variables with 'No NIP' as base category\n", "if len(categorical_columns) > 0:\n", "    print(f\"\\nApplying dummy encoding to categorical variables...\")\n", "    for col in categorical_columns:\n", "        print(f\"Unique values in {col}: {X_processed[col].unique()}\")\n", "        \n", "        # Create dummy variables, dropping 'No NIP' as base category\n", "        dummy_vars = pd.get_dummies(X_processed[col], prefix=col, drop_first=False)\n", "        \n", "        # If 'No NIP' exists, drop it to use as base category\n", "        no_nip_col = f\"{col}_No NIP\"\n", "        if no_nip_col in dummy_vars.columns:\n", "            dummy_vars = dummy_vars.drop(no_nip_col, axis=1)\n", "            print(f\"Using 'No NIP' as base category for {col}\")\n", "        else:\n", "            # If 'No NIP' doesn't exist, drop the first category\n", "            dummy_vars = dummy_vars.iloc[:, 1:]\n", "            print(f\"'No NIP' not found in {col}, using first category as base\")\n", "        \n", "        # Add dummy variables to the dataset\n", "        X_processed = pd.concat([X_processed, dummy_vars], axis=1)\n", "    \n", "    # Remove original categorical columns\n", "    X_processed = X_processed.drop(categorical_columns, axis=1)\n", "    print(f\"Dummy encoding completed. New shape: {X_processed.shape}\")\n", "\n", "# Update feature columns list\n", "feature_columns = X_processed.columns.tolist()\n", "print(f\"Final feature columns: {feature_columns}\")\n", "\n", "# Basic statistics\n", "print(\"\\nTarget variable statistics:\")\n", "print(y.describe())\n", "\n", "# Split and scale data\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X_processed, y, test_size=0.1, random_state=42\n", ")\n", "\n", "print(f\"\\nTraining samples: {len(X_train)}\")\n", "print(f\"Test samples: {len(X_test)}\")\n", "\n", "# Scale features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# Storage for results\n", "models = {}\n", "results_summary = []\n", "\n", "print(\"Data preparation completed!\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 2. Linear Regression (Baseline Model)\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "LINEAR REGRESSION (BASELINE)\n", "============================================================\n", "Training R²: 0.0836\n", "Test R²: -0.0794\n", "Training RMSE: 2.6184\n", "Test RMSE: 9.6422\n", "Cross-validation R² (mean ± std): 0.0034 ± 0.1028\n"]}], "source": ["print(\"=\"*60)\n", "print(\"LINEAR REGRESSION (BASELINE)\")\n", "print(\"=\"*60)\n", "\n", "# Train Linear Regression\n", "lr_model = LinearRegression()\n", "lr_model.fit(X_train_scaled, y_train)\n", "\n", "# Predictions\n", "y_train_pred_lr = lr_model.predict(X_train_scaled)\n", "y_test_pred_lr = lr_model.predict(X_test_scaled)\n", "\n", "# Performance metrics\n", "train_r2_lr = r2_score(y_train, y_train_pred_lr)\n", "test_r2_lr = r2_score(y_test, y_test_pred_lr)\n", "train_rmse_lr = np.sqrt(mean_squared_error(y_train, y_train_pred_lr))\n", "test_rmse_lr = np.sqrt(mean_squared_error(y_test, y_test_pred_lr))\n", "cv_scores_lr = cross_val_score(lr_model, X_train_scaled, y_train, cv=5, scoring='r2')\n", "\n", "print(f\"Training R²: {train_r2_lr:.4f}\")\n", "print(f\"Test R²: {test_r2_lr:.4f}\")\n", "print(f\"Training RMSE: {train_rmse_lr:.4f}\")\n", "print(f\"Test RMSE: {test_rmse_lr:.4f}\")\n", "print(f\"Cross-validation R² (mean ± std): {cv_scores_lr.mean():.4f} ± {cv_scores_lr.std():.4f}\")\n", "\n", "# Store results\n", "models['Linear Regression'] = {\n", "    'model': lr_model,\n", "    'predictions_train': y_train_pred_lr,\n", "    'predictions_test': y_test_pred_lr\n", "}\n", "\n", "results_summary.append({\n", "    'Method': 'Linear Regression',\n", "    'Train R²': train_r2_lr,\n", "    'Test R²': test_r2_lr,\n", "    'Train RMSE': train_rmse_lr,\n", "    'Test RMSE': test_rmse_lr,\n", "    'CV R² Mean': cv_scores_lr.mean(),\n", "    'CV R² Std': cv_scores_lr.std()\n", "})\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Linear Regression - OLS Statistical Analysis\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OLS STATISTICAL ANALYSIS - LINEAR REGRESSION:\n", "                                OLS Regression Results                               \n", "=====================================================================================\n", "Dep. Variable:     ABI MS Promo Uplift - rel   R-squared:                       0.084\n", "Model:                                   OLS   Adj. R-squared:                  0.069\n", "Method:                        Least Squares   F-statistic:                     5.581\n", "Date:                       Tue, 01 Jul 2025   Prob (F-statistic):           1.34e-08\n", "Time:                               16:25:19   Log-Likelihood:                -1631.3\n", "No. Observations:                        685   AIC:                             3287.\n", "Df Residuals:                            673   BIC:                             3341.\n", "Df Model:                                 11                                         \n", "Covariance Type:                   nonrobust                                         \n", "================================================================================================\n", "                                   coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------------------------\n", "const                            3.1296      0.101     31.007      0.000       2.931       3.328\n", "ABI_Duration_Days               -0.3936      0.133     -2.966      0.003      -0.654      -0.133\n", "Avg Temp                         0.1459      0.105      1.390      0.165      -0.060       0.352\n", "KSM                              0.0352      0.106      0.333      0.739      -0.173       0.243\n", "ABI vs Segment PTC Index Agg    -0.1730      0.105     -1.643      0.101      -0.380       0.034\n", "ABI_Promo_W_Num_Distribution     0.1136      0.123      0.921      0.358      -0.129       0.356\n", "ABI Mechanic_FID                 0.5268      0.168      3.141      0.002       0.198       0.856\n", "ABI Mechanic_Immediate           0.5975      0.183      3.258      0.001       0.237       0.958\n", "ABI Mechanic_LV                  0.6839      0.207      3.300      0.001       0.277       1.091\n", "Same Week_True                   0.3471      0.115      3.030      0.003       0.122       0.572\n", "Before_1                        -0.0593      0.108     -0.551      0.582      -0.271       0.152\n", "After_1                          0.1152      0.107      1.075      0.283      -0.095       0.326\n", "==============================================================================\n", "Omnibus:                      608.764   <PERSON><PERSON><PERSON>-Watson:                   1.906\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            19667.899\n", "Skew:                           3.900   Prob(JB):                         0.00\n", "Kurtosis:                      28.065   Cond. No.                         4.51\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\n", "Linear Regression Feature Importance:\n", "                         Feature  Coefficient  Abs_Coefficient\n", "7                ABI Mechanic_LV     0.683872         0.683872\n", "6         ABI Mechanic_Immediate     0.597545         0.597545\n", "5               ABI Mechanic_FID     0.526806         0.526806\n", "0              ABI_Duration_Days    -0.393594         0.393594\n", "8                 Same Week_True     0.347088         0.347088\n", "3   ABI vs Segment PTC Index Agg    -0.172967         0.172967\n", "1                       Avg Temp     0.145920         0.145920\n", "10                       After_1     0.115238         0.115238\n", "4   ABI_Promo_W_Num_Distribution     0.113619         0.113619\n", "9                       Before_1    -0.059342         0.059342\n", "2                            KSM     0.035245         0.035245\n", "\n", "OLS Coefficient Mapping:\n", "                    Feature_Name  Coefficient  P_Value Significant\n", "0                      Intercept       3.1296   0.0000         Yes\n", "1              ABI_Duration_Days      -0.3936   0.0031         Yes\n", "2                       Avg Temp       0.1459   0.1650          No\n", "3                            KSM       0.0352   0.7392          No\n", "4   ABI vs Segment PTC Index Agg      -0.1730   0.1008          No\n", "5   ABI_Promo_W_Num_Distribution       0.1136   0.3575          No\n", "6               ABI Mechanic_FID       0.5268   0.0018         Yes\n", "7         ABI Mechanic_Immediate       0.5975   0.0012         Yes\n", "8                ABI Mechanic_LV       0.6839   0.0010         Yes\n", "9                 Same Week_True       0.3471   0.0025         Yes\n", "10                      Before_1      -0.0593   0.5821          No\n", "11                       After_1       0.1152   0.2827          No\n"]}], "source": ["# OLS Analysis for Linear Regression\n", "print(\"OLS STATISTICAL ANALYSIS - LINEAR REGRESSION:\")\n", "\n", "# Create DataFrame with proper feature names for OLS\n", "# Reset index to ensure alignment between y_train and X_train_df\n", "X_train_df = pd.DataFrame(X_train_scaled, columns=feature_columns, index=y_train.index)\n", "X_train_sm = sm.add_constant(X_train_df)\n", "ols_model_lr = sm.OLS(y_train, X_train_sm).fit()\n", "print(ols_model_lr.summary())\n", "\n", "# Feature importance\n", "feature_importance_lr = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Coefficient': lr_model.coef_,\n", "    'Abs_Coefficient': np.abs(lr_model.coef_)\n", "}).sort_values('Abs_Coefficient', ascending=False)\n", "\n", "print(\"\\nLinear Regression Feature Importance:\")\n", "print(feature_importance_lr)\n", "\n", "# Create a mapping table to show which features correspond to which coefficients\n", "print(\"\\nOLS Coefficient Mapping:\")\n", "coef_mapping = pd.DataFrame({\n", "    'Feature_Name': ['Intercept'] + list(feature_columns),\n", "    'Coefficient': [ols_model_lr.params[0]] + list(ols_model_lr.params[1:]),\n", "    'P_Value': [ols_model_lr.pvalues[0]] + list(ols_model_lr.pvalues[1:]),\n", "    'Significant': ['Yes' if p < 0.05 else 'No' for p in ([ols_model_lr.pvalues[0]] + list(ols_model_lr.pvalues[1:]))]\n", "}).round(4)\n", "print(coef_mapping)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 3. <PERSON> Regression (L2 Regularization)\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "RIDGE REGRESSION (L2 REGULARIZATION)\n", "============================================================\n", "Best Ridge alpha: 233.5721\n", "Training R²: 0.0711\n", "Test R²: -0.0520\n", "Training RMSE: 2.6362\n", "Test RMSE: 9.5194\n", "Cross-validation R² (mean ± std): 0.0140 ± 0.0867\n"]}], "source": ["print(\"=\"*60)\n", "print(\"RIDGE REGRESSION (L2 REGULARIZATION)\")\n", "print(\"=\"*60)\n", "\n", "# Hyperparameter tuning for Ridge\n", "ridge_alphas = np.logspace(-3, 3, 20)\n", "ridge_grid = GridSearchCV(Ridge(), {'alpha': ridge_alphas}, cv=5, scoring='r2')\n", "ridge_grid.fit(X_train_scaled, y_train)\n", "best_alpha_ridge = ridge_grid.best_params_['alpha']\n", "\n", "print(f\"Best Ridge alpha: {best_alpha_ridge:.4f}\")\n", "\n", "# Train Ridge model\n", "ridge_model = Ridge(alpha=best_alpha_ridge)\n", "ridge_model.fit(X_train_scaled, y_train)\n", "\n", "# Predictions\n", "y_train_pred_ridge = ridge_model.predict(X_train_scaled)\n", "y_test_pred_ridge = ridge_model.predict(X_test_scaled)\n", "\n", "# Performance metrics\n", "train_r2_ridge = r2_score(y_train, y_train_pred_ridge)\n", "test_r2_ridge = r2_score(y_test, y_test_pred_ridge)\n", "train_rmse_ridge = np.sqrt(mean_squared_error(y_train, y_train_pred_ridge))\n", "test_rmse_ridge = np.sqrt(mean_squared_error(y_test, y_test_pred_ridge))\n", "cv_scores_ridge = cross_val_score(ridge_model, X_train_scaled, y_train, cv=5, scoring='r2')\n", "\n", "print(f\"Training R²: {train_r2_ridge:.4f}\")\n", "print(f\"Test R²: {test_r2_ridge:.4f}\")\n", "print(f\"Training RMSE: {train_rmse_ridge:.4f}\")\n", "print(f\"Test RMSE: {test_rmse_ridge:.4f}\")\n", "print(f\"Cross-validation R² (mean ± std): {cv_scores_ridge.mean():.4f} ± {cv_scores_ridge.std():.4f}\")\n", "\n", "# Store results\n", "models['Ridge Regression'] = {\n", "    'model': ridge_model,\n", "    'predictions_train': y_train_pred_ridge,\n", "    'predictions_test': y_test_pred_ridge,\n", "    'alpha': best_alpha_ridge\n", "}\n", "\n", "results_summary.append({\n", "    'Method': 'Ridge Regression',\n", "    'Train R²': train_r2_ridge,\n", "    'Test R²': test_r2_ridge,\n", "    'Train RMSE': train_rmse_ridge,\n", "    'Test RMSE': test_rmse_ridge,\n", "    'CV R² Mean': cv_scores_ridge.mean(),\n", "    'CV R² Std': cv_scores_ridge.std()\n", "})\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RIDGE REGRESSION COEFFICIENTS ANALYSIS:\n", "                         Feature  Coefficient  Abs_Coefficient\n", "0              ABI_Duration_Days    -0.342450         0.342450\n", "8                 Same Week_True     0.257926         0.257926\n", "7                ABI Mechanic_LV     0.182693         0.182693\n", "6         ABI Mechanic_Immediate     0.165011         0.165011\n", "5               ABI Mechanic_FID     0.144222         0.144222\n", "1                       Avg Temp     0.122538         0.122538\n", "3   ABI vs Segment PTC Index Agg    -0.090955         0.090955\n", "10                       After_1     0.082655         0.082655\n", "9                       Before_1    -0.036506         0.036506\n", "4   ABI_Promo_W_Num_Distribution     0.010325         0.010325\n", "2                            KSM     0.002903         0.002903\n"]}], "source": ["# Ridge coefficient analysis\n", "print(\"RIDGE REGRESSION COEFFICIENTS ANALYSIS:\")\n", "ridge_coef_df = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Coefficient': ridge_model.coef_,\n", "    'Abs_Coefficient': np.abs(ridge_model.coef_)\n", "}).sort_values('Abs_Coefficient', ascending=False)\n", "\n", "print(ridge_coef_df)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 4. <PERSON><PERSON> Regression (L1 Regularization)\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "LASSO REGRESSION (L1 REGULARIZATION)\n", "============================================================\n", "Best Lasso alpha: 0.2069\n", "Training R²: 0.0517\n", "Test R²: -0.0417\n", "Training RMSE: 2.6636\n", "Test RMSE: 9.4726\n", "Cross-validation R² (mean ± std): 0.0098 ± 0.0740\n"]}], "source": ["print(\"=\"*60)\n", "print(\"LASSO REGRESSION (L1 REGULARIZATION)\")\n", "print(\"=\"*60)\n", "\n", "# Hyperparameter tuning for <PERSON><PERSON>\n", "lasso_alphas = np.logspace(-3, 1, 20)\n", "lasso_grid = GridSearchCV(Lasso(max_iter=2000), {'alpha': lasso_alphas}, cv=5, scoring='r2')\n", "lasso_grid.fit(X_train_scaled, y_train)\n", "best_alpha_lasso = lasso_grid.best_params_['alpha']\n", "\n", "print(f\"Best Lasso alpha: {best_alpha_lasso:.4f}\")\n", "\n", "# Train Lasso model\n", "lasso_model = Lasso(alpha=best_alpha_lasso, max_iter=2000)\n", "lasso_model.fit(X_train_scaled, y_train)\n", "\n", "# Predictions\n", "y_train_pred_lasso = lasso_model.predict(X_train_scaled)\n", "y_test_pred_lasso = lasso_model.predict(X_test_scaled)\n", "\n", "# Performance metrics\n", "train_r2_lasso = r2_score(y_train, y_train_pred_lasso)\n", "test_r2_lasso = r2_score(y_test, y_test_pred_lasso)\n", "train_rmse_lasso = np.sqrt(mean_squared_error(y_train, y_train_pred_lasso))\n", "test_rmse_lasso = np.sqrt(mean_squared_error(y_test, y_test_pred_lasso))\n", "cv_scores_lasso = cross_val_score(lasso_model, X_train_scaled, y_train, cv=5, scoring='r2')\n", "\n", "print(f\"Training R²: {train_r2_lasso:.4f}\")\n", "print(f\"Test R²: {test_r2_lasso:.4f}\")\n", "print(f\"Training RMSE: {train_rmse_lasso:.4f}\")\n", "print(f\"Test RMSE: {test_rmse_lasso:.4f}\")\n", "print(f\"Cross-validation R² (mean ± std): {cv_scores_lasso.mean():.4f} ± {cv_scores_lasso.std():.4f}\")\n", "\n", "# Store results\n", "models['Lasso Regression'] = {\n", "    'model': lasso_model,\n", "    'predictions_train': y_train_pred_lasso,\n", "    'predictions_test': y_test_pred_lasso,\n", "    'alpha': best_alpha_lasso\n", "}\n", "\n", "results_summary.append({\n", "    'Method': 'Lasso Regression',\n", "    'Train R²': train_r2_lasso,\n", "    'Test R²': test_r2_lasso,\n", "    'Train RMSE': train_rmse_lasso,\n", "    'Test RMSE': test_rmse_lasso,\n", "    'CV R² Mean': cv_scores_lasso.mean(),\n", "    'CV R² Std': cv_scores_lasso.std()\n", "})\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LASSO FEATURE SELECTION ANALYSIS:\n", "Features selected by <PERSON><PERSON>: 2 out of 11\n", "\n", "Selected features:\n", "             Feature  Coefficient  Abs_Coefficient\n", "0  ABI_Duration_Days    -0.338452         0.338452\n", "8     Same Week_True     0.160944         0.160944\n", "\n", "Features eliminated by <PERSON><PERSON>: 9\n", "['Avg Temp', 'KSM', 'ABI vs Segment PTC Index Agg', 'ABI_Promo_W_Num_Distribution', 'ABI Mechanic_FID', 'ABI Mechanic_Immediate', 'ABI Mechanic_LV', 'Before_1', 'After_1']\n"]}], "source": ["# Lasso feature selection analysis\n", "print(\"LASSO FEATURE SELECTION ANALYSIS:\")\n", "lasso_coef_df = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Coefficient': lasso_model.coef_,\n", "    'Abs_Coefficient': np.abs(lasso_model.coef_)\n", "}).sort_values('Abs_Coefficient', ascending=False)\n", "\n", "selected_features = lasso_coef_df[lasso_coef_df['Coefficient'] != 0]\n", "eliminated_features = lasso_coef_df[lasso_coef_df['Coefficient'] == 0]\n", "\n", "print(f\"Features selected by <PERSON><PERSON>: {len(selected_features)} out of {len(feature_columns)}\")\n", "print(\"\\nSelected features:\")\n", "print(selected_features)\n", "\n", "if len(eliminated_features) > 0:\n", "    print(f\"\\nFeatures eliminated by <PERSON><PERSON>: {len(eliminated_features)}\")\n", "    print(eliminated_features['Feature'].tolist())\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 5. Model Comparison Summary\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "MODEL COMPARISON SUMMARY\n", "============================================================\n", "              Method  Train R²  Test R²  Train RMSE  Test RMSE  CV R² Mean  \\\n", "0  Linear Regression    0.0836  -0.0794      2.6184     9.6422      0.0034   \n", "1   Ridge Regression    0.0711  -0.0520      2.6362     9.5194      0.0140   \n", "2   Lasso Regression    0.0517  -0.0417      2.6636     9.4726      0.0098   \n", "\n", "   CV R² Std  \n", "0     0.1028  \n", "1     0.0867  \n", "2     0.0740  \n", "\n", "Best performing model (Test R²): Lasso Regression\n", "\n", "Overfitting Analysis (Train R² - Test R²):\n", "Linear Regression: 0.1630\n", "Ridge Regression: 0.1232\n", "Lasso Regression: 0.0934\n", "\n", "Note: Lower overfitting score indicates better generalization\n"]}], "source": ["# Summary comparison\n", "print(\"=\"*60)\n", "print(\"MODEL COMPARISON SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "comparison_df = pd.DataFrame(results_summary)\n", "print(comparison_df.round(4))\n", "\n", "# Best model identification\n", "best_test_r2 = max([r['Test R²'] for r in results_summary])\n", "best_model_name = [r['Method'] for r in results_summary if r['Test R²'] == best_test_r2][0]\n", "print(f\"\\nBest performing model (Test R²): {best_model_name}\")\n", "\n", "# Overfitting analysis\n", "print(\"\\nOverfitting Analysis (Train R² - Test R²):\")\n", "for result in results_summary:\n", "    method = result['Method']\n", "    train_r2 = result['Train R²']\n", "    test_r2 = result['Test R²']\n", "    overfitting_score = train_r2 - test_r2\n", "    print(f\"{method}: {overfitting_score:.4f}\")\n", "    \n", "print(\"\\nNote: Lower overfitting score indicates better generalization\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 6. Comprehensive Visualizations\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"image/png": "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*********************************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******************************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", "text/plain": ["<Figure size 2000x1500 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Model comparison visualization\n", "plt.figure(figsize=(20, 15))\n", "\n", "# Actual vs Predicted for all models\n", "for i, (model_name, model_data) in enumerate(models.items()):\n", "    # Training set\n", "    plt.subplot(3, 3, i*3 + 1)\n", "    plt.scatter(y_train, model_data['predictions_train'], alpha=0.6, s=50, color='blue')\n", "    plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)\n", "    plt.xlabel('Actual')\n", "    plt.ylabel('Predicted')\n", "    train_r2 = r2_score(y_train, model_data['predictions_train'])\n", "    plt.title(f'{model_name} - Training\\nR² = {train_r2:.3f}')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Test set\n", "    plt.subplot(3, 3, i*3 + 2)\n", "    plt.scatter(y_test, model_data['predictions_test'], alpha=0.6, s=50, color='green')\n", "    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "    plt.xlabel('Actual')\n", "    plt.ylabel('Predicted')\n", "    test_r2 = r2_score(y_test, model_data['predictions_test'])\n", "    plt.title(f'{model_name} - Test\\nR² = {test_r2:.3f}')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Residuals\n", "    plt.subplot(3, 3, i*3 + 3)\n", "    residuals = y_test - model_data['predictions_test']\n", "    plt.hist(residuals, bins=20, alpha=0.7, edgecolor='black')\n", "    plt.xlabel('Residuals')\n", "    plt.ylabel('Frequency')\n", "    plt.title(f'{model_name} - Residuals')\n", "    plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('updated_models_comparison.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Performance comparison charts\n", "plt.figure(figsize=(15, 10))\n", "\n", "methods = [r['Method'] for r in results_summary]\n", "train_r2s = [r['Train R²'] for r in results_summary]\n", "test_r2s = [r['Test R²'] for r in results_summary]\n", "cv_means = [r['CV R² Mean'] for r in results_summary]\n", "\n", "x = np.arange(len(methods))\n", "width = 0.25\n", "\n", "# R² comparison\n", "plt.subplot(2, 2, 1)\n", "plt.bar(x - width, train_r2s, width, label='Train R²', alpha=0.8)\n", "plt.bar(x, test_r2s, width, label='Test R²', alpha=0.8)\n", "plt.bar(x + width, cv_means, width, label='CV R² Mean', alpha=0.8)\n", "plt.xlabel('Method')\n", "plt.ylabel('R² Score')\n", "plt.title('R² Comparison Across Methods')\n", "plt.xticks(x, methods, rotation=45)\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# RMSE comparison\n", "train_rmses = [r['Train RMSE'] for r in results_summary]\n", "test_rmses = [r['Test RMSE'] for r in results_summary]\n", "\n", "plt.subplot(2, 2, 2)\n", "plt.bar(x - width/2, train_rmses, width, label='Train RMSE', alpha=0.8)\n", "plt.bar(x + width/2, test_rmses, width, label='Test RMSE', alpha=0.8)\n", "plt.xlabel('Method')\n", "plt.ylabel('RMSE')\n", "plt.title('RMSE Comparison Across Methods')\n", "plt.xticks(x, methods, rotation=45)\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Cross-validation with error bars\n", "plt.subplot(2, 2, 3)\n", "cv_stds = [r['CV R² Std'] for r in results_summary]\n", "plt.bar(x, cv_means, yerr=cv_stds, capsize=5, alpha=0.8)\n", "plt.xlabel('Method')\n", "plt.ylabel('CV R² Score')\n", "plt.title('Cross-Validation R² (with std)')\n", "plt.xticks(x, methods, rotation=45)\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Overfitting comparison\n", "plt.subplot(2, 2, 4)\n", "overfitting_scores = [r['Train R²'] - r['Test R²'] for r in results_summary]\n", "colors = ['red' if score > 0.1 else 'orange' if score > 0.05 else 'green' for score in overfitting_scores]\n", "plt.bar(x, overfitting_scores, color=colors, alpha=0.8)\n", "plt.xlabel('Method')\n", "plt.ylabel('Overfitting Score (Train R² - Test R²)')\n", "plt.title('Overfitting Analysis')\n", "plt.xticks(x, methods, rotation=45)\n", "plt.axhline(y=0.1, color='red', linestyle='--', alpha=0.5, label='High overfitting')\n", "plt.axhline(y=0.05, color='orange', linestyle='--', alpha=0.5, label='Moderate overfitting')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('updated_performance_comparison.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 7. Feature Importance Comparison\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Coefficient Comparison:\n", "                         Feature  Linear   Ridge   Lasso\n", "0              ABI_Duration_Days -0.3936 -0.3425 -0.3385\n", "1                       Avg Temp  0.1459  0.1225  0.0000\n", "2                            KSM  0.0352  0.0029 -0.0000\n", "3   ABI vs Segment PTC Index Agg -0.1730 -0.0910 -0.0000\n", "4   ABI_Promo_W_Num_Distribution  0.1136  0.0103 -0.0000\n", "5               ABI Mechanic_FID  0.5268  0.1442  0.0000\n", "6         ABI Mechanic_Immediate  0.5975  0.1650  0.0000\n", "7                ABI Mechanic_LV  0.6839  0.1827  0.0000\n", "8                 Same Week_True  0.3471  0.2579  0.1609\n", "9                       Before_1 -0.0593 -0.0365 -0.0000\n", "10                       After_1  0.1152  0.0827  0.0000\n"]}], "source": ["# Feature coefficients comparison\n", "plt.figure(figsize=(15, 8))\n", "\n", "# Create coefficient comparison dataframe\n", "coef_comparison = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Linear': lr_model.coef_,\n", "    'Ridge': ridge_model.coef_,\n", "    'Lasso': lasso_model.coef_\n", "})\n", "\n", "# Plot coefficients for each method\n", "x_pos = np.arange(len(feature_columns))\n", "width = 0.25\n", "\n", "plt.bar(x_pos - width, coef_comparison['Linear'], width, label='Linear Regression', alpha=0.8)\n", "plt.bar(x_pos, coef_comparison['Ridge'], width, label='Ridge Regression', alpha=0.8)\n", "plt.bar(x_pos + width, coef_comparison['Lasso'], width, label='Lasso Regression', alpha=0.8)\n", "\n", "plt.xlabel('Features')\n", "plt.ylabel('Coefficient Value')\n", "plt.title('Feature Coefficients Comparison Across Methods')\n", "plt.xticks(x_pos, feature_columns, rotation=45, ha='right')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('updated_feature_coefficients.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"\\nCoefficient Comparison:\")\n", "print(coef_comparison.round(4))\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}