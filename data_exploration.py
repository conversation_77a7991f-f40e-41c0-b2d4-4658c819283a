#!/usr/bin/env python3
"""
Data Exploration Script for ADS V3 Dataset
This script analyzes the clean_new_test_output_ads_v3.csv file to understand
data distribution and identify potential outliers.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def load_and_explore_data():
    """Load the dataset and perform initial exploration"""
    print("🔍 Loading and exploring the dataset...")
    
    # Load the data
    df = pd.read_csv('clean_new_test_output_ads_v3.csv', index_col=0)
    
    print(f"Dataset shape: {df.shape}")
    print(f"Number of rows: {df.shape[0]}")
    print(f"Number of columns: {df.shape[1]}")
    
    print("\n📊 Column Information:")
    print(df.info())
    
    print("\n📈 Basic Statistics:")
    print(df.describe())
    
    print("\n🔢 Data Types:")
    print(df.dtypes)
    
    print("\n❓ Missing Values:")
    missing_values = df.isnull().sum()
    print(missing_values[missing_values > 0])
    
    return df

def identify_numeric_columns(df):
    """Identify numeric columns for outlier analysis"""
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    print(f"\n🔢 Numeric columns ({len(numeric_cols)}):")
    for col in numeric_cols:
        print(f"  - {col}")
    return numeric_cols

def analyze_outliers_iqr(df, numeric_cols):
    """Analyze outliers using IQR method"""
    print("\n📊 IQR Outlier Analysis:")
    outlier_summary = {}
    
    for col in numeric_cols:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
        outlier_count = len(outliers)
        outlier_percentage = (outlier_count / len(df)) * 100
        
        outlier_summary[col] = {
            'count': outlier_count,
            'percentage': outlier_percentage,
            'lower_bound': lower_bound,
            'upper_bound': upper_bound,
            'Q1': Q1,
            'Q3': Q3,
            'IQR': IQR
        }
        
        print(f"\n{col}:")
        print(f"  Outliers: {outlier_count} ({outlier_percentage:.2f}%)")
        print(f"  Bounds: [{lower_bound:.3f}, {upper_bound:.3f}]")
        print(f"  Range: [{df[col].min():.3f}, {df[col].max():.3f}]")
    
    return outlier_summary

def analyze_outliers_zscore(df, numeric_cols, threshold=3):
    """Analyze outliers using Z-score method"""
    print(f"\n📊 Z-Score Outlier Analysis (threshold = {threshold}):")
    zscore_summary = {}
    
    for col in numeric_cols:
        z_scores = np.abs(stats.zscore(df[col]))
        outliers = df[z_scores > threshold]
        outlier_count = len(outliers)
        outlier_percentage = (outlier_count / len(df)) * 100
        
        zscore_summary[col] = {
            'count': outlier_count,
            'percentage': outlier_percentage,
            'max_zscore': z_scores.max(),
            'mean_zscore': z_scores.mean()
        }
        
        print(f"\n{col}:")
        print(f"  Outliers: {outlier_count} ({outlier_percentage:.2f}%)")
        print(f"  Max Z-score: {z_scores.max():.3f}")
        print(f"  Mean Z-score: {z_scores.mean():.3f}")
    
    return zscore_summary

def create_outlier_visualizations(df, numeric_cols):
    """Create visualizations to identify outliers"""
    print("\n📈 Creating outlier visualizations...")
    
    # Set up the plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Calculate number of plots needed
    n_cols = len(numeric_cols)
    n_rows = (n_cols + 2) // 3  # 3 columns per row
    
    # Create box plots
    fig, axes = plt.subplots(n_rows, 3, figsize=(15, 5*n_rows))
    axes = axes.flatten() if n_rows > 1 else [axes] if n_rows == 1 else axes
    
    for i, col in enumerate(numeric_cols):
        if i < len(axes):
            df.boxplot(column=col, ax=axes[i])
            axes[i].set_title(f'Box Plot: {col}')
            axes[i].tick_params(axis='x', rotation=45)
    
    # Hide unused subplots
    for i in range(len(numeric_cols), len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('outlier_boxplots.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # Create histograms
    fig, axes = plt.subplots(n_rows, 3, figsize=(15, 5*n_rows))
    axes = axes.flatten() if n_rows > 1 else [axes] if n_rows == 1 else axes
    
    for i, col in enumerate(numeric_cols):
        if i < len(axes):
            axes[i].hist(df[col], bins=30, alpha=0.7, edgecolor='black')
            axes[i].set_title(f'Histogram: {col}')
            axes[i].set_xlabel(col)
            axes[i].set_ylabel('Frequency')
    
    # Hide unused subplots
    for i in range(len(numeric_cols), len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('outlier_histograms.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("📁 Saved visualizations: outlier_boxplots.png, outlier_histograms.png")

def main():
    """Main function to run the data exploration"""
    print("🚀 Starting Data Exploration for Outlier Detection")
    print("=" * 60)
    
    # Load and explore data
    df = load_and_explore_data()
    
    # Identify numeric columns
    numeric_cols = identify_numeric_columns(df)
    
    # Analyze outliers using different methods
    iqr_summary = analyze_outliers_iqr(df, numeric_cols)
    zscore_summary = analyze_outliers_zscore(df, numeric_cols)
    
    # Create visualizations
    create_outlier_visualizations(df, numeric_cols)
    
    # Summary report
    print("\n" + "=" * 60)
    print("📋 OUTLIER DETECTION SUMMARY")
    print("=" * 60)
    
    print("\n🎯 Top columns with outliers (IQR method):")
    iqr_sorted = sorted(iqr_summary.items(), key=lambda x: x[1]['percentage'], reverse=True)
    for col, stats in iqr_sorted[:10]:  # Top 10
        print(f"  {col}: {stats['count']} outliers ({stats['percentage']:.2f}%)")
    
    print("\n🎯 Top columns with outliers (Z-score method):")
    zscore_sorted = sorted(zscore_summary.items(), key=lambda x: x[1]['percentage'], reverse=True)
    for col, stats in zscore_sorted[:10]:  # Top 10
        print(f"  {col}: {stats['count']} outliers ({stats['percentage']:.2f}%)")
    
    print("\n✅ Data exploration complete!")
    return df, iqr_summary, zscore_summary

if __name__ == "__main__":
    df, iqr_summary, zscore_summary = main()
