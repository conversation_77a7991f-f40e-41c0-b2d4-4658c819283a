# Outlier Analysis and Removal for ADS V3 Dataset

## 📋 Overview

This repository contains a comprehensive outlier analysis and removal strategy for the `clean_new_test_output_ads_v3.csv` dataset. The analysis includes multiple outlier detection methods, impact assessment, and cleaned datasets ready for further analysis.

## 📊 Dataset Information

- **Original Dataset**: `clean_new_test_output_ads_v3.csv`
- **Rows**: 762
- **Columns**: 20
- **Key Target Variable**: `ABI MS Promo Uplift - rel`

## 🔍 Analysis Performed

### 1. Data Exploration (`data_exploration.py`)
- Basic statistical analysis
- Missing value assessment
- Outlier detection using IQR and Z-score methods
- Visualization generation (box plots and histograms)

### 2. Detailed Outlier Analysis (`outlier_analysis.py`)
- Extreme outlier identification
- Multivariate outlier detection using Mahalanobis distance
- Business logic validation
- Impact analysis on key statistics

### 3. Outlier Removal Strategies (`outlier_removal_strategies.py`)
- Implementation of 9 different outlier removal methods
- Comparative analysis of each method's effectiveness
- Generation of cleaned datasets

### 4. Final Recommendations (`outlier_removal_recommendations.py`)
- Data quality metrics comparison
- Decision framework for method selection
- Final recommendations with rationale

## 🎯 Key Findings

### Outlier Statistics
- **Extreme outliers (Z-score > 3)**: 13 in promo uplift column
- **Multivariate outliers**: 54 (7.09% of data)
- **Business logic violations**: 24 rows total
  - 1 row with promo uplift > 50%
  - 15 rows with coverage < 0.1
  - 8 rows with duration > 25 days

### Data Quality Issues
- Original data has high skewness (9.146) and kurtosis (124.232)
- Coefficient of variation: 1.205 (indicating high variability)
- Some extreme values that may impact analysis

## 📁 Generated Files

### Cleaned Datasets
1. **`cleaned_business_logic.csv`** ⭐ **RECOMMENDED**
   - Removes: 24 rows (3.1%)
   - Remaining: 738 rows
   - Method: Business logic only

2. **`cleaned_z-score_3.csv`** (Alternative)
   - Removes: 95 rows (12.5%)
   - Remaining: 667 rows
   - Method: Z-score threshold = 3

3. **`cleaned_iqr_2.0.csv`** (Aggressive)
   - Removes: 576 rows (75.6%)
   - Remaining: 186 rows
   - Method: IQR with multiplier = 2.0

4. **`cleaned_hybrid.csv`** (Combined approach)
   - Removes: 577 rows (75.7%)
   - Remaining: 185 rows
   - Method: Business logic + Modified IQR

### Analysis Scripts
- `data_exploration.py` - Initial data exploration
- `outlier_analysis.py` - Detailed outlier analysis
- `outlier_removal_strategies.py` - Strategy comparison
- `outlier_removal_recommendations.py` - Final recommendations

### Visualizations
- `outlier_boxplots.png` - Box plots for all numeric variables
- `outlier_histograms.png` - Histograms for all numeric variables

## 🏆 Final Recommendation

### **Use: `cleaned_business_logic.csv`**

#### Rationale:
1. **Minimal Data Loss**: Only 3.1% of data removed (24 rows)
2. **Business Relevance**: Removes only clearly problematic values
3. **Statistical Integrity**: Preserves natural data distribution
4. **Low Risk**: Minimal chance of removing valid business cases
5. **Analysis Suitability**: Perfect for exploratory analysis and reporting

#### What Was Removed:
- **Unrealistic promo uplift**: 1 row with uplift > 50%
- **Data quality issues**: 15 rows with coverage < 0.1
- **Unusual duration**: 8 rows with campaign duration > 25 days

## 🧭 Decision Framework

Choose your approach based on:

### Data Availability
- **Your case (762 samples)**: Conservative approach ✅
- High (>1000): Consider aggressive approaches
- Low (<500): Definitely use conservative

### Analysis Purpose
- **Exploratory Analysis**: Conservative ✅
- Predictive Modeling: Moderate
- Statistical Testing: Conservative or Moderate

### Risk Tolerance
- **Low risk**: Conservative ✅
- Medium risk: Moderate (Z-score)
- High risk: Aggressive (IQR)

## 📋 Next Steps

1. **Use `cleaned_business_logic.csv` for your main analysis**
2. **Document the removed rows** and validate with business stakeholders
3. **Monitor results** and adjust if needed based on domain expertise
4. **Consider Z-score method** if you need cleaner data later

## ⚠️ Important Notes

- Always validate outlier removal with business stakeholders
- Document all removed data points and reasons
- Consider the trade-off between data quality and quantity
- The recommended approach prioritizes data preservation over aggressive cleaning

## 🔧 Usage

```python
# Load the recommended cleaned dataset
import pandas as pd
df_clean = pd.read_csv('cleaned_business_logic.csv', index_col=0)

# Verify the cleaning
print(f"Original: 762 rows")
print(f"Cleaned: {len(df_clean)} rows")
print(f"Removed: {762 - len(df_clean)} rows ({(762 - len(df_clean))/762*100:.1f}%)")
```

## 📊 Quality Metrics Comparison

| Dataset | Rows | Mean Uplift | Std | Skewness | Kurtosis | CV |
|---------|------|-------------|-----|----------|----------|----|
| Original | 762 | 3.283 | 3.957 | 9.146 | 124.232 | 1.205 |
| **Business Logic** | **738** | **3.261** | **3.188** | **6.200** | **63.781** | **0.978** |
| Z-Score (3) | 667 | 3.065 | 1.868 | 1.803 | 4.424 | 0.609 |
| IQR (2.0) | 186 | 3.102 | 1.620 | 0.879 | 0.051 | 0.522 |

The business logic approach provides the best balance of data preservation and quality improvement.
