{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# Linear Regression Analysis with Regularization Options - UNCLUBBED\n", "\n", "## Overview\n", "This notebook compares three regression approaches to reduce overfitting:\n", "- **Linear Regression** (Baseline)\n", "- **Ridge Regression** (L2 Regularization)\n", "- **Lasso Regression** (L1 Regularization)\n", "\n", "**Target Variable:** ABI MS Promo Uplift - rel\n", "Removed Weighted Distribution, replaced with Numeric Dist\n", "\n", "**Features:**\n", "\n", "- ABI Mechanic (Encoded)\n", "- Same Week\n", "- 1 week after\n", "- 2 week after\n", "- 1 week before\n", "- 2 week before\n", "- Avg <PERSON><PERSON>\n", "- KSM\n", "- ABI_Duration_Days\n", "- ABI Depth_numeric\n", "- ABI vs Segment PTC Index\n", "- ABI_Promo_W_Num_Distribution\n", "\n", "\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 1. Data Loading and Preparation\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.linear_model import LinearRegression, Lasso, Ridge\n", "from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV\n", "from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error\n", "from sklearn.preprocessing import StandardScaler\n", "import statsmodels.api as sm\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully\")\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading dataset...\n", "Dataset shape: (762, 21)\n"]}, {"data": {"text/plain": ["['ABI Start',\n", " 'ABI End',\n", " 'ABI Coverage',\n", " 'ABI Mechanic',\n", " 'ABI Rounded',\n", " 'Overlapping',\n", " 'Same Week',\n", " '1 wk after',\n", " '2 wk after',\n", " '1 wk before',\n", " '2 wk before',\n", " 'Avg Temp',\n", " 'KSM',\n", " 'ABI MS Promo Uplift - rel',\n", " 'ABI Base W_Distribution',\n", " 'ABI Base Num_Distribution',\n", " 'ABI_Promo_W_W_Distribution',\n", " 'ABI_Promo_W_Num_Distribution',\n", " 'ABI vs Segment PTC Index Agg',\n", " 'ABI_Duration_Days']"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the dataset\n", "print(\"Loading dataset...\")\n", "df = pd.read_csv('clean_new_test_output_ads_v3.csv')\n", "print(f\"Dataset shape: {df.shape}\")\n", "df.drop(columns=['Unnamed: 0'], inplace=True)\n", "df.columns.tolist()\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Available features: 11\n", "Missing features: 0\n", "Final shapes - X: (762, 11), y: (762,)\n"]}], "source": ["# Define the target variable and features\n", "target_variable = 'ABI MS Promo Uplift - rel'\n", "\n", "requested_features = [\n", "    'ABI_Duration_Days',\n", "    'ABI Mechanic',\n", "    'Same Week',\n", "    '1 wk after',\n", "    '2 wk after',\n", "    '1 wk before',\n", "    '2 wk before',\n", "    'Avg Temp',\n", "    'KSM',\n", "    'ABI vs Segment PTC Index Agg',\n", "    'ABI_Promo_W_Num_Distribution'\n", "]\n", "\n", "# Check feature availability\n", "available_features = [f for f in requested_features if f in df.columns]\n", "missing_features = [f for f in requested_features if f not in df.columns]\n", "\n", "print(f\"Available features: {len(available_features)}\")\n", "print(f\"Missing features: {len(missing_features)}\")\n", "\n", "if missing_features:\n", "    print(f\"Missing: {missing_features}\")\n", "\n", "# Prepare data\n", "feature_columns = available_features\n", "X = df[feature_columns].copy()\n", "y = df[target_variable].copy()\n", "\n", "print(f\"Final shapes - X: {X.shape}, y: {y.shape}\")\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values analysis:\n", "ABI vs Segment PTC Index Agg    49\n", "dtype: int64\n", "\n", "Categorical columns: ['ABI Mechanic', 'Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']\n", "Numeric columns: ['ABI_Duration_Days', 'Avg Temp', 'KSM', 'ABI vs Segment PTC Index Agg', 'ABI_Promo_W_Num_Distribution']\n", "\n", "Numeric missing values: {'ABI vs Segment PTC Index Agg': 49}\n", "Numeric missing values filled with median.\n", "\n", "Applying dummy encoding to categorical variables...\n", "Unique values in ABI Mechanic: ['FID' 'Immediate' 'LV' 'No NIP']\n", "Using 'No NIP' as base category for ABI Mechanic\n", "Unique values in Same Week: [ True False]\n", "'No NIP' not found in Same Week, using first category as base\n", "Unique values in 1 wk after: [1 0]\n", "'No NIP' not found in 1 wk after, using first category as base\n", "Unique values in 2 wk after: [0 1]\n", "'No NIP' not found in 2 wk after, using first category as base\n", "Unique values in 1 wk before: [0 1]\n", "'No NIP' not found in 1 wk before, using first category as base\n", "Unique values in 2 wk before: [1 0]\n", "'No NIP' not found in 2 wk before, using first category as base\n", "Dummy encoding completed. New shape: (762, 13)\n", "Final feature columns: ['ABI_Duration_Days', 'Avg Temp', 'KSM', 'ABI vs Segment PTC Index Agg', 'ABI_Promo_W_Num_Distribution', 'ABI Mechanic_FID', 'ABI Mechanic_Immediate', 'ABI Mechanic_LV', 'Same Week_True', '1 wk after_1', '2 wk after_1', '1 wk before_1', '2 wk before_1']\n", "\n", "Target variable statistics:\n", "count    762.000000\n", "mean       3.282874\n", "std        3.957150\n", "min        0.635801\n", "25%        1.642080\n", "50%        2.512538\n", "75%        3.721450\n", "max       68.992889\n", "Name: ABI MS Promo Uplift - rel, dtype: float64\n", "\n", "Training samples: 685\n", "Test samples: 77\n", "Data preparation completed!\n"]}], "source": ["# Handle missing values and prepare data\n", "print(\"Missing values analysis:\")\n", "missing_info = X.isnull().sum()\n", "print(missing_info[missing_info > 0] if missing_info.sum() > 0 else \"No missing values found.\")\n", "\n", "# Explicitly define categorical and numeric columns\n", "# Categorical columns include timing indicators and mechanic type\n", "categorical_columns = [\n", "    'ABI Mechanic',\n", "    'Same Week', \n", "    '1 wk after', \n", "    '2 wk after', \n", "    '1 wk before', \n", "    '2 wk before'\n", "]\n", "\n", "# Filter to only include categorical columns that exist in the dataset\n", "categorical_columns = [col for col in categorical_columns if col in X.columns]\n", "\n", "# Numeric columns are all the remaining columns\n", "numeric_columns = [col for col in X.columns if col not in categorical_columns]\n", "\n", "print(f\"\\nCategorical columns: {categorical_columns}\")\n", "print(f\"Numeric columns: {numeric_columns}\")\n", "\n", "# Handle missing values separately for categorical and numeric columns\n", "X_processed = X.copy()\n", "\n", "# For numeric columns: fill with median\n", "if len(numeric_columns) > 0:\n", "    numeric_missing = X_processed[numeric_columns].isnull().sum()\n", "    if numeric_missing.sum() > 0:\n", "        print(f\"\\nNumeric missing values: {numeric_missing[numeric_missing > 0].to_dict()}\")\n", "        X_processed[numeric_columns] = X_processed[numeric_columns].fillna(X_processed[numeric_columns].median())\n", "        print(\"Numeric missing values filled with median.\")\n", "\n", "# For categorical columns: fill with mode (most frequent value)\n", "if len(categorical_columns) > 0:\n", "    categorical_missing = X_processed[categorical_columns].isnull().sum()\n", "    if categorical_missing.sum() > 0:\n", "        print(f\"\\nCategorical missing values: {categorical_missing[categorical_missing > 0].to_dict()}\")\n", "        for col in categorical_columns:\n", "            if X_processed[col].isnull().sum() > 0:\n", "                mode_value = X_processed[col].mode()[0] if len(X_processed[col].mode()) > 0 else 'Unknown'\n", "                X_processed[col] = X_processed[col].fillna(mode_value)\n", "        print(\"Categorical missing values filled with mode.\")\n", "\n", "# Apply dummy encoding to categorical variables with 'No NIP' as base category\n", "if len(categorical_columns) > 0:\n", "    print(f\"\\nApplying dummy encoding to categorical variables...\")\n", "    for col in categorical_columns:\n", "        print(f\"Unique values in {col}: {X_processed[col].unique()}\")\n", "        \n", "        # Create dummy variables, dropping 'No NIP' as base category\n", "        dummy_vars = pd.get_dummies(X_processed[col], prefix=col, drop_first=False)\n", "        \n", "        # If 'No NIP' exists, drop it to use as base category\n", "        no_nip_col = f\"{col}_No NIP\"\n", "        if no_nip_col in dummy_vars.columns:\n", "            dummy_vars = dummy_vars.drop(no_nip_col, axis=1)\n", "            print(f\"Using 'No NIP' as base category for {col}\")\n", "        else:\n", "            # If 'No NIP' doesn't exist, drop the first category\n", "            dummy_vars = dummy_vars.iloc[:, 1:]\n", "            print(f\"'No NIP' not found in {col}, using first category as base\")\n", "        \n", "        # Add dummy variables to the dataset\n", "        X_processed = pd.concat([X_processed, dummy_vars], axis=1)\n", "    \n", "    # Remove original categorical columns\n", "    X_processed = X_processed.drop(categorical_columns, axis=1)\n", "    print(f\"Dummy encoding completed. New shape: {X_processed.shape}\")\n", "\n", "# Update feature columns list\n", "feature_columns = X_processed.columns.tolist()\n", "print(f\"Final feature columns: {feature_columns}\")\n", "\n", "# Basic statistics\n", "print(\"\\nTarget variable statistics:\")\n", "print(y.describe())\n", "\n", "# Split and scale data\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X_processed, y, test_size=0.1, random_state=42\n", ")\n", "\n", "print(f\"\\nTraining samples: {len(X_train)}\")\n", "print(f\"Test samples: {len(X_test)}\")\n", "\n", "# Scale features\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# Storage for results\n", "models = {}\n", "results_summary = []\n", "\n", "print(\"Data preparation completed!\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 2. Linear Regression (Baseline Model)\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "LINEAR REGRESSION (BASELINE)\n", "============================================================\n", "Training R²: 0.0830\n", "Test R²: -0.0787\n", "Training RMSE: 2.6192\n", "Test RMSE: 9.6393\n", "Cross-validation R² (mean ± std): 0.0038 ± 0.1039\n"]}], "source": ["print(\"=\"*60)\n", "print(\"LINEAR REGRESSION (BASELINE)\")\n", "print(\"=\"*60)\n", "\n", "# Train Linear Regression\n", "lr_model = LinearRegression()\n", "lr_model.fit(X_train_scaled, y_train)\n", "\n", "# Predictions\n", "y_train_pred_lr = lr_model.predict(X_train_scaled)\n", "y_test_pred_lr = lr_model.predict(X_test_scaled)\n", "\n", "# Performance metrics\n", "train_r2_lr = r2_score(y_train, y_train_pred_lr)\n", "test_r2_lr = r2_score(y_test, y_test_pred_lr)\n", "train_rmse_lr = np.sqrt(mean_squared_error(y_train, y_train_pred_lr))\n", "test_rmse_lr = np.sqrt(mean_squared_error(y_test, y_test_pred_lr))\n", "cv_scores_lr = cross_val_score(lr_model, X_train_scaled, y_train, cv=5, scoring='r2')\n", "\n", "print(f\"Training R²: {train_r2_lr:.4f}\")\n", "print(f\"Test R²: {test_r2_lr:.4f}\")\n", "print(f\"Training RMSE: {train_rmse_lr:.4f}\")\n", "print(f\"Test RMSE: {test_rmse_lr:.4f}\")\n", "print(f\"Cross-validation R² (mean ± std): {cv_scores_lr.mean():.4f} ± {cv_scores_lr.std():.4f}\")\n", "\n", "# Store results\n", "models['Linear Regression'] = {\n", "    'model': lr_model,\n", "    'predictions_train': y_train_pred_lr,\n", "    'predictions_test': y_test_pred_lr\n", "}\n", "\n", "results_summary.append({\n", "    'Method': 'Linear Regression',\n", "    'Train R²': train_r2_lr,\n", "    'Test R²': test_r2_lr,\n", "    'Train RMSE': train_rmse_lr,\n", "    'Test RMSE': test_rmse_lr,\n", "    'CV R² Mean': cv_scores_lr.mean(),\n", "    'CV R² Std': cv_scores_lr.std()\n", "})\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### Linear Regression - OLS Statistical Analysis\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OLS STATISTICAL ANALYSIS - LINEAR REGRESSION:\n", "                                OLS Regression Results                               \n", "=====================================================================================\n", "Dep. Variable:     ABI MS Promo Uplift - rel   R-squared:                       0.083\n", "Model:                                   OLS   Adj. R-squared:                  0.065\n", "Method:                        Least Squares   F-statistic:                     4.673\n", "Date:                       Tu<PERSON>, 01 Jul 2025   Prob (F-statistic):           9.23e-08\n", "Time:                               16:24:40   Log-Likelihood:                -1631.5\n", "No. Observations:                        685   AIC:                             3291.\n", "Df Residuals:                            671   BIC:                             3355.\n", "Df Model:                                 13                                         \n", "Covariance Type:                   nonrobust                                         \n", "================================================================================================\n", "                                   coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------------------------\n", "const                            3.1296      0.101     30.951      0.000       2.931       3.328\n", "ABI_Duration_Days               -0.3952      0.134     -2.947      0.003      -0.659      -0.132\n", "Avg Temp                         0.1418      0.105      1.348      0.178      -0.065       0.348\n", "KSM                              0.0310      0.106      0.291      0.771      -0.178       0.240\n", "ABI vs Segment PTC Index Agg    -0.1700      0.106     -1.607      0.109      -0.378       0.038\n", "ABI_Promo_W_Num_Distribution     0.1136      0.124      0.918      0.359      -0.129       0.357\n", "ABI Mechanic_FID                 0.5276      0.169      3.130      0.002       0.197       0.859\n", "ABI Mechanic_Immediate           0.6047      0.184      3.279      0.001       0.243       0.967\n", "ABI Mechanic_LV                  0.6950      0.209      3.330      0.001       0.285       1.105\n", "Same Week_True                   0.3494      0.130      2.689      0.007       0.094       0.605\n", "1 wk after_1                     0.0831      0.116      0.715      0.475      -0.145       0.311\n", "2 wk after_1                     0.0869      0.107      0.816      0.415      -0.122       0.296\n", "1 wk before_1                   -0.0445      0.117     -0.382      0.703      -0.274       0.185\n", "2 wk before_1                    0.0095      0.106      0.089      0.929      -0.198       0.217\n", "==============================================================================\n", "Omnibus:                      608.155   <PERSON><PERSON><PERSON>-Watson:                   1.904\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):            19602.466\n", "Skew:                           3.895   Prob(JB):                         0.00\n", "Kurtosis:                      28.022   Cond. No.                         4.56\n", "==============================================================================\n", "\n", "Notes:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "\n", "Linear Regression Feature Importance:\n", "                         Feature  Coefficient  Abs_Coefficient\n", "7                ABI Mechanic_LV     0.694955         0.694955\n", "6         ABI Mechanic_Immediate     0.604656         0.604656\n", "5               ABI Mechanic_FID     0.527634         0.527634\n", "0              ABI_Duration_Days    -0.395230         0.395230\n", "8                 Same Week_True     0.349383         0.349383\n", "3   ABI vs Segment PTC Index Agg    -0.170030         0.170030\n", "1                       Avg Temp     0.141805         0.141805\n", "4   ABI_Promo_W_Num_Distribution     0.113630         0.113630\n", "10                  2 wk after_1     0.086944         0.086944\n", "9                   1 wk after_1     0.083112         0.083112\n", "11                 1 wk before_1    -0.044535         0.044535\n", "2                            KSM     0.030986         0.030986\n", "12                 2 wk before_1     0.009453         0.009453\n", "\n", "OLS Coefficient Mapping:\n", "                    Feature_Name  Coefficient  P_Value Significant\n", "0                      Intercept       3.1296   0.0000         Yes\n", "1              ABI_Duration_Days      -0.3952   0.0033         Yes\n", "2                       Avg Temp       0.1418   0.1782          No\n", "3                            KSM       0.0310   0.7710          No\n", "4   ABI vs Segment PTC Index Agg      -0.1700   0.1086          No\n", "5   ABI_Promo_W_Num_Distribution       0.1136   0.3590          No\n", "6               ABI Mechanic_FID       0.5276   0.0018         Yes\n", "7         ABI Mechanic_Immediate       0.6047   0.0011         Yes\n", "8                ABI Mechanic_LV       0.6950   0.0009         Yes\n", "9                 Same Week_True       0.3494   0.0074         Yes\n", "10                  1 wk after_1       0.0831   0.4746          No\n", "11                  2 wk after_1       0.0869   0.4150          No\n", "12                 1 wk before_1      -0.0445   0.7028          No\n", "13                 2 wk before_1       0.0095   0.9289          No\n"]}], "source": ["# OLS Analysis for Linear Regression\n", "print(\"OLS STATISTICAL ANALYSIS - LINEAR REGRESSION:\")\n", "\n", "# Create DataFrame with proper feature names for OLS\n", "# Reset index to ensure alignment between y_train and X_train_df\n", "X_train_df = pd.DataFrame(X_train_scaled, columns=feature_columns, index=y_train.index)\n", "X_train_sm = sm.add_constant(X_train_df)\n", "ols_model_lr = sm.OLS(y_train, X_train_sm).fit()\n", "print(ols_model_lr.summary())\n", "\n", "# Feature importance\n", "feature_importance_lr = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Coefficient': lr_model.coef_,\n", "    'Abs_Coefficient': np.abs(lr_model.coef_)\n", "}).sort_values('Abs_Coefficient', ascending=False)\n", "\n", "print(\"\\nLinear Regression Feature Importance:\")\n", "print(feature_importance_lr)\n", "\n", "# Create a mapping table to show which features correspond to which coefficients\n", "print(\"\\nOLS Coefficient Mapping:\")\n", "coef_mapping = pd.DataFrame({\n", "    'Feature_Name': ['Intercept'] + list(feature_columns),\n", "    'Coefficient': [ols_model_lr.params[0]] + list(ols_model_lr.params[1:]),\n", "    'P_Value': [ols_model_lr.pvalues[0]] + list(ols_model_lr.pvalues[1:]),\n", "    'Significant': ['Yes' if p < 0.05 else 'No' for p in ([ols_model_lr.pvalues[0]] + list(ols_model_lr.pvalues[1:]))]\n", "}).round(4)\n", "print(coef_mapping)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 3. <PERSON> Regression (L2 Regularization)\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "RIDGE REGRESSION (L2 REGULARIZATION)\n", "============================================================\n", "Best Ridge alpha: 233.5721\n", "Training R²: 0.0704\n", "Test R²: -0.0522\n", "Training RMSE: 2.6372\n", "Test RMSE: 9.5199\n", "Cross-validation R² (mean ± std): 0.0127 ± 0.0885\n"]}], "source": ["print(\"=\"*60)\n", "print(\"RIDGE REGRESSION (L2 REGULARIZATION)\")\n", "print(\"=\"*60)\n", "\n", "# Hyperparameter tuning for Ridge\n", "ridge_alphas = np.logspace(-3, 3, 20)\n", "ridge_grid = GridSearchCV(Ridge(), {'alpha': ridge_alphas}, cv=5, scoring='r2')\n", "ridge_grid.fit(X_train_scaled, y_train)\n", "best_alpha_ridge = ridge_grid.best_params_['alpha']\n", "\n", "print(f\"Best Ridge alpha: {best_alpha_ridge:.4f}\")\n", "\n", "# Train Ridge model\n", "ridge_model = Ridge(alpha=best_alpha_ridge)\n", "ridge_model.fit(X_train_scaled, y_train)\n", "\n", "# Predictions\n", "y_train_pred_ridge = ridge_model.predict(X_train_scaled)\n", "y_test_pred_ridge = ridge_model.predict(X_test_scaled)\n", "\n", "# Performance metrics\n", "train_r2_ridge = r2_score(y_train, y_train_pred_ridge)\n", "test_r2_ridge = r2_score(y_test, y_test_pred_ridge)\n", "train_rmse_ridge = np.sqrt(mean_squared_error(y_train, y_train_pred_ridge))\n", "test_rmse_ridge = np.sqrt(mean_squared_error(y_test, y_test_pred_ridge))\n", "cv_scores_ridge = cross_val_score(ridge_model, X_train_scaled, y_train, cv=5, scoring='r2')\n", "\n", "print(f\"Training R²: {train_r2_ridge:.4f}\")\n", "print(f\"Test R²: {test_r2_ridge:.4f}\")\n", "print(f\"Training RMSE: {train_rmse_ridge:.4f}\")\n", "print(f\"Test RMSE: {test_rmse_ridge:.4f}\")\n", "print(f\"Cross-validation R² (mean ± std): {cv_scores_ridge.mean():.4f} ± {cv_scores_ridge.std():.4f}\")\n", "\n", "# Store results\n", "models['Ridge Regression'] = {\n", "    'model': ridge_model,\n", "    'predictions_train': y_train_pred_ridge,\n", "    'predictions_test': y_test_pred_ridge,\n", "    'alpha': best_alpha_ridge\n", "}\n", "\n", "results_summary.append({\n", "    'Method': 'Ridge Regression',\n", "    'Train R²': train_r2_ridge,\n", "    'Test R²': test_r2_ridge,\n", "    'Train RMSE': train_rmse_ridge,\n", "    'Test RMSE': test_rmse_ridge,\n", "    'CV R² Mean': cv_scores_ridge.mean(),\n", "    'CV R² Std': cv_scores_ridge.std()\n", "})\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RIDGE REGRESSION COEFFICIENTS ANALYSIS:\n", "                         Feature  Coefficient  Abs_Coefficient\n", "0              ABI_Duration_Days    -0.346276         0.346276\n", "8                 Same Week_True     0.248473         0.248473\n", "7                ABI Mechanic_LV     0.184489         0.184489\n", "6         ABI Mechanic_Immediate     0.165040         0.165040\n", "5               ABI Mechanic_FID     0.141956         0.141956\n", "1                       Avg Temp     0.120584         0.120584\n", "3   ABI vs Segment PTC Index Agg    -0.091206         0.091206\n", "9                   1 wk after_1     0.067952         0.067952\n", "10                  2 wk after_1     0.047071         0.047071\n", "4   ABI_Promo_W_Num_Distribution     0.008754         0.008754\n", "11                 1 wk before_1    -0.004382         0.004382\n", "12                 2 wk before_1    -0.003347         0.003347\n", "2                            KSM     0.001478         0.001478\n"]}], "source": ["# Ridge coefficient analysis\n", "print(\"RIDGE REGRESSION COEFFICIENTS ANALYSIS:\")\n", "ridge_coef_df = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Coefficient': ridge_model.coef_,\n", "    'Abs_Coefficient': np.abs(ridge_model.coef_)\n", "}).sort_values('Abs_Coefficient', ascending=False)\n", "\n", "print(ridge_coef_df)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 4. <PERSON><PERSON> Regression (L1 Regularization)\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "LASSO REGRESSION (L1 REGULARIZATION)\n", "============================================================\n", "Best Lasso alpha: 0.2069\n", "Training R²: 0.0517\n", "Test R²: -0.0417\n", "Training RMSE: 2.6636\n", "Test RMSE: 9.4726\n", "Cross-validation R² (mean ± std): 0.0098 ± 0.0740\n"]}], "source": ["print(\"=\"*60)\n", "print(\"LASSO REGRESSION (L1 REGULARIZATION)\")\n", "print(\"=\"*60)\n", "\n", "# Hyperparameter tuning for <PERSON><PERSON>\n", "lasso_alphas = np.logspace(-3, 1, 20)\n", "lasso_grid = GridSearchCV(Lasso(max_iter=2000), {'alpha': lasso_alphas}, cv=5, scoring='r2')\n", "lasso_grid.fit(X_train_scaled, y_train)\n", "best_alpha_lasso = lasso_grid.best_params_['alpha']\n", "\n", "print(f\"Best Lasso alpha: {best_alpha_lasso:.4f}\")\n", "\n", "# Train Lasso model\n", "lasso_model = Lasso(alpha=best_alpha_lasso, max_iter=2000)\n", "lasso_model.fit(X_train_scaled, y_train)\n", "\n", "# Predictions\n", "y_train_pred_lasso = lasso_model.predict(X_train_scaled)\n", "y_test_pred_lasso = lasso_model.predict(X_test_scaled)\n", "\n", "# Performance metrics\n", "train_r2_lasso = r2_score(y_train, y_train_pred_lasso)\n", "test_r2_lasso = r2_score(y_test, y_test_pred_lasso)\n", "train_rmse_lasso = np.sqrt(mean_squared_error(y_train, y_train_pred_lasso))\n", "test_rmse_lasso = np.sqrt(mean_squared_error(y_test, y_test_pred_lasso))\n", "cv_scores_lasso = cross_val_score(lasso_model, X_train_scaled, y_train, cv=5, scoring='r2')\n", "\n", "print(f\"Training R²: {train_r2_lasso:.4f}\")\n", "print(f\"Test R²: {test_r2_lasso:.4f}\")\n", "print(f\"Training RMSE: {train_rmse_lasso:.4f}\")\n", "print(f\"Test RMSE: {test_rmse_lasso:.4f}\")\n", "print(f\"Cross-validation R² (mean ± std): {cv_scores_lasso.mean():.4f} ± {cv_scores_lasso.std():.4f}\")\n", "\n", "# Store results\n", "models['Lasso Regression'] = {\n", "    'model': lasso_model,\n", "    'predictions_train': y_train_pred_lasso,\n", "    'predictions_test': y_test_pred_lasso,\n", "    'alpha': best_alpha_lasso\n", "}\n", "\n", "results_summary.append({\n", "    'Method': 'Lasso Regression',\n", "    'Train R²': train_r2_lasso,\n", "    'Test R²': test_r2_lasso,\n", "    'Train RMSE': train_rmse_lasso,\n", "    'Test RMSE': test_rmse_lasso,\n", "    'CV R² Mean': cv_scores_lasso.mean(),\n", "    'CV R² Std': cv_scores_lasso.std()\n", "})\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LASSO FEATURE SELECTION ANALYSIS:\n", "Features selected by <PERSON><PERSON>: 2 out of 13\n", "\n", "Selected features:\n", "             Feature  Coefficient  Abs_Coefficient\n", "0  ABI_Duration_Days    -0.338452         0.338452\n", "8     Same Week_True     0.160944         0.160944\n", "\n", "Features eliminated by <PERSON><PERSON>: 11\n", "['Avg Temp', 'KSM', 'ABI vs Segment PTC Index Agg', 'ABI_Promo_W_Num_Distribution', 'ABI Mechanic_FID', 'ABI Mechanic_Immediate', 'ABI Mechanic_LV', '1 wk after_1', '2 wk after_1', '1 wk before_1', '2 wk before_1']\n"]}], "source": ["# Lasso feature selection analysis\n", "print(\"LASSO FEATURE SELECTION ANALYSIS:\")\n", "lasso_coef_df = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Coefficient': lasso_model.coef_,\n", "    'Abs_Coefficient': np.abs(lasso_model.coef_)\n", "}).sort_values('Abs_Coefficient', ascending=False)\n", "\n", "selected_features = lasso_coef_df[lasso_coef_df['Coefficient'] != 0]\n", "eliminated_features = lasso_coef_df[lasso_coef_df['Coefficient'] == 0]\n", "\n", "print(f\"Features selected by <PERSON><PERSON>: {len(selected_features)} out of {len(feature_columns)}\")\n", "print(\"\\nSelected features:\")\n", "print(selected_features)\n", "\n", "if len(eliminated_features) > 0:\n", "    print(f\"\\nFeatures eliminated by <PERSON><PERSON>: {len(eliminated_features)}\")\n", "    print(eliminated_features['Feature'].tolist())\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 5. Model Comparison Summary\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "MODEL COMPARISON SUMMARY\n", "============================================================\n", "              Method  Train R²  Test R²  Train RMSE  Test RMSE  CV R² Mean  \\\n", "0  Linear Regression    0.0830  -0.0787      2.6192     9.6393      0.0038   \n", "1   Ridge Regression    0.0704  -0.0522      2.6372     9.5199      0.0127   \n", "2   Lasso Regression    0.0517  -0.0417      2.6636     9.4726      0.0098   \n", "\n", "   CV R² Std  \n", "0     0.1039  \n", "1     0.0885  \n", "2     0.0740  \n", "\n", "Best performing model (Test R²): Lasso Regression\n", "\n", "Overfitting Analysis (Train R² - Test R²):\n", "Linear Regression: 0.1617\n", "Ridge Regression: 0.1225\n", "Lasso Regression: 0.0934\n", "\n", "Note: Lower overfitting score indicates better generalization\n"]}], "source": ["# Summary comparison\n", "print(\"=\"*60)\n", "print(\"MODEL COMPARISON SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "comparison_df = pd.DataFrame(results_summary)\n", "print(comparison_df.round(4))\n", "\n", "# Best model identification\n", "best_test_r2 = max([r['Test R²'] for r in results_summary])\n", "best_model_name = [r['Method'] for r in results_summary if r['Test R²'] == best_test_r2][0]\n", "print(f\"\\nBest performing model (Test R²): {best_model_name}\")\n", "\n", "# Overfitting analysis\n", "print(\"\\nOverfitting Analysis (Train R² - Test R²):\")\n", "for result in results_summary:\n", "    method = result['Method']\n", "    train_r2 = result['Train R²']\n", "    test_r2 = result['Test R²']\n", "    overfitting_score = train_r2 - test_r2\n", "    print(f\"{method}: {overfitting_score:.4f}\")\n", "    \n", "print(\"\\nNote: Lower overfitting score indicates better generalization\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 6. Comprehensive Visualizations\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"image/png": "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*********************************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", "text/plain": ["<Figure size 2000x1500 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Model comparison visualization\n", "plt.figure(figsize=(20, 15))\n", "\n", "# Actual vs Predicted for all models\n", "for i, (model_name, model_data) in enumerate(models.items()):\n", "    # Training set\n", "    plt.subplot(3, 3, i*3 + 1)\n", "    plt.scatter(y_train, model_data['predictions_train'], alpha=0.6, s=50, color='blue')\n", "    plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)\n", "    plt.xlabel('Actual')\n", "    plt.ylabel('Predicted')\n", "    train_r2 = r2_score(y_train, model_data['predictions_train'])\n", "    plt.title(f'{model_name} - Training\\nR² = {train_r2:.3f}')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Test set\n", "    plt.subplot(3, 3, i*3 + 2)\n", "    plt.scatter(y_test, model_data['predictions_test'], alpha=0.6, s=50, color='green')\n", "    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "    plt.xlabel('Actual')\n", "    plt.ylabel('Predicted')\n", "    test_r2 = r2_score(y_test, model_data['predictions_test'])\n", "    plt.title(f'{model_name} - Test\\nR² = {test_r2:.3f}')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Residuals\n", "    plt.subplot(3, 3, i*3 + 3)\n", "    residuals = y_test - model_data['predictions_test']\n", "    plt.hist(residuals, bins=20, alpha=0.7, edgecolor='black')\n", "    plt.xlabel('Residuals')\n", "    plt.ylabel('Frequency')\n", "    plt.title(f'{model_name} - Residuals')\n", "    plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('updated_models_comparison.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAAPdCAYAAABlRyFLAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzs3Qm8jGX/x/HfsR37mrVslUKWRCRKIkREqKQsSU9FsrQQWZIka5Z2pCciihalpKisWapHoT1ll53s8399r+eZ+c85Z8Y5h3Nmzn3O5/16TTr33DNzz3LN/K7r/l2/K8bn8/kMAAAAAAAAAAAkkCnhJgAAAAAAAAAAIAyiAwAAAAAAAAAQBoPoAAAAAAAAAACEwSA6AAAAAAAAAABhMIgOAAAAAAAAAEAYDKIDAAAAAAAAABAGg+gAAAAAAAAAAITBIDoAAAAAAAAAAGEwiA4AAAAAAAAAQBgMogNAOvLaa69ZTEyM/f7779E+FETR4MGD3edg9+7dqf5YZcqUsU6dOqX64wAAAKRHxFIQxe7du3dP9cehvwicPQbRAUSM/wfbf8mSJYudf/75LmjcsmVLnH1nzpxpV199tdWrV88uu+wye/XVV5P8OHPnzrUbb7zRzjvvPMuWLZuVKFHCbr31Vvvss89S4VnhbGzYsMF9BrJnz2779u2z9DyQnSlTJvvzzz8TXH/gwAHLkSPHOQXMTz/9tM2bNy8FjhYAAKSnWFquu+46t1+5cuVC3t/ChQsD9zVnzpw41/3nP/+xNm3aWOnSpV28pse54YYbbMKECQkGgIOPKfjSpEmTJD2vHTt22MMPP2zly5e3nDlzWq5cuax69er21FNPpds40YvUn9L7+thjj1l65f/s3nPPPSGv79+/f2Cfs0lWWbZsmesj8LkGvClLtA8AQMbz5JNPWtmyZe3o0aO2YsUK1yH46quvbP369S5Il1q1atmSJUssa9as9s0339gVV1xhDRs2dIF6OD6fz+6++253f9WqVbPevXtbsWLFbNu2bW5gvUGDBrZ06VI3OJ9e3XXXXXb77bdbbGyspWVvvPGGe2/27t3rOm3hAtX0QO/Fm2++aY8++mic7e+8884537cG0dXBbdmy5TnfFwAASD+xtJ/+/vnnn23VqlVWs2bNONdNnz7dXa/7iT/QV79+fStVqpR17drVxWxKCNBjPffcc/bggw/G2f/yyy+3Pn36JDhOJbIk5uuvv7amTZvaoUOH7M4773SD57J69Wp75pln7IsvvrBPPvnE0rNNmza5pIu0TMkf77//vuuLKa7Ve6OB5PRIbeLtt9+2559/3iVkBdNzD9Vmkkpta8iQIe7EV/78+VPoiAFECoPoACJOWeI1atRw/6/BU2WMjxgxwt577z2X4SDqGAQPjvvP+J/J6NGjXSeiZ8+eNmbMmDj7K2vg3//+t8vYSY8OHz7ssnYyZ87sLmmZ3s8ZM2bYHXfcYb/99pvrwKXUIPrp06ft+PHjCTqQ0aSOYahBdL0GzZo1c0E6AABASsbSfhdddJGdPHnSxSLBg+gaBFSSSahYZNiwYZYvXz43wB1/oG/nzp0JjkdZ6hoATy5l47Zq1crFruvWrXOZ6PGP45VXXrH0SPGw3gPNSkzryS+iz8ipU6dsypQpdv3117uTG5oxnJL9mLRCMyjUlj766CO7+eab4wyAq+/SunVr4ncgg0rbpzsBZAjXXHON+/eXX35JcN3BgwetY8eO9tBDD7nppOH8888/Nnz4cBd8jxo1KuSAu7K0gzsPv/76q7Vt29YKFizopo5eddVVNn/+/Di3Wbx4sbuvt956y2UNqJOQJ08el/27f/9+O3bsmBu0L1KkiOXOnds6d+7stgXzl+vQYPGll17qBniVZaPgM9gff/xhDzzwgNtHAXWhQoXc8cWvV+efyqtMfe2vx77gggviXBd8G2XyNG7c2HWwdL86QaGM/fjBqzKISpYs6QJ5HYNeRwX4oZ6LSohUqlTJ7atyOwsWLLCk0mwAHZ8y5nXR6/DXX3+FHBBXtlPlypXda1a4cGEX1Or5hHptdRw6Hv+xqDOmTmbevHnde6OZCMqgCnbixAn3vmqasx5Dr3ndunXd9Ga/7du3u/dVr7Huv3jx4i6gTmodQZ0s0GyKjRs3xrlPlRfSdaHoMzRo0CC7+OKL3WPqfdEgfPBnS89d79u0adMCJ5ni19NU59Sf6aLOsJ7HkSNH4uyjjvXQoUNdJ1uPpQyjxx9/PMHnWJ8FTavW66D2ogy177//PsGxJ+U1BQAAkYmlpV27djZr1iwXW/kpq1gxQfxBd//9KK4KlSmruDOlvPTSS64MjZJf4g+gS9GiRW3AgAFxtik72B/zKdO9W7duCUpjqIyN4tTvvvvODfQqblFM5S9Zoxhas14VFyvm/fTTT0OW5FPsptdHsaTiGfVH4mcgT5061Q0q63XRMVWsWNFeeOGFBM9F8dVNN91kH3/8sTsBosfW8/dfFxzDJTWWUiyp914D0HqvFJ+qZGKo56LZCInFhGeiWFvlfBT/VahQwf0div81U9zuf32VzBT/eH744QcXBxcoUMA9t+TEpEnp26g0qPpb6rfp/VN/Qv2KpFB/79prr3UJL/FfA92PPluhrFy50vVV9PrqM6fPnvo9wc/9kUcecf+vY/bH7/H7FEnpZyWlnyOK1fX51OukGF6xfPD3QHJeUwBkogNIA/yBg4Ko+APjKlOhoHfkyJFnvA9NYd2zZ48b0E5KJrZqL6qsi4LHHj16uOBUg5EtWrRwAbayYoJpgF4BRd++fV0QqnqQKjWjqZcqSaKgyD+dVkHHwIED49xewbo6L3osBUTqACjI0tRafyCmbB9lOGhgWUGOXhcF4eoIKNBUMBZMA+gKUPVYGkwNRdlCjRo1cvvp2BU4636DS4locFTP+/PPP7cuXbq4KbkK8BXkqWMzduzYBK+1bq/HV2A6fvx4l5GxefNm9zomRgGoguMrr7zSPXc9L2VH+YNKPx2LXk8FiMqyUmD95ZdfutfZn33l70DoJIcG0xX4KeBWwKhOhQJLDT7rvVJHRa+lv+Mket/03ur+dYJFU1UVRK5du9Z1FETPTfenqcu6b72m6sTo+Z6pvJCfgnC9nwrENf1a9FlQwKvsr/gU2Or90Ot87733uo6K6pLqffjxxx8DNdA1s8J/3NpP9LoGUydGn0c9Rz0nrS2gTp6y1fx0H/rs68SQTqSoA6D91QlThpqfPmcKvJVZr4vuT58tZf4HS8prCgAAUj+W9tNgpX6flRyiATVRXKKBt1CD4kpcWb58uSsPE27AMJgGfUPVh9bgruLncJTtq+sVgySFnoMGl1Xi8f7773dlUBQrK4bWYKXiPT/F5xq0VlytpBTtp/9XHKr+wn333edeF/Ux9PgqV6O4Nn4cpVhPcY3iT8W8ut/XX389sI/uVwOdit0041UnJxQjK57TAH8wHa9OaPzrX/9yZXI0wBzueSYWS2ngXzHyhRde6PZXv0n9kzp16rj94seoSYkJw9m6davrJyheFD0HxaUTJ06MU+5EJy0Uf+t9UGyqY9AJGb0mmlUQTO+JThKoNKE/aScpMWlS+jaK03WM+nz7n5/uQ58RnQhJCn02tK/KDClmVz9k9uzZrlxoqFIu6o/o/dDAvRJh1Ef0n2BR/0Xv4y233OJiefV79Pqp3yJ6LsnpZyW1n6OkHZ300LHrtVJ7fPnllxO0yaS8pgD+xwcAETJ16lRFSL5PP/3Ut2vXLt+ff/7pmzNnjq9w4cK+2NhY97ffkSNHfA0bNvS1b9/ed+LEiUTv+7nnnnP3PXfu3CQdS8+ePd3+X375ZWDbwYMHfWXLlvWVKVPGd+rUKbft888/d/tVqlTJd/z48cC+7dq188XExPhuvPHGOPdbu3ZtX+nSpeNs0+11Wb16dWDbH3/84cuePbuvVatWcZ5zfMuXL3e3ff311xO8jnXr1vWdPHkyzv7+63777Tf3t14P/f3111+HfS3mzZvn9nnqqafibG/Tpo17jj///HOc55ItW7Y427799lu3fcKECb7E6DUsVKiQr3///oFtd9xxh69q1apx9vvss8/cffbo0SPBfZw+fTrO8WTKlMn3/fffx9mnZcuW7jh/+eWXwLatW7f68uTJ47v22msD2/S4zZo1C3u8e/fudY8xcuRIX3INGjTI3Vaf9Ycffth38cUXB6678sorfZ07dw48h27dugWu+/e//+2eU/BnU1588UW379KlSwPbcuXK5evYsWPYx7777rvjbNfnTa+/3zfffOP2u+eee+Lsp+PVdr0PsnPnTvd66rUKfv0ff/xxt1/wMST2mgIAgNSPpaVevXq+yy67zP1/jRo1fF26dAnEN/pdnzZtWiDWnT17duB2n3zyiS9z5szuotj20Ucf9X388cdxYmE/xb3+WDf+Zfjw4Wd8PgUKFEgQA4bjj0UaNWoUiNNl4sSJ7rGmTJkS53lr24wZMwLbNm7cGIgbV6xYEdiu56Xtem3jx1EtWrSIcwwPPPCA267Y90zxe+PGjX0XXnhhyNdpwYIFCfbXdcmNpS6//HJfkSJFfH///Xdgm45Lz69Dhw7JjgnPZNSoUb4cOXL4Dhw44P7+8ccfQ/a7FGMr1lY/J1hw7Og/HvWlgiU1Jk1K3+ahhx7y5c2bN0E/KSn8cfmePXvc501xucyfP9/1i37//fc4Mb7/+ZUrV86978HPVZ8N9S1vuOGGwDb1KYL7avEfOyn9rKT2c/z93ZUrV8ZpR/ny5Ut2fxHAf1HOBUDEKXtEZ7pVokKZBjorrkwUf0kSUcarzugrK0T768y6MmLCUYaGxM8gCefDDz90GQH+6YOiLANlTejMuzK/g3Xo0CFOdovO8PsXMg2m7TpmnfEPVrt27cBCSaKFmjTlUhnfqi8owVkByuj5+++/XRa+sgGUMRKfMlgSy7r3T8P94IMP3H2Gey10P8qSD6YMED1H1QMMpvcjOOO5SpUqLhNC5XESo/vS81J2iJ/+/9tvv41TGkR1BjW9UZkc8cUv1aOpkpo666fXUwtQaRaDsnP8VIZFWSXK8PB/XvT66HF/+umnkMer90QZNsrcUubR2dLjagaDMqX8/4Yr5aIsF2Wfa1qzsrr8F3/mmDKBkkpZVsGUtaLX3//89d6LsmqC+RcH85c3UraTMs6VjR/8+iuTK77EXlMAAJD6sXR8ijuUWarfc826VOwXf+aln7KdFXcru1ox2rPPPutKPajMhR4nPsW/yv6NfwmO90JRPJLU2N0fiyj2CF6EU/Gw4tD4JRkV1yvz3E9Z34pRFGP5M3X9xy6h4tj4meT+BVX98VP8+F2lHhWzKTbV/envYMoE1+uYmMRiqW3btrlSgSrPorKUwTG53rvg40tqTHgmyt7X7En/e6UMcvVrgku67Nq1y5VoVN9I/Zxgocpsxj+epMakSenbaB/N0j2XUoKa1aFZw8oa98/c0CzmUOVF9V7ovVIb02vqj911DMqG1+sSqoRKKIn1s5LTz9FrqnKlweVM9b3Rvn37OI+ZlNcUwH8xiA4g4iZNmuSCGgXwKguhICP+gjqa8qcgQVPSNICpiwaiw1Fw4a+hnhSqPx5qCqUCa//1weIHg6p1J+q8xN+uICl+0KxgM75LLrnElZNR0CmahqmSGf665Jrip0BHdR7j31/8xVfDURCvKYCa+qr708C9phYG1xbUc1VNyfidmKS+Fv5AMymDzG+88YY7bj0/DSbrokBRJV2CA3FN/dQxBXcMwon/Ouj11Osa7v3V+6MTHaLyKnp99V6oxqFKymgqqp+OU9NANfivupwqzaKOpKZHJke1atXcoLgCcD3PYsWKBQbF41MQro6T3vvgi44x3IJe4cR/r/zTvP3vld5bdUR1siaYjk8Btf+99/8b/3Os44o/dTyx1xQAAKR+LB2fBpQVTyqmUSyiUidnGsBW2T0NuitmUPnBfv36uThbg/bxk00UY2rwL/7lTOsZ+eP35MTuEj++U7KDBhPjx6s6oRB/8FZxeqjYXULFsfHjHsWsipuCa1irRIieq78uuWIj1fGWUIPoSZFYLBXutfDHuv4B3OTEhOGoDIrqb6tMjD9210UJThp09Q/Y+gd5k1L+J9RrkdSYNCl9G5VC0Wun8ir6HGhgPznrN/lpUNpfwlHlFMMlwPhPdmgdr/jxu8rm6NhC9eVCSayflZx+jl6zUH3Q+LdNymsK4L8YRAcQcTobrmBTP9bKZlGwpaBENefOln8xItWOTg3hMr7DbY+/IGdSKLtFJw9Us1A1vpVloMBN9e9CZS+cqcaknzoP6mApm0g1w1XjXIGkskfO9vU+2+esIFs1EbWqvQI6/0VZ5AoGNcB8Nq9bUl6HcDQorgH7KVOmuM+hAt0rrrjC/eunjCfVL1RNRi3u9MQTT7ggVR2K5NBnXLXQ9Txvu+22OFlUwfReq8MUKqNLF3UMUvq9CpUhlJqvKQAAiGwsrUxVDXyOHj3aZcaGGxCMT4PUGlBX7WrV/1amqmbNpQTF74qx4q+vklZj9/jxkuIdZRpr0FqLoypbWrFar1693PXx4/ekxqypEUud7fNWAozoOQXH7/ocqTa4Zo+ejXCvRWIxaVL6Nqr1ruxwtQ3/uk8aUNcgd3Lotjo5pdtpQDnUIrzB77Pq64eL3zUzIilSsm+ZVKnRXwTSKwbRAUSVAgUNTmrBGi1Oc7ZUlkVn6TXlzl8e5UyUGaPFfUKtKO+/PiWFmo6pToMysP2LySh4UZCmoFRZPpqOqeelTJRzpal8GqDXokTKPlKms1at9z9Xvf7xM4FS+rVQNpOCbXXA1PkKvqh8j7Il/CvYK9NHx6TFYpNLr6de13Dvrwavg7OQlO3euXNn99lR5oamTWqBpmA6Hk0n1YkNLbKlzp7ep+RQZ1XTb/W+n6njqsfS81anLFRWV3D2yLkOfuu9VeAf//OphXf1ufO/9/5/4++nbJhQGUxJeU0BAEBkY2nFH1rkUBngymBPLv/C7opnUkLz5s3dTMykDMT6Y5H48Z1iMiVopHTsHiruUQa24ib/op1KDtHgqgZrtVioXlPFaueS4JGUWCrca+GPdZVNrMz4c6WBWyV/aHHK+LG7Ljom/0xSf2kRxcmpGZMmpW/jP/mjz9fzzz/vTkjo/dGCsHoPk0rvo8qmaEa0+mX+hUDj85dfUbsKFbvr4i8Leq6xe3L6OXrNQvVBQ902Ka8pAAbRAaQByopRRs24ceNCrnaeFAomHnvsMTflUP+GOluvTApNRxUFufr/4DrrmvaoFcsVGAfX2E4JepzguuYKht999123Ero/40D/xj/uCRMmJOmkQDga4Ix/n5dffrn71z9FT6+FHiN+x0urxivQU+ZGStDrrwBbNRB1kiD48vDDD7sMDX8grswqHbemFSY3E0Ovo15Xvb7B020VhKsjoBMT/vI/qlsYTMegaaT+10YZ8vE/kwqUNf05uVMcdTt9xtXRDa5NGJ+yXJQB8sorryS4Th3N4Om56iCdy0kWfwdaxxVM2VSi+pfiD/71eQx+/ePfLimvKQAAiE4srZhL681oYFGDjOEoczdUvOWvWx2qlMTZUEyoDHklKijJID6VsFOihT8W0TGPHz8+zrFNnjzZlcrwxywpXTYnmOIg8cfG/hg++Hh0LCqFcS4Si6X0mimenzZtWpw4UAPYSvg4mxMkoSi5RbG0BvPjx+66aGalPis6gaPBXWXQK3te5U+Sm0Wd1Jg0KX2b+K+fBpY14B+8T1Kpj6I2o5mo4ShjW3H+qFGjQmZu+0t3iv/kxtnG78np5+g1XbFiRaD/6z+W4BKaSX1NAfxXlmgfAACIav21bdvWXnvttQQLzSTnPnTGXBnCCugU3KmOnupXq46dAohly5a5ffv27esyOxQEa0FNZXsoEFUmi7JhwpXaOFuaiqmFhPRYmhaozosEDxKrNuW///1vV5tRg/gaeNciSirncrb0nPRYWjhKwZ2yzTU4G5yBpCwNZZj079/fBWNVq1Z1AbiCM5UyCV7c5mwpuNZ7En/xUj+9Jnp9lNWizpGO56677nL/rwwKLeyj7BRlT+k6TTU8E3W4NHVSgaTKn2TJksVeeuklFwiqprmfXmd1PBX86jOgzAvNCPDfvzp0ygjXwLb21f3MnTvXBarBi1Ul1UMPPZToPnreKuejdqDXTDUodZJD2SXarsVo/ZlgOm59RtTBUA151ZcMXiwrMXqvNftBJ48UzKsmotqJPjfKvNFrLeoYqROhEwD6nOqzo3I2qqsaPysnsdcUAABEJ5ZWjJmUmWEqMahEAsWPKrmibG/F0CpLp2QTDaoG08l/f9mP+IO/iifC0SxSxVWKKzRod+edd7r4QZR8oljdvyaSYhHVZVfsrLhQpTaUUas4V+VmdNuUpn6BHkePp7hcz1HZ/IqfRIOZ/oxnZTprAFVxtsqJnEu2flJiKZUOUT9Gr0+XLl1cooUG+ZP6HieFBls1aBvuBIVeG/UflK2sBUEVtyv2VumZe++918Wl6luozI3Kq6RETJqUvs0999zjZnVq/SHVRNdsV702+oz513xKKh2X//0OR/1GldrR+3HZZZe59qFFeNUuFMvr2DRrQfyfb71u6ksoSUWfn+TMHEhqP+fRRx91fUt9ftUH0WPo9VWGenCN/aS8pgD+xwcAETJ16lSd4vZ9/fXXCa47deqU76KLLnKXkydPntPjzJkzx9eoUSNfwYIFfVmyZPEVL17cd9ttt/kWL14cZ79ffvnF16ZNG1/+/Pl92bNn99WsWdP3wQcfxNnn888/d8c8e/bsJD2XQYMGue27du0KbNPf3bp1873xxhu+cuXK+WJjY33VqlVz9x1s7969vs6dO/vOO+88X+7cuX2NGzf2bdy40Ve6dGlfx44dE33s4Ot+++039/fatWt97dq185UqVco9bpEiRXw33XSTb/Xq1XFud/DgQV+vXr18JUqU8GXNmtUd58iRI32nT5+Os5//ucQX/xjjGz16tLvtokWLwu7z2muvuX3effdd97c+BzqG8uXL+7Jly+YrXLiw78Ybb/StWbMm0ePxP3e9hnotc+bM6atfv75v2bJlcfZ56qmn3Puuz0COHDncYw0bNsx3/Phxd/3u3bvd/Wt7rly5fPny5fPVqlXL99Zbb/kSE+qzEEqo56DHHzFihO+yyy5z71uBAgV81atX9w0ZMsS3f//+wH76fFx77bXu2HU//vcg3GPH/3zIiRMn3P2WLVvWvfclS5b09evXz3f06NEEbVT7qT3p8a677jrf+vXrE7z3ib2mAAAgMrF0vXr1XCxxJqFi3Y8++sh39913u99wxVGKwy6++GLfgw8+6NuxY0ec2ysO0O1DXXRdUmzdutXFoZdccomLyRW3Ke5R/BAc98jEiRPdcSlmKVq0qO/+++93MXSwcM9bx9OsWbNEYzF/HPXDDz+4vkKePHlcLNa9e3ffP//8E+e27733nq9KlSruuMuUKePitylTpiSIt8I9tv+6s4mlPv30U1+dOnXcPnnz5vU1b97cHXOw5MSEwfRYhQoV8l1zzTW+M1H8qH6Nn2LDVq1aBfpXl156qe+JJ55I9HiSGpMmpW/j7wvqOn12te+//vUv37Zt23yJOVPfIrHnsG7dOt8tt9ziXjcdm97XW2+9NUH/Z+jQob7zzz/flylTpjjvQXL6WUnp58h3333n2oPeCz2mHnvy5Mln1V8E4PPF6D/+AXUAQMpTSZRu3bqdU813AAAAAKlPmdzKeFfpi3B1sAEAGQ810QEAAAAAAAAACINBdAAAAAAAAAAAwmAQHQAAAAAAAACAMKiJDgAAAAAAAABAGGSiAwAAAAAAAAAQRpZwVyDpTp8+bVu3brU8efJYTExMtA8HAAAAHqQJogcPHrQSJUpYpkzkupwJ8TcAAAAiGYMziJ4CFMCXLFky2ocBAACAdODPP/+0Cy64INqHkaYRfwMAACCSMTiD6ClAGTD+Fztv3rzRPhycRSbTrl27rHDhwmR9AamEdgZEBm3N2w4cOOAGhv2xpZd98cUXNnLkSFuzZo1t27bN5s6day1btoyT8TNo0CB75ZVXbN++fVanTh174YUXrFy5ckm6f+LvyOF7BfAu2i/gbbThtBWDM4ieAvxTSBXAE8R780vp6NGj7r3jSwlIHbQzIDJoa+lDeihPcvjwYatatardfffddssttyS4/tlnn7Xx48fbtGnTrGzZsvbEE09Y48aN7YcffrDs2bMnev/E35HD9wrgXbRfwNtow2krBmcQHQAAAECKuvHGG90lFGWhjxs3zgYMGGA333yz2/b6669b0aJFbd68eXb77bcnuM2xY8fcJThjyN+51AWpR6+v3jNeZ8B7aL+At9GGIyOpry+D6AAAAAAi5rfffrPt27dbw4YNA9vy5ctntWrVsuXLl4ccRB8+fLgNGTIkwXZNcVaGFlK3Y7l//37XiScLDvAW2i/gbbThyNCioknBIDoAAACAiNEAuijzPJj+9l8XX79+/ax3794JaleqRijlXFK/A6/pzdRjBbyH9gt4G204MpJSSlAYRAcAAIigU6dO2YkTJ1It0NZ9KzOXQDttypYtG+/NWYiNjXWX+PRa8nqmPnXgea0Bb6L9ApGPyVMytj958qQdP36cNnwOsmbNapkzZw57fVJfWwbRAQAAIkDTMJVlu2/fvlR9DAXbmpKYHhanTI8UpGshTQ2mZ1TFihVz/+7YscOKFy8e2K6/L7/88igeGQAASO8iEZOnFGL7lJM/f34Xg57L68ggOgAAQAT4g/UiRYpYzpw5UyUQVqCtbJUsWbIQaKdB6gRt3brVtm3bZqVKlcqw75FOIqgTs2jRosCgucqzrFy50u6///5oHx4AAEjHIhGTpxRi+5R5DY8cOWI7d+50fwcncCQXg+gAAAARmC7qD9YLFSqUao9DoJ32qaalBtL1PmlqaXp16NAh+/nnn+MsJvrNN99YwYIF3QmEnj172lNPPWXlypVzg+pPPPGElShRwlq2bBnV4wYAAOlXpGLylEJsnzJy5Mjh/tVAut77M5V2ORMG0QEAAFKZv96isl2QsfnLuKgTl54H0VevXm3169cP/O1fFLRjx4722muv2aOPPmqHDx+2e++913Vm69atawsWLEjywk4AAADJRUyeceX833uuzwCD6AAAAGkcGSTIKJ+B6667zmVPnel1ePLJJ90FAAAgkjJKPIaUfc9Z2hUAAAAAAAAAgDAYRAcAAAAAAAAAIAzKuQAAAETRsTHTUvT+VELj9BmmK8b27mjRVqZMGbewpC4AAABAeovJzyQtxONCTJ48DKJ7XCQbeSSllS8UAAAyssRqBw4aNMgGDx6c7Pv9+uuvLVeuXOdcc3vJkiXu/2NjY61UqVLWuXNn69u3b+C4H3jgAfvpp5/sr7/+soYNG9qECRPO6TEBAACASMuUKZOnY/Lff//dypYt657H5s2b7fzzzw/cftu2bVayZEk7deqU/fbbb25gX+bOnWsjRoywDRs22OnTp9393nDDDTZu3Dh3vRaq1+PEp2M4evSopQYG0QEAABCSglq/WbNm2cCBA23Tpk2Bbblz546TAa/gN0uWxMPLwoULp8jxde3a1S1MeezYMfvss8/s3nvvtfz589v999/vrleQnS1bNnd90aJF7emnn7Y8efKkyGMDGcmmZaPtd992i7Hwi8WmJ7VavxHtQwAAIGDr1q2BAWkvxuR+Gjx//fXXrV+/foFt06ZNc9s1uO63aNEiu+2222zYsGHWokUL99x/+OEHW7hwoQXLmzdvnNchtReN9VxN9EmTJrmzEtmzZ7datWrZqlWrzrj/7NmzrXz58m7/ypUr24cffhjn+kOHDln37t3tggsusBw5cljFihXtxRdfTOVnAQAAkPYVK1YscMmXL58LSv1/b9y40Q1If/TRR1a9enWX9fHVV1/ZL7/8YjfffLMbtFZAf+WVV9qnn34a534Vy/mzSET3++qrr1qrVq0sZ86cVq5cOXvvvfcSPT7tq2MpXbq0y0SpUqVKnOBaA+gnT550GekMoAMAAMCLvB6T+3Xs2NGmTp1qwfS3tgd7//33rU6dOvbII4/YpZdeapdccom1bNnSjQkHC34d/Bc939TiqUF0nW3p3bu3m6awdu1aq1q1qjVu3Nh27twZcv9ly5ZZu3btrEuXLrZu3Tr3guuyfv36wD66vwULFtgbb7zhpgioDpAG1ZPyIQEAAMjoNFXzmWeecXGUAmYlKDRt2tRlkCj+atKkiTVv3jxOdkkoQ4YMsVtvvdW+++47d/v27dvbnj17knQMyrj58ssvXSdCA+fBmfSK/XQMGkgHAAAA0qO0HJP7Kat87969bpBf9K/+1nEF02D4999/H2f8Ni3w1CD6mDFj3BQBndXwZ4zrbMeUKVNC7v/cc8+5D4nOXFSoUMGGDh1qV1xxhU2cODHOQLvOeKiGj87AaMqBBucTy3AHAACAuambqk940UUXWcGCBV0c9a9//csqVarkslcUf+m6xBIUOnXq5JIfLr74Ypc1rsA/sXjs+eefd5k1yri59tprXb3EHj16BK5XHKgOwOjRo+2qq66yX3/9NcWeNwAAAJBWpOWY3C9r1qx25513BsZx9a/+1vZgDz74oMucV0URjdXefvvtbl+Viwm2f/9+97jBlxtvvNFSi2dqoh8/ftzWrFkTp26OCtJrkajly5eHvI22K9M8mDLX582bF/j76quvdh+gu+++20qUKGGLFy+2H3/80caOHRv2WPSmBb9xBw4ccP/qQ6JLJEX20SInkq+jHktnyyL93gEZCe0MGZ2/Dfgv0XS2j++/Xfx/NW00+D4VaGthI5XQUya4yqn8888/9scff8TZL/5roSDZ/7eSJFTjcMeOHWc8XmXGPP744y6DRY9Zu3Ztd/Hf5ptvvkmx559S/M87VNzIdyQAIKWxpgGQMdSoUSPO3/6YfP78+XFi8sQy0ZXF7qdFRxWTh6sAEhyT9+/f38Xkqh6isVZdQtH4q67TAL1KcGvsVscWTI+r41ZJms8//9xWrFhhffr0ccnS2l99BVEZG1UqCaZS3ZbRB9F3797tCuPHr22jvzVNIJTt27eH3F/b/SZMmOCyz1UTXUX3NTD/yiuvuDMn4QwfPtxNb4hv165dqbYCbDgncnrmLUyWrIk00JSkDqvOXqlDm9iKxwDODu0MGd2JEydcO1CAGD9ITOlB3f/eXfj7jP/4SeUf4PXfXnGZKOMk+D4V4GraqKaTKttFgayyR5SAELyf//Xw03dD8N+qcRjq9fr/5+lzgbOyU3SZPn26m3morJUGDRpYWqXno+f+999/J8i6OXjwYNSOCwAAAGnP0UM6CXbi//8+vNPMd9oO7f3vDMt/Dm51//pO7LJDe/8/4feh3gPs88VL7akn+9pFF2ptyVi7q1N3O3xgV+C2vtMn7diRvwN/y8ljcf/WCbgjB7fH2Rbs1MmjljPWrFihTFasUCGb8tIIq1rjeqt6WWmrf10dt8/h/X+5f48c+MuqVK5ol5Qra7e2bWmXXnKhlTk/p333nx/+t58G+MsE7lt9CV3uueceN0iv2ugq9a0KJf7+gzLmIyV9jsAmgwbRdUZD2egqgP/FF19Yt27dXFa6stxDUTZ8cIa7MtFLlizpVrXVGZpIOnbk7DrCaV1skSIReyx1ZNVR1/vH4B6QOmhnyOh0kl0DpDphr0uw0ym+grzvjKvSx3/8pPK3Xf/tM2fOHPg7+D6VHaJSeW3atAlkwSgLXccUvJ/uL/hv3V/8Y4u/TzDdX/B95s+f300bVT1IZaSc6TWIJn/SRqFChdzC98Hi/w0AAACcjRUr11j7drdYi5sau78PHTpsmzf/ZVanVqo+bu7cuez+f3Wy/gOH29Il74eMye9q39Z6PTzQxo0emuT7VdKMMtAPHz5s0eKZQfTzzjvPda40rTeY/lbB+VC0/Uz7axqDpgDPnTvXmjVrFpi2oKm/o0aNCjuIrowrXeJThyjSg0PpdSgq0q+jGnU03j8gI6GdISPT594/6Bvtwd2zfXz/7UL9G3yfqrmo2EoLB2n7E088ETiRFrxfYn+H23am6++77z576qmn7J133gkM4qc1/mMO9X3I9yMAAABSwkUXlbH3PvjEbmzSwMWeQ58ea6dPR6as092d2tmzoybau+8tsJY3J6xR3qnDbW57/nyhE5FVhubIkSNuYVMlPO/bt8/Gjx/vZveq7nvwzNTgaiN+RYoUSZW42jOD6FrVVTU3NT24ZcuWbps6ZPq7e/fuIW+jmpi6vmfPnoFtCxcudNtFL74u8V9YDdZTkxIAAERCbO+OKXZfCiRVLkTZztEarNdC8P5ah0qCeOyxxwLrx6Q2LaLUoUMHF3jfcsstDEoDAAAg4jF5WjD8qf72wIOPWcMmba1QwQLW66F/2cGDhyLy2AUL5Ld2t7Wyp0c8Zy2a/zcTPpj6KucVKhj29vXq1bNJkya5uF7J0AUKFLBq1arZJ598YpdeemlgP/UxihcvnuD2qgEfLuH6XMT4or26UjKo7o2mB7/00ktWs2ZNGzdunL311luuJrpqnevFPf/8813Nclm2bJl74VWTU5nmM2fOdIXrNcVXq9PKdddd5+qtT5w40Z3dWLJkid1///2uA6h/k0JvWr58+Vy934iXcxkzzdKjSH556YSJFklIrTNVAGhngMq5/Pbbb1a2bNlULdmRFgbRcfafhWjGlF7DaxXZ3/Av5z1msSxMCHgO7ReITkyekrH9oX1/xamJnt7lLnBhmo3BPZOJLrfddptbvHPgwIEuXf/yyy+3BQsWBBYP1QqzwYMzyoCaMWOGDRgwwJVt0fTiefPmBQbQRQPrqnGulWT37NnjBtKHDRvmpgMDAAAAAAAAADI2Tw2ii0q3hCvfsnjx4gTb2rZt6y7hKL1/6tSpKXqMAAAAAAAAAID0gTn1AAAAAAAAAACEwSA6AAAAAAAAAABhMIgOAAAAAAAAAEAYDKIDAAAAAAAAABAGg+gAAAAAAAAAAITBIDoAAAAAAAAAAGEwiA4AAAAAAAAAQBhZon0AAAAAGdnKt+9M0fvz+XwWExMT9vpard9I0ccDAAAAvC6lY/IzIR73JjLRAQAAEJIG4890GTx48Dnd97x585J1DHnz5rUrr7zS3n333cD1hw4dsiZNmlj9+vWtQoUK9tprr531MQEAAABpTZ6CF53x8vQzz53Tfb8//5NkHUOJUlWtXoOW9sGHC+Ps88aMOe766rUaJbj93Hkfuusuq3ptYNupU6ds9LgX7YpaN1jhEhWt1IVXWK1atezVV18N7NOpU6eQ/RDF/5FGJjoAAABC2rZtW+D/Z82aZQMHDrRNmzYFtuXOnTsixzF16lQXKB84cMCef/55a9Omja1du9YqV65sOXPmtA8++MCyZMliX331lfXp08cF2wAAAEB68POGFYH/f3vuBzZs+Dhbu+rTwLZcuXJG5DhemDjCbmhQzw4cPGSvTn7D7urU3b5a/J5dVvHSOMeya/fftnLVWqtV84rA9tffeMtKXlAizv0NHzHepkx700aPGGzVqlW2gwcP2Q8/bre9e/fG2U/9APUHgsXGxlqkkYkOAACAkIoVKxa45MuXz2V9BG+bOXOmy/7Onj27lS9f3g1w+x0/fty6d+9uxYsXd9eXLl3ahg8f7q4rU6aM+7dVq1buPv1/h5M/f373eJdccokNHTrUTp48aZ9//rm7LlOmTG4AfefOnW6Q/7nnzj4TBwAAAEhrihYtHLjkzZvHxc/B2+a884HL/j6veAWX1f3K5DfixOR9Hh1sF1e4yl1fsco1NmrsC+46f1b4HXfdnyBLPJR8+fK6xyt3cVkb8HgvF5N/8eXyOPtkyZzZbm3T3P49fXZg25Yt2+zLpSutbZsWcfb9cMEi63r3ndaqZVMrU7qkVa5Uwbp06WIPP/xwggHz4D6ILgUKFLBIIxMdABJxbMw0S69ie3eM9iEA8Kjp06e7QeuJEydatWrVbN26dda1a1fLlSuXdezY0caPH2/vvfeevfXWW1aqVCn7888/3UW+/vprK1KkSCDDPHPmzEl6TAXqkydPdv+fLVu2wPYVK1ZY//793WNWqlQplZ4xAAAAkLbMmv2uDXtmnI0aMdiqVqlo3373gz3Y83HLmTOHtW/X2l54aZp9+NGnNm3KBJcJ/teWbbZly1Z328WL5tqFl9QMZJhnypwpyTH562/MThCT+93Vvq01bX6HPTt8oDuO6W++bQ0bXGtFCp8XZ7+iRc6zJV8ut3u6tLfC5xWytI5BdAAAACTboEGDbPTo0XbLLbe4v8uWLWs//PCDvfTSS24QffPmzVauXDmrW7euy5ZRJrpf4cKF42SYJ6Zdu3ZuoP2ff/6x06dPu8z1W2+91V2nDPRrrrnGLr30UrvnnnvcfS5YsCDVnjcAAACQVjz9zDh7emg/u7l5Y/e3Mro3bfrJprz2phtE/2vLVrvoojJ29VU1XExequT5gdv6B679GeaJubtrz//F5EddTF661AUuizy+qlUuszJlStm89z6ydre1coPoTz/V337//b8JNX7Dn+pvd3bqbheXv8oqlC/nyr+0ufUuu/HGG+Psp9KN8ctIPv744+4SSQyiAwAAIFkOHz5sv/zyi5tuqezz4KwUlX0R1SW/4YYb3OC2ss1vuukma9Qo4SJDSTF27Fhr2LCh/frrr9arVy+XcV6wYEF3nTLaT5w4kULPDAAAAPCGw4eP2K+/bbZuPfrZgz37x4nJVfZFNJDe4paOVq1mQ7uhwbXWpNH11uD6a87q8Z4ZNsCuq3e1Gwzv23+YjXzmCStYIH/Ife9q38YtNKrs98NH/rHGN1xnL73y7zj7lC9fzlYt+8jWfbPeVqxcY0uXr7LmzZu7fkTw4qL169e3F174bwkaP39fIJIYRAcAAECyHDp0yP37yiuvWK1ateJc5y/NcsUVV9hvv/1mH330kX366acuc1wD4XPmzEn24ylb/eKLL3YXlYBp2rSpy3rXADoAAACQUQfRZcK4p61G9aohY/LLq1ay9esW28JPl9jnS5Zax7sftOvq1bE3pk1K9uMVKXKeXXRhGXdRCZjWt3Wx1csXWOF4ZVrk1rY32xODR9jTI8bb7be2dGsYhaL1japfUcVdut3f2ebNX2Z33XWXK9Woma6icpHqB0QbC4sCAAAgWYoWLWolSpRwmeH+wW3/xR/sSt68ee22225zg+2zZs2yt99+2/bs2eOuy5o1q506dSrZj12zZk2rXr26DRs2LEWfEwAAAOAlGtQuXryo/f775sDgtv+isi5+ykpvfctNNvG54fba5PH27vsLbM/efYGY/PSp08l+7BrVq1q1qpVs5OjnQ16vDPWmTRrYV0tXuhrpSVWxYsXAzNe0hkx0AAAAJNuQIUOsR48ernyLyrUcO3bMVq9ebXv37rXevXvbmDFjrHjx4m7RUWWYzJ4922WUq2a5qK75okWLrE6dOhYbG2sFChRI8mP37NnTWrVqZY8++qidf/7/13UEAAAAMpLHH3vIHu33pBsob9ignh0/ftzWrvuP7du33x7s1sUmTJpsxYoVtiqVL3Mx+dx3P3L1z/Pny+tuX6rU+bb4i2V2Va3qli02mxXI/9/SjEnxwH2d7I4O91vPHvdaiRIJ1zl6cdJIGzPqSStUMHScf2fHbu5xVQtdx/THH3/ak09PsEsuucTKly8f2E/9jO3bt8e5rTLbzzsvYQZ8amIQHQAAIIpqtX4jxe7L5/O5GogKKrVwUGrSIp45c+a0kSNH2iOPPOKmWVauXNkNcEuePHns2WeftZ9++slNJ73yyivtww8/dMG7aFFSDbYrS10D4b///nuSH1uD9sp4Vzb688+Hzn4BAAAAohGTR1KnDrdZzpw57LkJr9iAQSMsV84cVrHipW6AW3LnzmXjxr9iv/z6u2XOlMmuuKKKvT1rciAmf3ro4/b4gGH22uuzrETxovb9t18k+bFvaFjPSpcqaSPHPG9jRz2Z4PocObK7SziqzT7nnfdt9LgX7MCBQ1a0yHnWoGEjGzx4cJzyLwsWLHDJOcG07tLGjRstkmJ86m3hnBw4cMBlYe3fv99NW46kY2OmWXoU27tjxB5LKwrv3LnT1VX1f4kAGaGdRbKt0c6Q0R09etTVB9fAb/bs4QNJLw2iI+U/C9GMKb2G1ypy9Bv+5bzHLNa33WIsY3QdvTqQAsRH+wWiE5OnZGx/aN9fFmMnLKPIXeDCNBuDM5IBAAAAAAAAAEAYDKIDAAAAAAAAABAGg+gAAAAAAAAAAITBIDoAAAAAAAAAAGEwiA4AAAAAAAAgwyy6i4zldAq851lS5EgAAAAAAAAAII3Kli2bZcqUybZu3WqFCxd2f8fExFha5fP57Pjxk2Z2yjKKLEePpsJreNx27drl3nu952d9bCl6ZAAAAAAAAACQxmgQtWzZsrZt2zY3kJ7WaQD42D/7LCYDDaLH7tFJg5SXM2dOK1WqlPsMZJhB9EmTJtnIkSNt+/btVrVqVZswYYLVrFkz7P6zZ8+2J554wn7//XcrV66cjRgxwpo2bRpnnw0bNthjjz1mS5YssZMnT1rFihXt7bffdi8uAAAAAAAAAO9TJrLG+zT+d+rUqTRfgmT1p6Msm+22GPNZRlC+0cgUv8/MmTNblixZznnWgacG0WfNmmW9e/e2F1980WrVqmXjxo2zxo0b26ZNm6xIkSIJ9l+2bJm1a9fOhg8fbjfddJPNmDHDWrZsaWvXrrVKlSq5fX755RerW7eudenSxYYMGWJ58+a177//3rJnzx6FZwgAAAAAAAAgtWgwNWvWrO6S1gfRfScPmvn2Ki/dMoLsaXg81lOD6GPGjLGuXbta586d3d8aTJ8/f75NmTLF+vbtm2D/5557zpo0aWKPPPKI+3vo0KG2cOFCmzhxorut9O/f32WmP/vss4HbXXTRRRF7TgAAIGN7eEn7FJ/2eaYsi1H1pqfo4wEAAABAenf2hWAiTEXg16xZYw0bNgxsUx0b/b18+fKQt9H24P1Fmev+/XVGR4Pwl1xyiduubHZluM+bN++Mx3Ls2DE7cOBAnIv//iJ+0eOmx0uEX0cNOETl/ePijUu020M6aWu0My4Z/aI2EOoSaeGO40wX1Y3s3r27XXjhhRYbG2slS5a05s2b26effuriovPOO8/N/At12yeffNKKFi3qYrlQ12vA33/RjMArr7zSxWL+6w8ePOiSIurXr28VKlSwqVOnhj3OQYMGufvR/vGvU8KErrvuuuvO6jVI6Uu4zwkAAACAtMczmei7d+92tYrUCQumvzdu3BjyNqqbHmp/bZedO3faoUOH7JlnnrGnnnrK1UtfsGCB3XLLLfb5559bvXr1Qt6vOokq/RKfVno9msKryCbmRE7PvIXJknXnzog9ljqs+/fvdx3ac1lgAOlXem1nkWxrtDNkdCdOnHDtQLUXdQmW0gPpid1f/MdPjNaV0cBzvnz5XAykknh6Pprd161bN1u/fr3dcccdbnD74YcfTnAs06ZNszvvvNMNYId77FdffdUaNWrkEhNeeukla9u2ra1cudIqV67s6lbOnTvX1TFcunSpPfroo+7+QtFrXLx4cRfH6bgvuOCCwHWauaj6lzqm5L4GKUmPreP8+++/E0wh1gkDAAAAAGlP+h0ZSgJ/ts/NN99svXr1cv9/+eWXu1rqKvcSbhC9X79+rja7nzp8ysgqXLiwy6CKpGNHotcJTE2xIWrcp+bnQB17vX8M7iEjtbNItjXaGTI6nWTXAKkGgnUJdq4L3IRypvuM//iJeeihh9z9rVq1ynLlyhXYrgXe77nnHnd/+leLva9YscKtNeO3ePFi+/XXXwP7hVOwYMHAgLcSG3RfX375pVWrVi2wj5IflNWucn3h7kvfL5pZWL16dZs+fbor2yeK7TRo3aZNG7egfPDtNYCvkoG//fablSlTxh588EF74IEHAtdr8Xllxv/1119WrFgxd8Jg4MCBgQHwwYMH27vvvutiQ23fu3ev3Xjjjfbyyy9bnjx5EhyjHlvHWahQoQQ1H9NyDUgAAAAgI/PMILqmCWs11R07dsTZrr/VoQlF28+0v+5THZmKFSvG2UdThb/66quwx6JpzLrEpw5RpAeH0utQVKRfRw0OROP9gzek509FJD/ztDNkZPrcB5ctiabkPP6ePXvcLL1hw4ZZ7ty5E1xfoEAB92+VKlVcGRZlo19zzTWB61977TW7+uqrXWyV2DH5M9WVMS6KtfzHqsF5DYiPHz8+sDj8mZ7b3Xff7TLWBwwY4P7WcbVv3z7BfhpoVwkYrZejAft169a59Xf0XDt27Oj2UYKEnkeJEiXsP//5j7te23T//vvSQvUaSP/ggw/cIPqtt97qZjjqdQv3XEN9H/L9CAAAAKRNnonUNZVXWUWLFi2Kk9mov2vXrh3yNtoevL9o6rF/f92nOnybNm2Ks8+PP/5opUuXTpXnAQAA4BU///yzK39Svnz5RPft0qWLzZ4925XKE2Xez5kzxw1oJ6Zdu3Zu4FoD55odqIxwDUT7M9A1MK9ECGW0q955Ym666SY3U/CLL76ww4cP21tvvRXyODSAPnr0aFfKr2zZsu5fPb5KyvhpIF4nAnRMqgOvkjW6v2CKSTXQrgF+Hetdd92VIAYFAAAA4F2eyUQXTZNVVlCNGjWsZs2aNm7cONcx6ty5s7u+Q4cOdv7557t6nf7pxyrJos5Rs2bNbObMmbZ69Wo3vdbvkUcesdtuu82uvfZat2CVsq3ef/99N/0YAAAgI0tOvXYNhGsA2j9gPWvWLJdZrTgrMWPHjnWLwav0i+5DGecq8SIqz6Ia7MmhUiuqm64MdN2nFpFXtnwwxZDKINfgv7LL/ZQNr/rvfnoeOh7tqxMEuj5++T4NsAeXblFddg3+AwAAAEgfPDWIrk6YFu9UvUktDqr65Rr09i8eunnz5jjTYJU1NGPGDJdB9Pjjj1u5cuVcTcvgacCtWrVy9c818N6jRw+79NJL7e23345TzxMAACAjUuyk0iPhFnEPpoFl1RzXwLUG0fWvsslDlYGJT6X2Lr74YnfR7Zo2bWo//PCDG0A/WzqGWrVquYVPQ2Wh+zPmX3nlFbdfMJUQlOXLl7syMFpQvnHjxm5wXUkZStAIFn+BUL1m/rV3AAAAAHifpwbRpXv37u4SSqjs8bZt27rLmahjlZSpxgAAABmJssE1eDxp0iSXbBC8sKjs27fP8ufPH/hbWd3XXXedqw2uxTxHjhyZ7MfUbEOV8FM9cS0ierYuu+wyd/nuu+/cYqDxKQlDdc6VqR5cLz2YnoNK/PkXKJU//vjjrI8JAAAAgDd5piY6AAAAIk8D6KdOnXKD25qt99NPP9mGDRtciZP469KoPJ6yyVViT3XUNSvwbPTs2dPVJd+yZcs5Hftnn31m27ZtizPQH0wZ5pqNqOeiNXG0cKgy4ceMGRPIxNdMR2Wfq5yL9ps7d+45HRMAAAAA7/FcJjoAAEB6Mqre9BStYa6a3VmyZHElRVLChRdeaGvXrnWZ4X369HGD0oULF3bZ4i+88EKcffWYmt2nMnr9+vU768fU4qFa6FOP+fzzz5/1/cTPnI9PC5XmzJnTZcxrnRztX7lyZTeILy1atHA12jUL8tixY26NnSeeeMIGDx581seE/9KJGb2Ob7zxhivTqFkBnTp1cmUYU+qzCwAAAKSUGF9yVoxCSAcOHHA1Mvfv359goanUdmzMNEuPYnt3jNhjqWapFv9S3dXgmvpAem9nkWxrtDNkdEePHrXffvvNDQxnz5491R4nNQbREbnPQjRjykh7+umnXcb/tGnTXNmd1atXW+fOnd2JE5UOSkxGeq2iTb/hX857zGJ92y3GMkbXsVbrN6J9CECKoP0C3kYbjoykxpVkogMAAACIKNWbv/nmm112v5QpU8befPNNW7VqVcj9NRNAl+DOjr9zySKuqUuvr7rtPss4J+b4TCG9oP0C3kYbTluPySA6AAAAgIhSvfyXX37Z1aK/5JJL7Ntvv7WvvvoqUI8+PtWuVw37+Hbt2uWy+5G6HcuTVvB/s1syRhacZs8B6QHtF/A22nBkHDx4MEn7MYgOAAAAIKL69u3rssm1AG3mzJldjXSVcmnfvn3I/VVjv3fv3oG/dduSJUu6+vyUc0n9DvyPtsey+XZkmKnkKj8HpAe0X8DbaMORkdRymwyiAwAAAIiot956y6ZPn24zZsxwNdG/+eYbt6CrFhjt2DHheh2xsbHuEp/W2WCtjdSn/Dd13jNKB57PFNIT2i/gbbThtPOYDKIDAABECHU6ocVfYfbII4+4bPTbb7/d/V25cmX7448/XNmWUIPoAAAAQDQxiA4AANKEY2OmWXqVreddLsNh69atrvxEtmzZ/lfbMOUHaE+ePGlZsmRJlfvHub8/quGt9yZr1qyWkR05ciRB1o/KunCiCQAAAGkRg+gAAACpTIOFZcuWtW3btrmB9NQcpNUgpB6PQfS0Se/LBRdc4AaMM7LmzZu7GuilSpVy5VzWrVvnFhW9++67o31oAAAAQAIMogMAAESAss81YKhMcS2imBo0gP73339boUKFqAmaRikDPaMPoMuECRPsiSeesAceeMB27tzpaqH/61//soEDB0b70AAAAIAEGEQHAACIEH8Zj9Qq5aFBdN23VphnEB1pWZ48eWzcuHHuAgAAAKR19K4AAAAAAAAAAAiDQXQAAAAAAAAAAMJgEB0AAAAAAAAAgDAYRAcAAAAAAAAAIAwG0QEAAAAAAAAACINBdAAAAAAAAAAAwmAQHQAAAAAAAACAMBhEBwAAAAAAAAAgDAbRAQAAAAAAAAAIg0F0AAAAAAAAAADCYBAdAAAAAAAAAIAwGEQHAAAAAAAAACAMBtEBAAAAAAAAAAiDQXQAAAAAAAAAANLLIPqkSZOsTJkylj17dqtVq5atWrXqjPvPnj3bypcv7/avXLmyffjhh2H3ve+++ywmJsbGjRuXCkcOAAAAAAAAAPAaTw2iz5o1y3r37m2DBg2ytWvXWtWqVa1x48a2c+fOkPsvW7bM2rVrZ126dLF169ZZy5Yt3WX9+vUJ9p07d66tWLHCSpQoEYFnAgAAAAAAAADwAk8Noo8ZM8a6du1qnTt3tooVK9qLL75oOXPmtClTpoTc/7nnnrMmTZrYI488YhUqVLChQ4faFVdcYRMnToyz35YtW+zBBx+06dOnW9asWSP0bAAAAAAAAAAAaV0W84jjx4/bmjVrrF+/foFtmTJlsoYNG9ry5ctD3kbblbkeTJnr8+bNC/x9+vRpu+uuu9xA+2WXXZakYzl27Ji7+B04cCBwX7pEUmQfLXIi+TrqsXw+X8TfO3hHev5kROpzTztDUqTnTwdtDUnB+wYAAACkTZ4ZRN+9e7edOnXKihYtGme7/t64cWPI22zfvj3k/truN2LECMuSJYv16NEjyccyfPhwGzJkSILtu3btsqNHj1okncjpmbcwWbKGKdGTWh3W/fv3u0EHnZgBMko7i2Rbo50hKWhr54625m0HDx6M9iEAAAAACCH99laTQJntKvmi+upaUDSplA0fnOGuTPSSJUta4cKFLW/evBZJx46ctPQotkiRiD2WBhz0/uv9Y8ABGamdRbKt0c6QFLS1c0db87bs2bNH+xAAAAAAeHkQ/bzzzrPMmTPbjh074mzX38WKFQt5G20/0/5ffvmlW5S0VKlSgeuV7d6nTx8bN26c/f777yHvNzY21l3iU2c10h3W9No9jvTrqAGHaLx/8Ib0/KmI5GeedobEpOdPBm0NScF7BgAAAKRNnonUs2XLZtWrV7dFixbFybbS37Vr1w55G20P3l8WLlwY2F+10L/77jv75ptvApcSJUq4+ugff/xxKj8jAAAAAAAAAEBa55lMdFEJlY4dO1qNGjWsZs2aLlv88OHD1rlzZ3d9hw4d7Pzzz3c1y+Whhx6yevXq2ejRo61Zs2Y2c+ZMW716tb388svu+kKFCrlLsKxZs7pM9UsvvTQKzxAAAAAAAAAAkJZ4ahD9tttuc4t3Dhw40C0Oevnll9uCBQsCi4du3rw5zjTYq6++2mbMmGEDBgywxx9/3MqVK2fz5s2zSpUqRfFZAAAAAAAAAAC8wlOD6NK9e3d3CWXx4sUJtrVt29ZdkipcHXQAAAAAAAAAQMbjmZroAAAAAAAAAABEGoPoAAAAAAAAAACEwSA6AAAAAAAAAABhMIgOAAAAAAAAAEAYDKIDAAAAAAAAABAGg+gAAAAAAAAAAITBIDoAAAAAAAAAAGEwiA4AAAAAAAAAQBgMogMAAAAAAAAAEAaD6AAAAAAAAAAAhMEgOgAAAAAAAAAAYTCIDgAAAAAAAABAGAyiAwAAAAAAAAAQBoPoAAAAAAAAAACEwSA6AAAAAAAAAABhMIgOAAAAAAAAAEAYDKIDAAAAAAAAABAGg+gAAAAAAAAAAITBIDoAAAAAAAAAAGEwiA4AAAAAAAAAQBgMogMAAAAAAAAAEAaD6AAAAAAAAAAAhMEgOgAAAAAAAAAAYTCIDgAAAAAAAABAGAyiAwAAAAAAAAAQBoPoAAAAAAAAAACkl0H0SZMmWZkyZSx79uxWq1YtW7Vq1Rn3nz17tpUvX97tX7lyZfvwww8D1504ccIee+wxtz1XrlxWokQJ69Chg23dujUCzwQAAAAAAAAAkNZ5ahB91qxZ1rt3bxs0aJCtXbvWqlatao0bN7adO3eG3H/ZsmXWrl0769Kli61bt85atmzpLuvXr3fXHzlyxN3PE0884f595513bNOmTdaiRYsIPzMAAAAAAAAAQFrkqUH0MWPGWNeuXa1z585WsWJFe/HFFy1nzpw2ZcqUkPs/99xz1qRJE3vkkUesQoUKNnToULviiits4sSJ7vp8+fLZwoUL7dZbb7VLL73UrrrqKnfdmjVrbPPmzRF+dgAAAAAAAACAtCaLecTx48fd4Ha/fv0C2zJlymQNGza05cuXh7yNtitzPZgy1+fNmxf2cfbv328xMTGWP3/+sPscO3bMXfwOHDjg/j19+rS7RFJkHy1yIvk66rF8Pl/E3zt4R3r+ZETqc087Q1Kk508HbQ1JkdHety1btrjSih999JGbIXrxxRfb1KlTrUaNGtE+NAAAAMCbg+i7d++2U6dOWdGiReNs198bN24MeZvt27eH3F/bQzl69KgL5FUCJm/evGGPZfjw4TZkyJAE23ft2uXuI5JO5PTMW5gsWcOU6EmtDqtOnmjQQSdmgIzSziLZ1mhnSAra2rmjrXnbwYMHLaPYu3ev1alTx+rXr+8G0QsXLmw//fSTFShQINqHBgAAACSQfnuryaRFRlXWRZ3OF1544Yz7Khs+OMNdmeglS5Z0wf+ZBt9Tw7EjJy09ii1SJGKPpQEHzT7Q+8eAAzJSO4tkW6OdISloa+eOtuZt2bNnt4xixIgRLn5W5rlf2bJlPTETNKNxM1zMzGcxllHwmUJ6QfsFvI02HBlJfUzPDKKfd955ljlzZtuxY0ec7fq7WLFiIW+j7UnZ3z+A/scff9hnn32W6EB4bGysu8SnzmqkO6zptXsc6ddRAw7ReP/gDen5UxHJzzztDIlJz58M2hqSIiO9Z++9954rs9i2bVtbsmSJnX/++fbAAw+49Y/S+kzQjEYdy5NW0H23qBufEeyM4KxYIDXRfgFvow2nrdmgnhlEz5Ytm1WvXt0WLVpkLVu2DHyY9Hf37t1D3qZ27dru+p49ewa2aSFRbY8/gK7po59//rkVKlQoAs8GAAAAyLh+/fVXN/tTszsff/xx+/rrr61Hjx4u5u/YsWOangma0ajP9aPtsWy+HRaTQTrwRSI4KxZITbRfwNtow2lrNqhnBtFFgbOCai02VLNmTRs3bpwdPnzYOnfu7K7v0KGDy2JRpoo89NBDVq9ePRs9erQ1a9bMZs6caatXr7aXX345MIDepk0bW7t2rX3wwQeu5rq/XnrBggVdEA8AAAAg5TuFiumffvpp93e1atVs/fr19uKLL4YcRE9LM0EzIuW/qfOeUTrwfKaQntB+AW+jDaedx/TUIPptt93mpmwOHDjQDXZffvnltmDBgsDioZs3b47zxK+++mqbMWOGDRgwwGW4lCtXzubNm2eVKlVy12/ZssVNJRXdVzBlpV933XURfX4AAABARlC8eHGrWLFinG0VKlSwt99+O2rHBAAAAKToIPovv/ziFgHSv88995xLtf/oo4+sVKlSdtlll1lqUumWcOVbFi9enGCb6izqEkqZMmXcQqIAAAAAIqdOnTq2adOmONt+/PFHK126dNSOCQAAAAgn2TnyWvincuXKtnLlSnvnnXfs0KFDbvu3335rgwYNSu7dAQAAAMhgevXqZStWrHDlXH7++Wc3e1QlF7t16xbtQwMAAADOfRC9b9++9tRTT7kFOoNrhl9//fUuEAYAAACAM7nyyitt7ty59uabb7pSi0OHDnXrHbVv3z7ahwYAAACcezmX//znPy5TJD6VdNm9e3dy7w4AAABABnTTTTe5CwAAAJDuMtHz589v27ZtS7B93bp1dv7556fUcQEAAAAAAAAA4L1B9Ntvv90ee+wx2759u8XExNjp06dt6dKl9vDDD1uHDh1S5ygBAAAAAAAAAPDCILoW/ylfvryVLFnSLSpasWJFu/baa+3qq6+2AQMGpM5RAgAAAAAAAACQ1mui+3w+l4E+fvx4GzhwoKuProH0atWqWbly5VLvKAEASKaHl6TPxelG1Zse7UMAAAAAACBDSfYg+sUXX2zff/+9GzRXNjoAAAAAAAAAAOlVssq5ZMqUyQ2e//3336l3RAAAAAAAAAAAeLUm+jPPPGOPPPKIrV+/PnWOCAAAAAAAAAAAL5ZzkQ4dOtiRI0esatWqli1bNsuRI0ec6/fs2ZOSxwcAAAAgle3cudOKFCkS9vqTJ0/a2rVrrWbNmhE9LgAAAMCTg+jjxo1LnSMBAAAAEBXFixe3bdu2BQbSK1eubB9++GFgDSSVc6xdu7adOnUqykcKAAAAeGAQvWPHjqlzJAAAAPCUh5e0t/RqVL3plpH4fL44f//+++924sSJM+4DAAAAZBTJHkQXZaDMmzfPNmzY4P6+7LLLrEWLFpY5c+aUPj4AAAAAaUBMTEy0DwEAAADwxiD6zz//bE2bNrUtW7bYpZde6rYNHz7cTfWcP3++XXTRRalxnAAAAAAAAAAARFym5N6gR48ebqD8zz//dIsL6bJ582YrW7asuw4AAACA97LMDx48aAcOHLD9+/e7vw8dOuT+9l8AAACAjCrZmehLliyxFStWWMGCBQPbChUqZM8884zVqVMnpY8PAAAAQCpTvfNLLrkkzt/VqlWL8zflXAAAAJBRJXsQPTY21mWpxKdMlWzZsqXUcQEAAACIkM8//zzahwAAAACkn0H0m266ye69916bPHmy1axZ021buXKl3XfffW5xUQAAAADeUq9evWgfAgAAAJB+BtHHjx9vHTt2tNq1a1vWrFndtpMnT7oB9Oeeey41jhEAAABAKlI8f+rUKTfr1G/Hjh324osv2uHDh12sX7du3ageIwAAAOCZQfT8+fPbu+++az///LNt2LDBbatQoYJdfPHFqXF8AAAAAFJZ165dXWnGl156yf2t8o1XXnmlHT161IoXL25jx451fYCmTZtG+1ABAACAtD+I7qdBcwbOAQAAAO9bunSpTZw4MfD366+/7jLTf/rpJ8uXL5899thjNnLkSAbRAQAAkCFlSu4NWrdubSNGjEiw/dlnn7W2bdum1HEBAAAAiJAtW7ZYuXLlAn8vWrTIxf0aQBeVc/z++++jeIQAAACAhzLRv/jiCxs8eHCC7TfeeKONHj06pY4LSLceXtLe0qtR9aZH+xAAAMBZyJ49u/3zzz+Bv1esWOEyz4OvP3ToUJSODgAAAPBYJrqCZ9VLjE+LjB44cCCljgsAAABAhFx++eX273//2/3/l19+6RYVvf766wPX//LLL1aiRIkoHiEAAADgoUH0ypUr26xZsxJsnzlzplWsWDGljgsAAABAhAwcONCee+45u+iii6xx48bWqVMnt6Co39y5c61OnTpRPUYAAADAM4PoTzzxhA0dOtTVRZw2bZq7dOjQwYYNG+auS22TJk2yMmXKuCmltWrVslWrVp1x/9mzZ1v58uXd/joB8OGHH8a53ufzuU6DOgk5cuSwhg0bugWUAAAAgIyiXr16tmbNGuvRo4dNnTrVXnnllQSZ6r169Yra8QEAAACeGkRv3ry5zZs3z37++Wd74IEHrE+fPvbXX3/Zp59+ai1btrTUpAz43r1726BBg2zt2rVWtWpVlymzc+fOkPsvW7bM2rVrZ126dLF169a549Nl/fr1cRZEHT9+vL344ou2cuVKy5Url7vPo0ePpupzAQAAANKSChUq2EMPPWS33XabZcoUt5tw7733uoF0AAAAICNK9sKi0qxZM3eJtDFjxljXrl2tc+fO7m8NfM+fP9+mTJliffv2TbC/pqQ2adLEHnnkEfe3MugXLlxoEydOdLdVFvq4ceNswIABdvPNN7t9Xn/9dStatKg7UXD77bdH+BkCAAAAkffFF18kab9rr7021Y8FAAAASBeD6H7K1lZ2+OHDh+2GG26wcuXKWWo5fvy4m2Lar1+/wDZlyKj8yvLly0PeRtuVuR5MWeYaIJfffvvNtm/f7u7DL1++fK5MjG4bbhD92LFj7uLnX1D19OnT7hJJkX20yInk66jH0gmViD2mL8bSq0h//iMlfT6ryL5nEW9n6bitpdd2Jun3maXjtpZO21m02lo02/d1111nMTH/fT/1GQpF1586dSrCRwYAAAB4aBBdg9EnTpywCRMmBAa1r7rqKvvhhx8sZ86c9uijj7os79q1a6fKge7evdsF7coSD6a/N27cGPI2GiAPtb+2+6/3bwu3TyjDhw+3IUOGJNi+a9euyJeBuaOxpUeb5j0WwUeLsRNWwLLaXnUbU/3RHr56lKVX4UoreV46bWeRbWuRbWfpua2l23YmtLUUwG+al9vawYMHLVoKFChgefLkcQuK3nXXXXbeeedF7VgAAAAAzw6if/LJJ/b0008H/p4+fbpt3rzZLcJZqlQpu/vuu+2pp55y5VXSO2XDB2e4KxO9ZMmSVrhwYcubN29Ujy29+N0X/iRGSvNZjPlifJbNt0P/l+qPV6RIkVR/DCCttbVItzOhrSEtSa9tjXaWsrJnzx61x962bZvNnTvXlUnUmkFNmzZ16wqpNKI/Qx0AAADIqJI8iK4B84oVK8YZVG/Tpo2VLl3a/a1FiBRspxZlw2TOnNl27NgRZ7v+LlasWMjbaPuZ9vf/q23FixePs8+ZFk6KjY11l/hUXib+Ikw4O5EaZPv/x/vvY0bicfmMIKO2tUi2M6GtIS1Jr22NdpZ+Xs9s2bK5BUV1Udz/2muvWffu3V0Jw44dO7pZmFmynFMlSAAAAMCzMiUnqA+uj7hixQpXzsUvf/78tnevpg6nXmBfvXp1W7RoUZy6kfo7XAkZbQ/eX4JLzpQtW9YNpAfvo6zylStXplpZGgAAACAt0yzTgQMH2qeffmqXXHKJPfPMM4E1gAAAAICMKMmD6BUqVLD333/f/f/333/vMlTq168fuP6PP/5IUFs8pamEyiuvvGLTpk2zDRs22P333+8WNe3cubO7vkOHDnEWHlV2/IIFC2z06NGubvrgwYNt9erVLqtGNDW1Z8+ergzNe++9Z//5z3/cfZQoUcJatmyZqs8FAAAASGuUeT5jxgxr2LChVapUyc0GVbnGggULRvvQAAAAgKhJ8pxMLRx6++23uyBag+gq3aJMbr8PP/zQatasaalJ00u1eKcyY7Twp0quaJDcP3ivgf3gabBXX3216wQMGDDAHn/8cStXrpzNmzfPdQiCn5cG4u+9917bt2+f1a1b191nNGtSAgAAAJG0atUqmzp1qs2cOdPKlCnjklTeeustBs8BAACA5Ayit2rVyg2Uf/DBB9aoUSN78MEH41yfM2dOe+CBByy1KYvcn0ke3+LFixNsa9u2rbuEo2z0J5980l0AAACAjEhlGlXGpUePHq6Eonz11VcJ9mvRokUUjg4AAACIrmStDtSgQQN3CWXQoEEpdUwAAAAAIkyzOocOHXrG5JNTp05F9JgAAAAAzw2iAwAAAEh/Tp8+neg+R44cicixAAAAAGlNkhcWBQAAAJAxFxsdM2aMXXjhhdE+FAAAACAqGEQHAAAAMjgNlPfr189q1KhhV199tc2bN89tnzJlipUtW9bGjh1rvXr1ivZhAgAAAFFBORcAAAAggxs4cKC99NJL1rBhQ1u2bJm1bdvWOnfubCtWrHBZ6Po7c+bM0T5MAAAAIG1nop84ccI2bdoU+Hv58uWpdUwAAAAAImj27Nn2+uuv25w5c+yTTz5xC4iePHnSvv32W7v99tsZQAcAAECGluRB9I4dO1rz5s3t8ccfd3/36dMnNY8LAAAAQIT89ddfVr16dff/lSpVstjYWFe+JSYmJtqHBgAAAHhnEH39+vX2448/WtasWW3SpEmpe1QAAAAAIkaZ59myZQv8nSVLFsudO3dUjwkAAADwXE304sWLu3+HDBlid9xxh/3222+peVwAAAAAIsTn81mnTp1cBrocPXrU7rvvPsuVK1ec/d55550oHSEAAADggUH0OnXquLqIykp58cUXrUOHDql7ZAAAAAAiQqUbg915551ROxYAAADAs4PoAwcODPx/3rx5bd68eQn2+eeffyxHjhwpd3QAAAAAUt3UqVOjfQgAAACA92uin8mxY8ds9OjRVrZs2ZS4OwAAAAAAAAAAvDWIroHyfv36WY0aNezqq68OZKIra0WD5+PGjbNevXql5rECAAAAAAAAABBRySrn8tJLL1nDhg1t2bJl1rZtW+vcubOtWLHCxowZ4/7OnDlz6h4tAAAAAAAAAABpcRB99uzZ9vrrr1uLFi1s/fr1VqVKFbfQ6LfffmsxMTGpe5QAAAAAAAAAAKTlci5//fWXVa9e3f1/pUqVLDY21pVvYQAdAAAAAAAAAGAZfRD91KlTli1btsDfWbJksdy5c6fWcQEAAAAAAAAA4J1yLj6fzzp16uQy0OXo0aN23333Wa5cueLs984776T8UQIAAAAAAAAAkJYH0Tt27Bjn7zvvvDM1jgcAAAAAAAAAAO8Nok+dOjV1jwQAAAAAAAAAAK/WRAcAAACA1PDMM89YTEyM9ezZM9qHAgAAACTAIDoAAACAqPn666/tpZdesipVqkT7UAAAAIBzK+cCAAAAACnp0KFD1r59e3vllVfsqaeeCrvfsWPH3MXvwIED7t/Tp0+7C1KPXl+fmfksxjIKPlNIL2i/gLfRhtPWYzKIDgAAACAqunXrZs2aNbOGDRuecRB9+PDhNmTIkATbd+3aZUePHk3lo8zY1LE8aQVduR114zOCnTt3RvsQgBRB+wW8jTYcGQcPHkzSfgyiAwAAAIi4mTNn2tq1a105l8T069fPevfuHScTvWTJkla4cGHLmzdvKh9pxqYO/I+2x7L5dlhMBunAFylSJNqHAKQI2i/gbbThyMiePXuS9mMQHQAAAEBE/fnnn/bQQw/ZwoULk9RxiY2NdZf4MmXK5C5IXcp/U+c9o3Tg+UwhPaH9At5GG047j+mZb5c9e/a4eonKNMmfP7916dLF1VA8E03t1BTRQoUKWe7cua1169a2Y8eOwPXffvuttWvXzmWx5MiRwypUqGDPPfdcBJ4NAAAAkHGtWbPGTde94oorLEuWLO6yZMkSGz9+vPv/U6dORfsQAQAAAO9lomsAfdu2bS5b5cSJE9a5c2e79957bcaMGWFv06tXL5s/f77Nnj3b8uXLZ927d7dbbrnFli5dGgjeNU3gjTfecAPpy5Ytc/eZOXNmty8AAACAlNegQQP7z3/+E2eb4vvy5cvbY4895uJxAAAAIK3wxCD6hg0bbMGCBa5eYo0aNdy2CRMmWNOmTW3UqFFWokSJBLfZv3+/TZ482Q2yX3/99W7b1KlTXbb5ihUr7KqrrrK77747zm0uvPBCW758ub3zzjsMogMAAACpJE+ePFapUqU423LlyuVmkMbfDgAAAESbJwbRNbCtEi7+AXRp2LChq1mzcuVKa9WqVYLbKMtcGevaz0+ZLaVKlXL3p0H0UDT4XrBgwTMez7Fjx9wleGEjf8F/XXDuVO0pko/li+Bj8hlBWhKpz32k25nQ1pCWpNe2RjtLWbyeAAAAQNrkiUH07du3J1idVbUSNdit68LdJlu2bG7wPVjRokXD3kblXGbNmuVKwJzJ8OHDbciQIQm279q1y9Vhx7k7FlMsgo8WYyetgMXEaMAh9RdqUP1PIOO1tci2M6GtIS1Jr22NdpayDh48aBnZ4sWLo30IAAAAQNobRO/bt6+NGDEi0VIukbB+/Xq7+eabbdCgQdaoUaMz7tuvXz/r3bt3nEx01VQvXLiwW/gU5+53X+gTHamWtRfjs2y+HRFZ7Tj+CSEgI7S1SLczoa0hLUmvbY12lrKyZ88e7UMAAAAAkNYG0fv06WOdOnU64z6qU16sWLEEmU4nT560PXv2uOtC0fbjx4/bvn374mSj79ixI8FtfvjhB7e4kRYVHTBgQKLHHRsb6y7xqbyMLjh3kRpk+//H++9jRuJx+Ywgo7a1SLYzoa0hLUmvbY12lrJ4PQEAAIC0KaqD6Mrc1iUxtWvXdoPhqnNevXp1t+2zzz5zdSNr1aoV8jbaL2vWrLZo0SJr3bq127Zp0ybbvHmzuz+/77//3i082rFjRxs2bFiKPTcAAAAAAAAAgPd5It2lQoUK1qRJE+vatautWrXKli5dat27d7fbb7/dSpQo4fbZsmWLWzhU10u+fPmsS5curuzK559/7gbgO3fu7AbQ/YuKqoRL/fr1XfkW7ada6bqotjkAAAAAAAAAAJ5YWFSmT5/uBs5VdkVTXZVdPn78+MD1J06ccJnmR44cCWwbO3ZsYN9jx45Z48aN7fnnnw9cP2fOHDdg/sYbb7iLX+nSpe3333+P4LMDAAAAAAAAAKRFnhlEL1iwoM2YMSPs9WXKlDGfz5dgcaZJkya5SyiDBw92FwAAAAAAAAAAPFvOBQAAAAAAAACAaGAQHQAAAAAAAACAMBhEBwAAAAAAAAAgDAbRAQAAAAAAAAAIg0F0AAAAAAAAAADCYBAdAAAAAAAAAIAwGEQHAAAAAAAAACAMBtEBAAAAAAAAAAiDQXQAAAAAAAAAAMJgEB0AAAAAAAAAgDAYRAcAAAAAAAAAIAwG0QEAAAAAAAAACINBdAAAAAAAAAAAwmAQHQAAAAAAAACAMBhEBwAAAAAAAAAgDAbRAQAAAAAAAAAIg0F0AAAAAAAAAADCYBAdAAAAAAAAAIAwGEQHAAAAAAAAACAMBtEBAAAAAAAAAAiDQXQAAAAAAAAAAMJgEB0AAAAAAAAAgDAYRAcAAAAAAAAAIAwG0QEAAAAAAAAACINBdAAAAAAAAAAAwmAQHQAAAAAAAAAArw+i79mzx9q3b2958+a1/PnzW5cuXezQoUNnvM3Ro0etW7duVqhQIcudO7e1bt3aduzYEXLfv//+2y644AKLiYmxffv2pdKzAAAAAAAAAAB4iWcG0TWA/v3339vChQvtgw8+sC+++MLuvffeM96mV69e9v7779vs2bNtyZIltnXrVrvllltC7qtB+SpVqqTS0QMAAAAAAAAAvMgTg+gbNmywBQsW2Kuvvmq1atWyunXr2oQJE2zmzJluYDyU/fv32+TJk23MmDF2/fXXW/Xq1W3q1Km2bNkyW7FiRZx9X3jhBZd9/vDDD0foGQEAAAAAAAAAvCCLecDy5ctdCZcaNWoEtjVs2NAyZcpkK1eutFatWiW4zZo1a+zEiRNuP7/y5ctbqVKl3P1dddVVbtsPP/xgTz75pLufX3/9NUnHc+zYMXfxO3DggPv39OnT7oJz57OYiD6WL4KPyWcEaUmkPveRbmdCW0Nakl7bGu0sZfF6AgAAAGmTJwbRt2/fbkWKFImzLUuWLFawYEF3XbjbZMuWzQ2+BytatGjgNhoIb9eunY0cOdINrid1EH348OE2ZMiQBNt37drl6rDj3B2LKRbBR4uxk1bA1cPXsENq27lzZ6o/BpD22lpk25nQ1pCWpNe2RjtLWQcPHoz2IQBAundszDTLKNyp2TLRPgogZdGGES1RHUTv27evjRgxItFSLqmlX79+VqFCBbvzzjuTfbvevXvHyUQvWbKkFS5c2C18inP3uy/0yZFUy9qL8Vk2347/5e+lrvgnhICM0NYi3c6Etoa0JL22NdpZysqePXu0DwFJkJE670IHHgAAIMqD6H369LFOnTqdcZ8LL7zQihUrliDT6eTJk7Znzx53XSjafvz4cVfrPDgbfceOHYHbfPbZZ/af//zH5syZ4/72+f7b2TzvvPOsf//+IbPNJTY21l3iU3kZXXDuIjXI9v+P99/HjMTj8hlBRm1rkWxnQltDWpJe2xrtLGXxegIAAABpU1QH0ZW5rUtiateu7QbDVedcC4T6B8BVN1ILjYai/bJmzWqLFi2y1q1bu22bNm2yzZs3u/uTt99+2/7555/Abb7++mu7++677csvv7SLLroohZ4lAADI6Gq1fiMij6PYSIkHyhBnQBYAAAAAMlBNdJVcadKkiXXt2tVefPFFt2Bo9+7d7fbbb7cSJUq4fbZs2WINGjSw119/3WrWrGn58uWzLl26uLIrqp2uMisPPvigG0D3Lyoaf6B89+7dgceLX0sdAAAAAAAAAJDxeGIQXaZPn+4GzjVQrswqZZePHz8+cL0G1pVpfuTIkcC2sWPHBvbVIqKNGze2559/PkrPAAAAAAAAAADgNZ4ZRFc2+YwZM8JeX6ZMmUBN8+DFmSZNmuQuSXHdddcluA8AAAAAAAAAQMZFsUwAAAAAAAAAAMJgEB0AAAAAAAAAgDAYRAcAAAAAAAAAIAwG0QEAAAAAAAAACINBdAAAAAAAAAAAwmAQHQAAAAAAAACAMBhEBwAAAAAAAAAgDAbRAQAAAAAAAAAIg0F0AAAAAAAAAADCYBAdAAAAAAAAAIAwGEQHAAAAAAAAACAMBtEBAAAARNTw4cPtyiuvtDx58liRIkWsZcuWtmnTpmgfFgAAABASg+gAAAAAImrJkiXWrVs3W7FihS1cuNBOnDhhjRo1ssOHD0f70AAAAIAEsiTcBAAAAACpZ8GCBXH+fu2111xG+po1a+zaa69NsP+xY8fcxe/AgQPu39OnT7tLJEX20aJPz9fnLjGWUUT6M4XIykjvLu0X6VFGeodpw2nrMRlEBwAAABBV+/fvd/8WLFgwbPmXIUOGJNi+a9cuO3r0qEXSiZwZqwulbuVJK2gxMTH/68qnfzt37oz2ISAVZaQ2TPtFekQbTt92RqENHzx4MEn7ZZxPHgAggVqt34jYmV39GCrLMFMmKokBAOL+RvTs2dPq1KljlSpVCrlPv379rHfv3nEy0UuWLGmFCxe2vHnzRvBozY4dOWkZiTrwv9key+bbYTEZpAOveAXpV0Zqw7RfpEe04fStSBTacPbs2ZO0H4PoAAAAAKJGtdHXr19vX331Vdh9YmNj3SU+nZiN9MnZjHgqWPlv6rxnlA48J/zTt4z27tJ+kd5ktHeYNpx2HpNBdAAAAABR0b17d/vggw/siy++sAsuuCDahwMAAACExCA6AAAAgIjy+Xz24IMP2ty5c23x4sVWtmzZaB8SAAAAEBaD6AAAAAAiXsJlxowZ9u6771qePHls+/btbnu+fPksR44c0T48AAAAIEOXEgIAAAAQZS+88ILt37/frrvuOitevHjgMmvWrGgfGgAAAJAAmegAAAAAIl7OBQAAAPAKMtEBAAAAAAAAAAiDQXQAAAAAAAAAAMJgEB0AAAAAAAAAgDAYRAcAAAAAAAAAwOuD6Hv27LH27dtb3rx5LX/+/NalSxc7dOjQGW9z9OhR69atmxUqVMhy585trVu3th07diTY77XXXrMqVapY9uzZrUiRIu42AAAAAAAAAAB4ZhBdA+jff/+9LVy40D744AP74osv7N577z3jbXr16mXvv/++zZ4925YsWWJbt261W265Jc4+Y8aMsf79+1vfvn3d/X/66afWuHHjVH42AAAAAAAAAAAvyGIesGHDBluwYIF9/fXXVqNGDbdtwoQJ1rRpUxs1apSVKFEiwW32799vkydPthkzZtj111/vtk2dOtUqVKhgK1assKuuusr27t1rAwYMcAPtDRo0CNxWWekAAAAAAAAAAHhiEH358uWuhIt/AF0aNmxomTJlspUrV1qrVq0S3GbNmjV24sQJt59f+fLlrVSpUu7+NIiurPbTp0/bli1b3OD6wYMH7eqrr7bRo0dbyZIlwx7PsWPH3MXvwIED7l/dly44dz6Liehj+SL4mHxGkBHpc+/z+fj8A6mMtuZtvG8AAABA2uSJQfTt27e7WuXBsmTJYgULFnTXhbtNtmzZ3OB7sKJFiwZu8+uvv7rOytNPP23PPfec5cuXz2Wm33DDDfbdd9+524cyfPhwGzJkSILtu3btcnXYce6OxRSL4KPF2EkrYDExGkTXcHrq2rlzZ6o/BpDW6LtWM4Q0uKcToABSB23N25TQAQAAACDtieoguuqQjxgxItFSLqnZ0VS2+vjx461Ro0Zu25tvvmnFihWzzz//PGxt9H79+lnv3r3jZKIrc71w4cJu4VOcu999oU+OpFomeozPsvl2/C8nPXXFPyEEZAT6vtWJKn1PMrAHpB7amrdpkXsAAAAAaU9UB9H79OljnTp1OuM+F154oRvUjp+9e/LkSduzZ4+7LhRtP378uO3bty9ONvqOHTsCtylevLj7t2LFioHr1ek877zzbPPmzWGPKTY21l3iU2eVDmvKiMRgdtzH++9jRuJx+Ywgo9LAHt+TQOqjrXkX7xkAAACQNkV1EF0D1rokpnbt2m4wXHXOq1ev7rZ99tlnLtuqVq1aIW+j/bJmzWqLFi2y1q1bu22bNm1yg+O6P6lTp05g+wUXXOD+XwPzu3fvttKlS6fY8wQAAAAAAAAAeJMn0l206GeTJk2sa9eutmrVKlu6dKl1797dbr/9ditRooTbR4uDauFQXS+qb96lSxdXdkWlWTQA37lzZzeArkVF5ZJLLrGbb77ZHnroIVu2bJmtX7/eOnbs6O6nfv36UX3OAAAAAAAAAIDo88QgukyfPt0Nbjdo0MCaNm1qdevWtZdffjlwvWqbK6P8yJEjgW1jx461m266yWWiX3vtta6MyzvvvBPnfl9//XWXzd6sWTOrV6+ey15fsGCB+xcAAAAAAAAAkLFFtZxLchQsWNBmzJgR9voyZcqYz+dLsDjTpEmT3CUcLQQ6efJkdwEAAAAAAAAAwJOZ6AAAAAAAAAAARBqD6AAAAAAAAAAAhMEgOgAAAAAAAAAAYTCIDgAAAAAAAABAGAyiAwAAAAAAAAAQBoPoAAAAAAAAAACEwSA6AAAAAAAAAABhMIgOAAAAAAAAAEAYDKIDAAAAAAAAABAGg+gAAAAAAAAAAITBIDoAAAAAAAAAAGEwiA4AAAAAAAAAQBgMogMAAAAAAAAAEAaD6AAAAAAAAAAAhMEgOgAAAAAAAAAAYTCIDgAAAAAAAABAGAyiAwAAAAAAAAAQBoPoAAAAAAAAAACEwSA6AAAAAAAAAABhMIgOAAAAAAAAAEAYDKIDAAAAAAAAABAGg+gAAAAAAAAAAITBIDoAAAAAAAAAAGEwiA4AAAAAAAAAQBgMogMAAAAAAAAAEAaD6AAAAAAAAAAAeH0Qfc+ePda+fXvLmzev5c+f37p06WKHDh06422OHj1q3bp1s0KFClnu3LmtdevWtmPHjjj7fP3119agQQN3nwUKFLDGjRvbt99+m8rPBgAAAAAAAADgBVnMIzSAvm3bNlu4cKGdOHHCOnfubPfee6/NmDEj7G169epl8+fPt9mzZ1u+fPmse/fudsstt9jSpUvd9RqEb9KkibVo0cKef/55O3nypA0aNMgNpP/555+WNWvWCD5DBKvV+o2IPdbp06dt586dVqRIEcuUyTPnlQAAAAAAAABEgCcG0Tds2GALFixwWeM1atRw2yZMmGBNmza1UaNGWYkSJRLcZv/+/TZ58mQ3yH799de7bVOnTrUKFSrYihUr7KqrrrKNGze6DPcnn3zSSpYs6fbRIHqVKlXsjz/+sIsvvjjCzxQAAAAAAAAAkJZ4YhB9+fLlrtyKfwBdGjZs6LKGV65caa1atUpwmzVr1riMde3nV758eStVqpS7Pw2iX3rppa7UiwbbH3/8cTt16pT7fw20lylTJuzxHDt2zF38Dhw4EMho1gXeovfM5/Px3gGpiHYGRAZtzdt43wAAAIC0yROD6Nu3b3elNoJlyZLFChYs6K4Ld5ts2bK5wfdgRYsWDdwmT548tnjxYmvZsqUNHTrUbStXrpx9/PHH7v7DGT58uA0ZMiTB9l27drk67PBeh1UzFzToQDkXIHXQzoDIoK1528GDB6N9CAAAAADS2iB63759bcSIEYmWckkt//zzj1ugtE6dOvbmm2+6THSVh2nWrJkrHZMjR46Qt+vXr5/17t07Tia6ysEULlzYLXwK7w04xMTEuPePAQcgddDOgMigrXlb9uzZo30IAAAAANLaIHqfPn2sU6dOZ9znwgsvtGLFirmFH4NpEVDVM9d1oWj78ePHbd++fXGy0Xfs2BG4jeql//777668i7+jqW0FChSwd999126//faQ9x0bG+su8ek+6LB6kwYceP+A1EU7AyKDtuZdvGcAAABA2hTVQXRlSemSmNq1a7vBcNU5r169utv22WefuWyrWrVqhbyN9suaNastWrTIWrdu7bZt2rTJNm/e7O5Pjhw54jor6mz6+f+mJiUAAAAAAAAAwBPpLlros0mTJta1a1dbtWqVLV261Lp37+4yxUuUKOH22bJli1s4VNdLvnz5XKkWlV35/PPP3QB8586d3QC6FhWVG264wfbu3WvdunVzZWO+//57t4/qodevXz+qzxkAAAAAAAAAEH2eGESX6dOnu0HyBg0aWNOmTa1u3br28ssvB64/ceKEyzRXdrnf2LFj7aabbnKZ6Ndee60r4/LOO+8Ertf9vf/++/bdd9+5wfVrrrnGtm7dagsWLLDixYtH/DkCAAAAAAAAANKWqJZzSY6CBQu6euXhlClTxnw+X4LFmSZNmuQu4SgbXRcAAAAAAAAAADybiQ4AAAAAAAAAQKQxiA4AAAAAAAAAQBgMogMAAACICpVdVFlGlWGsVauWrVq1KtqHBAAAACTAIDoAAACAiJs1a5b17t3bBg0aZGvXrrWqVata48aNbefOndE+NAAAAMCbC4umZf4FTQ8cOBDtQ8FZOH36tB08eNBlQGXKxHklIDXQzoDIoK15mz+W9MeW6d2YMWOsa9eu1rlzZ/f3iy++aPPnz7cpU6ZY37594+x77Ngxd/Hbv3+/+3ffvn3ucx9Jx47+YxmJXt1DR47ZCd8Ji7GM8dnU5wrpV0Zqw7RfpEe04fRtXxTacFJjcAbRU4A6q1KyZMloHwoAAADSQWyZL18+S8+OHz9ua9assX79+gW26cRPw4YNbfny5Qn2Hz58uA0ZMiTB9tKlS6f6sSIjeivaBwDgrNF+AW97K83G4Ayip4ASJUrYn3/+aXny5LGYmJhoHw7O4oyTToDoPcybN2+0DwdIl2hnQGTQ1rxN2S8K3hVbpne7d++2U6dOWdGiReNs198bN25MsL8G21X6xU/Z53v27LFChQoRf6cyvlcA76L9At5GG05bMTiD6ClAWTMXXHBBtA8D50hfSHwpAamLdgZEBm3Nu9J7BvrZio2NdZdg+fPnj9rxZER8rwDeRfsFvI02nDZicIplAgAAAIio8847zzJnzmw7duyIs11/FytWLGrHBQAAAITCIDoAAACAiMqWLZtVr17dFi1aFKdEi/6uXbt2VI8NAAAAiI9yLsjwNDV40KBBCaYIA0g5tDMgMmhr8BLVOO/YsaPVqFHDatasaePGjbPDhw9b586do31oCML3CuBdtF/A22jDaUuMT9XTAQAAACDCJk6caCNHjrTt27fb5ZdfbuPHj7datWpF+7AAAACAOBhEBwAAAAAAAAAgDGqiAwAAAAAAAAAQBoPoAAAAAAAAAACEwSA6AAAAAAAAAABhMIgOAAAAAAAAAEAYDKIDAAAAAAAAABAGg+hAGuTz+aJ9CEC6dvr06WgfApAh8HsGIDXw3QJ4EzE44G2+DP77myXaBwAgYWCRKdN/z2+dPHnSfUllzZrV/RsTExPtwwPSVRtbuHChHT161EqXLm1VqlSJ9qEB6batHTlyxP2O5cqVK9qHBcDjiJUBbyIGB7ztNLG9xfgy+mkEII165pln7Ouvv7a9e/fakCFD7Jprron2IQGeF9zB7tOnj7355pv2zz//WLly5ezGG290bQ1Ayho6dKh98sknLvBu3769PfDAA9E+JADpALEy4B3E4ED6MTQDx/aUcwHS4NS24cOH25gxY6xkyZKWLVs2a9iwoU2ePNlOnDgR1WME0kvwvnbtWluxYoW9//77tmzZMtfGPvzwQxfUA0i53zP9lk2cONG1sUqVKln37t3tsccei+rxAfAmYmXAm4jBAW8jtv9/lHMB0gj/tJjNmzfbvn37bPbs2VavXj23bdCgQXbfffe5L69OnTq5KasAkscfvM+aNcu1r6pVq1r16tXdtkcffdRy5sxp8+bNc0H86NGjo3y0gPd/z7755hvXrqZOnWpNmzZ1nej69eu73zH9/7PPPhvtQwXgIcTKgDcRgwPeRmz//xhEB9KQDz74wFq0aGHnn3++NWvWLLBd09sUfHTr1s3926FDB5d1AyB5Dh48aPPnz7evvvrKLrvsssD2/Pnzu7PoosyYe+65x1599dUoHingbcoyu/rqq12dxBkzZrht+v26/fbb3f8r2NbfI0aMiPKRAvASYmXAm4jBAW8jtv8vyrkAaYjO5j300EO2ZcsW+/XXX902/7IFgwcPtgEDBti9995rCxYsiPKRAt7gbz/+f/PkyWNPP/203XbbbbZp0yZXTzV+EK+sNu3PkiHA2dP0zueee85lhWrqdjAF26+//rqNHDnSJk2aFLVjBOA9xMqANxCDA+kLsf1/sbAokAZWNo5PZ+Bnzpxpc+bMsSZNmsS5TvUeO3bsaFmyMJEESGob+/PPPy02Nta1m4IFC9rWrVtt2LBhtmbNGmvTpo09/PDDgdsdOnTInWHXmfQztVMA/xWunRw/ftwF26qTOG7cOOvRo0ec6xctWuQ6zPyeAQiFWBnwJmJwwNuI7cNjEB2I8pfS22+/bb///rtlz57datasaVdeeWVgOoyuU924+J0DOXnyZLr+cgJSagEj1UlVnUUF5prarcD9lltuse3bt7uVxXUmvW3btta7d++w9wEg8d+zadOmud+z3bt32x133OEyVpR5pmmd/fr1c0H3gw8+mOA++D0DEB+xMuBNxOCAtxHbJ0KD6ACi4+GHH/YVKlTI16BBA1/hwoV9V1xxhe+JJ54IXN+5c2df/vz5fXPnzo3qcQJe9dRTT7k2NmfOHN+bb77pe/DBB32ZMmXyjR071l2/efNmX/fu3X0XXnihb8aMGdE+XMCz+vTp49pamzZtfJdccom79O7d27dr1y53/bPPPuvLmjWrb9iwYdE+VAAeQqwMeBMxOOBtxPahkYkOROms3nvvvWf33XefzZ0712rVquXO7ulMnmo4tm7d2vr27ev20zS3/fv328KFC6N89EDapqyWYsWKBf5W1otqpyrDJfgM+ahRo+zRRx+1JUuW2DXXXGN//PGHmw7es2dPy5w5c5SOHvCujz/+2JVWePfdd+2KK65w25Rtpt+z+vXr2xNPPOEyUpS18umnn9qXX35JhhmAkIiVAe8hBgfSF2L78CgyBURAr169bM+ePXHqSv32229WtGjRwJfSeeedZ926dXNTVD/55BM7cuSI267AQl9iAMJr0KCBjR49Os42taGff/7ZTTkT/dDrvLFqLyqwf/nll+3EiRNWunRp69OnjwveT506FaVnAHiDplyvW7cuzra9e/e6adrnn39+YDGwxx9/3OrUqWOzZs1y9RNz5MjhtvmDbHI4AAQjVga8iRgc8DZi++RhEB1IZZ999pnt27fP8ubNG2e7ViFXQKHFVURfOjqD37lzZ1u8eLGtX78+sK86FMrMARCaVgF/6qmn3P8fOHDA/VukSBGrW7euC9T//vtvV5fNH6BrYSO1uaxZs8a5H7JggPCWL19ux44ds8qVKye4Tr9nR48edUG0Osb6V1miyjLT76AoGPcH2RklWwVA4oiVAe8iBge8i9g++RhEB1LZ9ddfb1OmTHHBw4wZM9x0N7n88svdIg3PP/+8/fPPP4EvHZ3R05eYViYPxurkQGjqNJcvX95iY2PtmWeesVtvvdV+/fVXd12HDh1c21GWy8GDBwNB/F9//WWFCxeO9qEDnlK7dm2bOHFi4PfMH0C3bNnSBc+asq326O8Y79q1yy666CLXmQ6WUYJsAElDrAx4EzE44G3E9slHTXQgQnUdf/jhB7vtttvcF8706dNdJs3MmTOtffv2dv/991uTJk2sVKlSrk6c6jouXbqUzgCQiPhnvVesWGHXXnuttWrVytVNVTtTFow65wrar7rqKtchV2f822+/Tb+rhgOp9HumNqcp2h07drScOXO6moj16tWzr7/+2po3b24VK1Z05RaUUTpu3DgXbCvLhQwzAKEQKwPeRAwOeBux/dlhEB2IQKdA1NTeeuste/HFF92ZvNdff90FF/PmzXO1pDSNNV++fK7joMUZtE/8+wDw/4Lbx+rVq13HWu1HNd00hVSdbbU3Zbv85z//sbffftv96Ku+qtqcgndNUyOIB84s1G/RBx984NqX2tCAAQNcm/vxxx9d5pkW/1O7UpucP3+++z1T9llGDbYBhEasDHgTMTjgbcT2Z49BdCCVv5RUJ05n9O666y73JTN79mw3ZSZ79uw2bdo0K168uG3ZssUtwKKLpqfqtgQWQNLamH7klyxZYv/617/slltuce0tOIgfP368WxQlvoz6ww+cbVsbM2aMq3c6cOBAt+2jjz5y7Uv79O/f32Wg+adqK7xUoM3vGYBQiJUBbyIGB7yN2P7ccNoeSAX+L6XHHnvMLbSiL6Y9e/a4KW9t27a17t27u05Ap06dbMeOHS64KFeunFWtWjWwMFJG/VICktPG9OP+0ksv2aBBg6xZs2YueNcPfLVq1VxQ//HHH7tajJqiFh/BO5D0tqbyCQq0VYNYg1ly4403ut8z/bY9/fTTrs2pXZUuXdrKlCnD7xmAsIiVAW8iBge8jdj+3JCJDqQSLYI0ePBgN920SpUqbtvx48fdCsby3nvv2ahRo1wHQfvkz58/ykcMeMs333xj7dq1s1dffdXq1Knjpnlv27bNFi5caFdffbXVqFHD1qxZY1deeaWbOqpOOoDke+2111yg/cknn7iF/uTYsWMuqFYQrQD72WefdQNdkydPdoNcAJAYYmXAm4jBAW8jtj97Gff0AZCKdG5q06ZNblqqOgU6A6/FjzQ1RmfwtGiSVi/fu3evrVq1yi3SACB5CxjprPnRo0dd5tp3333nargtWrTI7dezZ0+34EmtWrVcWyxbtmxUjx3wKmWb6DesRYsWLsj+/vvvXWCtwa/Y2Fh74IEHrEuXLq4d6ndOZRYAIDHEyoB3EIMD6Qex/bkhEx1IpYUZ7rzzTvdlpDN8b775phUsWNAFEQomVENKZ+p1G39AwsJIQHjB7UMLFJUoUcIF6vfdd59t2LDBfv31V7v77rvt+uuvtxtuuMHq16/vOuCPPPJIoI1l5NptQFKF+i0aNmyYPfHEEy6TbObMmXbxxRe77DK1xRUrVriMtOABLn7PAMRHrAx4EzE44G3E9imLbzLgHAV/oejsnb5s1CmYMmWKtW7d2k1/ueOOO6xRo0buTN+7775rI0aMcNPeChUqFLgfvpSAxNuYfuyXLVtm3bp1cwsYDR061C10osXHNJ1U+/mnohUpUiRO1gzBO5D0tjZhwgSXgaJapxrg0m/W3LlzXWaKfs8qVKhg3377rd17771uv+BAm98zAMGIlQFvIgYHvI3YPuWRiQ6kEC2MNH36dDf9RWfjixUr5rZrGmqBAgUCZ+GbN29uefLksVmzZsUJLgCcmRYweuWVV2zatGlWs2bNOB1r+eeff2zr1q3Wo0cPV79NZ9EJ2oHkU2CtrBQtLKSapyVLlnTbDx486H6/gn/P1Fl+//33+T0DkChiZcCbiMEBbyO2Tzl8swEpQGf1lE2jRY/8iy74p62pU3DixAn797//bXPmzHErH2uhFX0pxa8vByC01atXu870vHnz3IJF+sH/8ccfbeXKla5OmzLXpk6davPnz7cDBw64Woxqf6dOnXKBAICkUUaoOskffvihVa9e3W1TO9JvlYJs/bbp90wDYbt377avv/7aXcc0TwBnQqwMeBMxOOBtxPYpi0F04BzpS2f9+vVuaps6BQoq9MWjzoLqOmrBJAUX2kedhPfee88FFtSGA5IuR44cli1bNjty5Iir0aZgYMGCBe46Zb4okFcdRmXGtGnTxgXttDEgeTRYpVqIbdu2dUH2Dz/8YF999ZVbMEzXDR482GrUqOECbC38pzbI7xmAxBArA95FDA54F7F9yqOcC5BMoTJitHiKzrprgYaXX37ZrVh+6aWXui+owoULuzPzWsE8Z86c7racmQfCC3XWW4sWderUyU0XVSDQuXNna9CggfvRb9WqlZsa3rVr18D+tDEg6b9nwb9rQ4YMseHDh7t6icoIveCCC9zU7bVr19q6detc+1NQrQ41v2cAQiFWBryJGBzwNmL71MepBeAsAwtNO9VFwf748eNdbceBAwe6IKJx48buTN8777xj48aNc9Pe8ufP726nLzS+lIDE29gXX3zhpnSXKFHCtae33nrLdcDz5ctn11xzjWXNmtW1QbUnLWoUjDYGJL2taWEhtSdlm/Xp08fVJ3799dcDv2eXXXaZq2/68MMP2+HDh92CYcLvGYD4iJUBbyIGB7yN2D4yyEQHzuJLaezYsbZo0SI37UXTT5988kn3xRO8MJL2b9q0qRUsWNDVl6KeI5C8xcdmz57tgvXY2Fh3RlzTvq+66ip3vbLVdu3aZffdd5/t3LnTBQH84ANJE5yd8uyzz7qpmxrA0u+XFg4rXbq0HTp0yHLnzu32Uftr1qyZa4uqicrvGYBQiJUB7yMGB7yH2D5yqBIPJJG/U/D444+7L6brr7/e1ZDSlFSd0dPZPn1J6UyeFl/Rl5LqxGkRB/+UGgCJ0w+9zpS/8cYbboqZ2pKmmSlQ9//oT5o0ye655x7X7pYtW+aCd20HkDh/oDxgwAAbNWqUm5o9ZswY27RpkxvQ+vvvv12Qrfqnyj5r1KiRbd++3U0B5fcMQDjEyoC3EYMD3kRsHzkMogPJoCDi3XffddkyvXv3dtNjdLnpppsCU1DVGdAZeZ29V50pTaPRwgyc3QNCUyaa+H+81W46dOhgV199tWtvI0eOdNPAW7Ro4X74VZOxefPmrr6qppv62xhZMEDS/fHHH/bJJ5+4jrIW9du/f78dOHDAunfv7hYHEwXcGzdudNkrq1ev5vcMQKKIlQHvIAYH0g9i+8ignAuQjMVV1qxZ476QtKqxAos777zTnen717/+5b6klixZ4oKMPXv2uEwbFmYAkj71TIF75cqVrUePHnbllVe6RU9at27tAnhNGVVbUnaMbqO6qn60MSB5v2fHjx+3P//8003N3rFjh3388cd26623Bn7PNN1TbU3/rw6zFgDk9wxAKMTKgDcRgwPeRmwfHWSiA2fg/1LSSsaTJ092i6uobpQWRdJZev+XkuiMnqaufvvtt662o39aDF9KQPgffn/w3qtXL2vXrp2bGqr289BDD7ng/bnnnnPBu6jzrcy2bdu2xbkf2hiQvN8z/VapNnGNGjXcgkIKsjXl0/97tnnzZps7d66bpq2pn/yeAQiHWBnwHmJwwPuI7aODQXTgDFPb5O2333a1Gi+99FI3HbVKlSruC0ln4f1fSseOHbOnnnrKfXHpLL4f02KAxH/4tciYFh574YUXrHDhwjZs2DC3arg64XXr1nUBu86st2/f3i2QogWPACT/9+ydd95xWShqXwqaixUr5tpdx44dXb1iUXbKI488YtmyZbM6deoEbsvvGYBgxMqAdxGDA95FbB9dlHMBzuCzzz5zq5OXK1fO1XWUpUuXBgKIG264wdVz/OCDD9y0GX9dx/hTWwGEph95ZatdfPHFNmPGDCtbtqzb/tNPP1mXLl1sw4YNlj17dpfZpjblr7/I1DMgeT766CNbtGiRq4nYr18/t00LCrVt29ZNAb3ssstc+/v0009dp1olGfg9A5AYYmXAm4jBAW8jto+OLFF6XCBN07ml9evXu+wZnYHXlBg/nb0bPny46wy89tprVqFCBbvwwgttwYIFliVLFrcwg/4FkFD8H+3q1au74F0LkSlLzd/+1BlXsK56qtqu6aX169d3QTttDEh6rVO1OWWZde7c2Xbu3Ok6xn7KVpk1a5a9/PLLrk6xpnKrTWpKKL9nAM6EWBnwFmJwwNuI7dMGMtGBEIur+M2ZM8fVmMqZM6dNnDjRatWqFed6rVKu6/z4UgKSRgG7fzr3unXr3MJjaktffvmly3o5ceKEO1MeH9kvQPJ+zw4fPuwWD1JGmaZjq22NHTvWGjZseMb7oK0BiI9YGfA+YnDAe4jt0w5y+IF4X0pvvvmm9e/f3/1/mzZtbPDgwe5s3/PPP++moAZ/CQV3CnQfdAqAxGu3LV++3KpWrWozZ850f1erVs3eeOMNO3DggMt0UdaLf7pofPzwA0lfLEyLgGmqtrJUlAn673//27UrLRamcgt+tDUAiSFWBryJGBzwNmL7tIVBdGR4wV9Kq1atcoGEOgfjx49321RTqk+fPvbDDz+4bTpjH+pLiIUZgNDUafZPH1UbWrFihfv/+++/3/3w+4N4BfSq13b99dfb0aNH+aEHzmGqtjLNpkyZYvPmzXNTOjXtU7URNcXzl19+sREjRgSCbdoagDMhVga8iRgc8DZi+7SHci7A/2jFYn0xKcD/5ptvLEeOHK7Oo39hJC24ouCjSJEiNmrUKLvkkkuifciAp+is+Ysvvuime+/Zs8dlw6gT/uqrr1qHDh3cPmp71113nd16660uOACQfL169XKLB2mhIS0QpnrFDz74oD3wwAPuN0y/dXfccYflyZPHtckqVapE+5ABeACxMuBNxOCAtxHbpx3MpwP+Ny1VQcQnn3zivnB0Jr5v376uzqPO/KnToC+lf/75x5YtW+YWYQGQdH///be999579uSTT7rgXLQIStGiRe2ee+5x07vVxi6//HKX5XbRRRdF+5ABT3rnnXds2rRptmjRIpedki1bNhdka7sGvrp16+ZqoWqf0aNHW6VKlaJ9yAA8gFgZ8CZicMDbiO3TFsq5AGZu+ouCfa1cHBsb61Y1VqChf8eNG+cu/oBDZ+bVWQiuLwfgzI4fP25//fWXOzsuaj8K2pUZo0XINK1UU9FEbVFT0ELVcgNwZvv27bPzzjvPSpUqFVgYbMKECVajRg0XWE+aNMnVUbziiitcSQZ+zwAkBbEy4E3E4IC3EdunLQyiI0PzBwiFCxd2AcaWLVvc3/rS0ZdUv3797NChQ65OnBZrEAUWwfXlAMQV6ke7ePHiVq9ePbfo2K5du1z7UTvKnTu3lStXzgXtd955p61cuTLQtqjlBpxZcEU+/++Z2o0WBlM2qLJT9NsmGuxS5sr8+fNdHVTVPPXXJ+b3DEA4xMqAdxCDA95GbJ/28coiQwcW/i+XunXr2q+//uqC/yNHjgS260usQYMGdumll7oFHHbs2OG2szASkPjiJ1pg7Ouvv7YNGza4v7t37+7alBYfO3DggNtPQYDOro8cOdKaN29u/fv3dwECy3UASV/oT/xt5vbbb3cZZvfee68LvhVci2qgNmnSxCpUqOBqJepvAIiPWBnwJmJwwNuI7b2BmujIkIHFK6+8Yhs3bnSLMmhBpGbNmrlpbC1atHBn8BRIlC5d2oYNG+bqPnbs2NHVn1IwctNNN0X7qQBpUnDW2eOPP24fffSRbd261dVlu+CCC1ydNtVe1Kri2qasmO+++87dTgsZ6Sz62rVr3UJlAJL2e6YpnMoe2759uxvI0gJ/s2fPdr9nDRs2tN69e1u+fPls+PDhVrJkSZeJpuwz1Ue97777ov1UAKQhxMqANxGDA95GbO8dZKIjw/B/KT366KM2ePBgO3HihJUpU8Z1AlQTrmnTpu6LZ+HChXb33Xdbo0aNXDbNoEGD3IrHOsOnWlQAQvOfOX/22WddPdTx48fbzz//7BY60RQzrSiutjVx4kTX2VabvOGGG2z16tXu/3X2XIG+pquRBQMk/numRf2efvppF0C3adPGlVXo0aOHywhdvHixa0s9e/Z007QPHjzoMki1TVO3NfgFAMGIlQFvIgYHvI3Y3jvIREeG8sknn7gsmg8++MCqVavmzrgrmFDQL+ocaEEGBRKq73jllVe6oGTIkCHuy0m1HwGEpyneOnM+duxYu+aaa+zDDz90WS8K6LUY2cmTJ+3yyy93F7/9+/e7AOHdd9+1r776yi1YBuDMVqxYYXPmzLG33nrL6tSpY0uXLnU1E9W2cuXK5YJpbVMnWr9jF110kfv3iSeecO1UGaMAEB+xMuBNxOCAtxHbewOD6MhQdLZOXy7qFLz55puurpSmy7Rr184FEVosqWLFilasWDG3/zfffGPPPPOMffbZZ65TUaJEiWg/BSBN04rhmj5aqFAhNzVUNdxUa1FTSJXRpunh6mD7p3r/9ddf9sILL9iSJUtcO1P7AxCeMsQUMKuOadGiRV2Q/fbbb1unTp3cQJcyzXTdt99+66Zra9Ew+f77793v2YIFC1wWKQNdAEIhVga8iRgc8CZie2+hnAvSLS2asnv37jjb9OWj4EJn21UvSlPe7r//fnedaseprtTff/8d+DIrUKCAm8aqqTPBZ+0BmMtY0SImY8aMcYG4KEjXVDJNLbvrrrtcG/PXZlNdN2W2+duYaOpo586dXbYMbQwITb9lf/75Z5wp23nz5nWDXZqyreBaHWXVLRZlog0dOtR+++23wH0og6VWrVr25Zdf0tYAOMTKgDcRgwPeRmzvYT4gHZo5c6avUaNGvlKlSvk6duzoW7Zsmdu+bds237XXXuuLiYnxjRo1KrD/kSNHfM2bN/d16NDBd/r06Tj3derUqYgfP5DWvfrqq74LLrjAV61aNV/x4sVdW/v111/ddWpvOXPm9F1zzTW+PXv2uDa0e/duX9OmTX1169b1nTx50u0Xv60BSGjGjBm+q666yleiRAlf7dq1fUuWLPEdP37ct2XLFl+LFi182bNn9/Xv3z+w/z///ON+z26//fYEv1/8ngHwI1YGvIkYHPA2Yntvi9F/oj2QD6Skl156yR555BG34ELOnDntySefdAsfzZw5001zmzx5sttH0120ernO3qtWnKanqu5jlixZ4qyODCAutZcHH3zQ3njjDWvcuLGbQqYpow888ICrw6h2pgy22267zdVNVY1UtUXVTl21apW7/tSpU67GG4Dw9FvVp08fV+tQJRKeeuopy58/v5t2rewT1U3U4kMqq6A2KNOnT7dt27bxewYgLGJlwJuIwQFvI7b3PmqiI11R0P/QQw/Z7NmzrXnz5m7bzp07bdy4cbZx40Y3zUUrkitwmDZtmqspValSJbf6sVYt15cSgQUQnn7gNTVUCxW1bdvWbWvSpImbfrZr1y4XnCtgv/nmm2316tW2aNEit10rit9xxx2ubWlhI7U1AGf+PVNHWb9nak+iaZ8DBgxw0zbV7tq0aeN+s95//323r2oYn3/++W7KNr9nAEIhVga8iRgc8DZi+/SBTHSkC/oYq66UFmKoW7euW0wlT5487rqGDRu6oENfPKo3dd1111mOHDncdZs3b3aBR758+dx1BBbAmelMuOovKjtN/6/sFv3YK+tFi6CoDSrbRfXb1BGPv8AJP/xA4tSGlEGm+qZaNEjtTJQp+umnn7osFmWhqBOt3y+1KdU5VSaLMlP4PQMQH7Ey4G3E4IB3EdunHwyiI11R8K9gQmfphw0b5lY01pl4ZdEokNAiDcqm0arlLVq0cBk4RYoUcbdlWgyQOLUTnT2fMGGCFSxY0P2t6WWaQlq+fHn74osv3EVnz7V98ODB1rdv32gfNuA5P/zwgzVt2tQqVqxo77zzjlskbN26dXbnnXdabGysm8qtrDP9v37L2rVrZxdddJG7rUI7/yJFABCMWBnwJmJwwNuI7dMHBtGRbvgDe3UOND1GAb8uyrTR6uPyxx9/2DfffONWK9d1b7/9Np0BIIn8P95qa7NmzbKJEye6qd3Lli1zZ9aDaUr4Tz/9ZDfeeCNnzIGztGHDBrvhhhtcJsrFF19sH374oSup4KffN11+/vlnVxeV3zMAZ0KsDHgTMTiQPhDbex+D6EiXnQNNidFiKzp7p4wana2Pf/bO/zdn9YCzC+LfeustF8Rrmpnqpipr7fjx45YtW7Y4t2HqGXD21BlW1qimdirQ1r/h2hRZogASQ6wMeBMxOJA+ENt7G+8G0hV9weiLRrUdNZXtzTffdCsf79ixIxD8qx6c+IMQOgVA0vk702prt956q3Xr1s327dtnHTp0cGfUFbyrXQUjeAfOnqZoa/q2skPV5rRImL9N+fMg9K+/XQLAmRArA95EDA6kD8T23sY7Ak8KDhC0SEOozoHqTb333ntukQbVfFRtOAleUIUvJeDMgicr+f8/OIi/7bbbrHv37q4dqs0dOHCAdgWksAoVKtjChQvdFFB1ljXYJf6BLf3LIBeAYMTKgLcRgwPpF7G9d/EtC0/yBwi9evWykSNHhu0cNGvWzHUONN1NmTYAEhc/i8VPP+TB2WnB2TAdO3Z0NRlz584d4aMF0l9bCxdsf/LJJ64EwzPPPJOqxwXA+4iVAe8hBge8jdg+/aMmOjwluCajFj266aab3FSY2rVrh9zfX0NKi67UrFmTKW1AIoLrrikzbfny5a5Gm37k+/fvn2D/UPVSqd0GJC64nWzatMlNw9bfpUuXPuPtNPVTCwAGZ4oCgB+xMuBNxOCAtxHbZwwMosOTRo0a5aasaQGVxM7aBQcWLK4CJM1jjz1mr7/+unXp0sVy5sxpAwcOtPbt27vFi+JjwTEgeYLbjDrGb7/9th0+fNhlmfXu3dvuu+++RDPKtC/BNoBwiJUBbyIGB7yH2D7jIEKC5/zzzz/29ddfu6yaFi1aJLp/cGBBpwBI3IoVK2zu3Lk2Z84cq1Onjr377ruWPXt2u+qqq0IGCwTvQPL428yzzz7rss1UQkHtaePGjS7QVl3i0aNHn/E+CLIBhEOsDHgTMTjgTcT2GQdREtK8+GfYc+TIYWPHjrWiRYvayy+/7OpINWrUiDPxQArRj3yuXLlc8D5v3jy766673I/+v/71Lzt48KAtWbLETQ+nvQFnT9meX375pfXo0cNuuOEGt02/ZWXKlLGWLVtatWrV7M4774z2YQLwAGJlIH0gBge8i9g+Y6BgFtJ8XangGm/Hjh1z/1+iRAkbMGCAW5W8VatW9tVXXwVqwgE4t8VPzj//fFeX7cUXX3TBu6aEK3iXNWvW2KxZs+yXX36JwtEC3hX/9+nIkSOuXqJ/u9qigm9ljd577702ffp095uXnAWKAGQ8xMqANxGDA95GbJ8xMYgOTyzMMHHiRLf6+M033+yCCSlSpIiNGTPGWrdubU2aNLGlS5fSOQCSQW3F38aUqbZnzx73/3ny5LFff/3VHnjgAdcB9wfvmh6uKWq63YUXXhjVYwe8Osj1+++/u4A6b968LitlxowZ9v3337u26G+PaoPaPzY2lgXCAIRFrAx4EzE44G3E9hkX7x7SLP+XS79+/Wz48OEuYNC0mEcffdQtuKKFGgoVKuSmq7Zt29auueYa++6775jeBiTzh//HH390bezGG2+0/fv3W4UKFdzU0axZs9qGDRvs1VdfdVNKmzdvbn/99Ze99tprdMKBsxjkevLJJ61v3772+eefu781JVu/bfqdU1vTfuoof/vtty4bDQDOhFgZ8B5icMDbiO0zNmqiI03ToipaFEmrG2tBlYULF7oFFxRc7Nq1y2XdqHOgM/MXXXSRVaxYMdqHDHgq++Wpp56ytWvXurPjWoSsfv369umnn7qMtZkzZ7qFUZQJc8kll1jx4sXto48+couOsXo4kDT+tqYAe/LkyfbKK69Y5cqV3bZrr73W9u3b537LatWqZVWrVrUDBw644Hz+/PluH2oYAzgTYmXAO4jBAe8jts/YYnycxkQapQBBU2F0Vr579+724YcfWvv27d0UVU1P1VQZrXQ8aNAgy507d+B2mkqjAAPAmWmKt9rPu+++64Lz5cuX24QJE1zbW7x4sRUsWND27t1rJ06csOzZswemodHGgOTRon6qhahssssvv9y1qZ07d7op2wqw9fdbb73l6pxqIcD777/ftTHaGoAzIVYGvIkYHPA2YvuMi0F0pBn+M3LBZ+b+/vtvd+ZOgb/OymtxJE1R1ZeRVi3XF5Wm0OgsPYDwli1bZldffXVg+ufx48etU6dOblqZv3aqAvfPPvvMdcTz5cvnsmFU2y24TXLmHEhc/HayYMECe+SRR1wW2cGDB+2NN95wiwspkM6fP7+rU6w2F4xMMwDxESsD3kMMDngfsT38qImONFcbTvXeFPBrCqqmn5YtW9b9rbqOqvMoOiOvRZL05aR6UwDC+/e//21169Z1Z8PVzvyLmugM+bp16wL76UddbUyZa6tXr7ZGjRq57Dbt719FnOAdSPrv2datWwMLDWnqpzJENc1z+/btbkBL07UVeH/xxRcJ7ocgG0AwYmXAe4jBAe8jtkcwBtGRpmrDDR061Nq0aWPXX3+9+zLSGT7JkSOHy6jRdBl1Brp27WqbNm1ytR/1ZaQvMgChaTEiZaXpR37WrFmB7Y0bN3bZa6qlGtyGLrvsMmvXrp3LauvWrZsL9FlFHDi7hYZU71QZaPp9a9GihaubqMyze+65x9U51ZTtXLlyRfvQAaRhxMqANxGDA95GbI/4KOeCNGPw4MFuAYZp06ZZuXLlXN0ofUHpogybKVOm2H333ef+v0CBAvbll1+6lcuZ2gaE528fCsKffvpp9+M/d+5c94OvKeAK6o8dO2YdOnSwtm3buv06d+5s1apVcwG8FjXSlNJSpUpF+6kAnqEAe+rUqa6+6TXXXOPqnQZTO9OiQ3fffbft2bPHZauQnQIgMcTKgHcQgwPpB7E9/DitiTRBC6foi0bBf7NmzWzDhg1uipsCDnUEFIToC+nHH3+0d955x9WWU6dAZ+7pFAChBXeadYbcv+2WW25xU800BVwdcXW0x40b54J0TTnduHGjW+zoyiuvdG2Mc61A0qk2ouoifvzxx3brrbe6xYR27NhhK1eudJ1mUXu766673HYtIKYgW3USASAcYmXAO4jBgfSD2B7BWBYWaYKmsymLRmfedcb9zjvvtJEjR7psmiNHjtjo0aPdmfkyZcrEmVrDysZAeP7gvX///q7T/eyzz7qpZprmrewX/bDr39dee81NAVeHu3Dhwq6Gqqh+oxY90uIoAJJGnd4LLrjASpQoYT/88IPrLKsmqgazFHQrEK9UqZJrnz179nS/Y7oNv2cAzoRYGfAOYnAg/SC2RzDeVURcqCmlpUuXtiZNmtgTTzxhc+bMcWfyVFNKdDbvq6++sipVqrgvLz/qwwEJaVGx4BpsWnRs/vz5LlNNZ8dFQbsyXjR9VIsbqbaqOuW6yPLly13dRq0wrjPp/8fencDJXP8PHH/PYtd9W5bkiCJ3bomUIko6hPR3JJ1yFZE7901RIkcqJSpdfiTR5cqVFGrLUW7WzTp25/94f/iOmd2ZtcfMzrGv5+PxtTPf+cx3PnN8x+f7nvf3/Uk4szgA1//PnP9f08yTf//912SErl+/3mSMvvrqq1KwYEF5+eWXZcuWLXLfffeZRemBNINsAM4YKwPBhzE4EPwY2+N6eGfht4kZtFaUXtcvH6W1HSdNmmR+gbcOCnRmY51URb+I7r//fr/2HQh0enBdqVIlk5lmuXjxouzatculJpv+Yt6zZ09ZuXKlGczHxsaajDbLP//8I7///rs5bVy3ByDp/880C1SzUcLDw6VZs2Ym20xLKnTo0EEaNWpk/p/Tg+lcuXIlmtyPeokAnDFWBoIPY3Ag+DG2R3IwsSj8YtCgQeaXea3vqAMIndlY6ZfSL7/8IsWLFzcZNzqIOHPmjGzYsMF8iTl/sQFw9dtvv5kZwTWzRQfu+p++evzxx+XkyZNmgiLnDDUdtOv+FhkZaSYfc/7FXQ/KdVAAIDHn/4s0G1RP49R1N910k0yfPt0xwZBVj1j/H9P/63Q//P777xlcA7guxspA8GAMDgQ3xvZILkZYSBfOkyq89dZbMmvWLHMQ0LFjR5k4caK0bdvWzE6uE6z06tXLHBjoAER/9du4caPjy4qDAsA9HXxrxooO3vWX8hYtWpj/1K3sGM1m0+y1w4cPO0451UG6DhI020VZp64pBu+AZ9b/Rf369ZMxY8aYjBTNDP3kk0+kZcuW5v8r/X9Ls1hGjx5tJiHSfU8zz5hoCIA7jJWB4MQYHAh+jO2RXGSiI11rOq5du9Ys+ku81oBTOpFKkyZNTG2pd955R3LmzJloO/qlxK97QPLopEVWXbZ58+aZQb2eXqqDAM1oq1OnjsmY0f1TM9d03yJzDUiZL774wgy09f+tunXrmuuacab7VY0aNczEfzrY/vTTT2Xr1q0yYMAAJhoCkAhjZSB0MAYHghdjeyQHQXT4jP46pxMuVK1a1VzX002t2m76xaQTM1gHDjqJih4cPPjgg2agUaRIET/3HggO27Ztk2PHjpl96c4773Ss10lPdACv63QGcf0PX08100mKtm/fbvYx3dd0PQfeQMrpxH5aG1H/n1uyZImZNEzLLZQvX94R7Fq4cKHLoJp9DYAzxspA8GIMDoQWxvZIDoLo8Bmt9TZ79mxTE846APjss8/MqamtW7eWN954Q7Jmzeq4TbNu6tWrJ0OGDDF1IAEkbe7cuTJq1ChzSqj+5924cWOZM2eO4/Z169aZ07x1EP/+++9LtmzZEv1nzy/nQOrt3btX8uXLJ/fee68ZWGtGyoEDB8wpoDoI14n/ZsyY4e9uAghQjJWB4MQYHAhNjO1xPZw3BK+z6kHpgEEPCqZNm2Z+edf1Dz30kMycOdMMPPQAQCdnsGrA6eltelqM/vIHIGk6QdEzzzwjAwcONBOfaGbaBx98IAsWLDC366mhtWvXNr+ia/ZLp06dzGmkyvnXcgbvQPLoPmWx8g9uvPFG2b9/vxw8eNAMtJX+n1arVi2TiaZ1jQEgIcbKQPBiDA6EBsb2SA2C6PA6a3BgfRG9/vrrZlIkzZ7RgwPNrHnvvfdkwoQJZvChv8JbBwcVK1Z01JUC4N7ixYvlueeeM6ecaRZblSpVTNaa7jf//fefaWPVVtRB/Ndffy0ff/yxmZgMQPKtWLFChg8f7tinrMG2c/3iwoULmyCXZqTpBGF66qcOvm+77TYmGgLgFmNlIDgxBgeCG2N7pBU/f8Lndu7cKfXr1zcDDM2q0Uka2rRpY27TAwadvXzq1Kn8Mg8kw4ULF2TZsmVSunRp2bVrl2P92LFjzV+dpOiVV16RggULmtPNtN6iDuJ1EqNbbrnFjz0Hgm9f0wNfrUOsE4P17t3bMdi2DpA1oJU3b17zf1iPHj2kc+fOEhUVZQboVlvqJAK4HsbKQOBjDA4EN8b28AZqosPrnL+EnGu96SmoR48edRwc6JeP1o7T63oKq/OvfwA807psY8aMMfUWNVvt559/NgfgL7/8stx0000me01P99aabjly5DCnnd11113mvtRfBJJPs0704FizQ7XEgh4cJ/x/TsXGxsrFixclJibGnAaqt7GvAfCEsTIQnBiDA8GNsT3SiiA6vEJ/mdNf9HTihesdHOgs5nowoJedf8WzJk0CcH1ap23EiBHy1VdfyalTp8yAvVixYi77n9Za/fvvv6V///78hw+kcV/75Zdf3A629XY9eNaaqK1atXK5DQAsjJWB0MAYHAhujO2RFgTR4ZXTYrp162YODLRelJ4Wk/CLRmtK6SltSk9X3bRpk/n1r3Llyn7tOxDMDh06JCNHjjRZMHrat/5nr/RXc52ozJnWbuPUMyDtg+2WLVtK3759HRlpOrg+fPiw/PHHHxwoA3CLsTIQWhiDA8GNsT1SiyA60u20GOfLeiAxadIkBhSAj35JZ8AO+G5fe+SRR+TJJ580g2w9kN6yZYsJfrHfAfCEsTIQWhiDA8GNsT1SgyA60u20GP0y6t69u5nJ/P777ze38aUEeGff02yYjRs3SqNGjRwzjgPwzb62fv162bFjhxQtWlR+/fVXM8imTiKA62GsDIQWxuBAcGNsj5QiiA6v4rQYwH/7Xp8+fSRr1qzy9ttvUzMV8OG+poGvI0eOyOeff84gG0CKMFYGQgtjcCC4MbZHShBEh9dxWgzgHzp7eN68eU02G5OPAb5z/PhxyZMnj9nXGGQDSCnGykBoYQwOBDfG9kgugujwCU6LAfyH2cOB9MG+BiC1GCsDoYdxARDc2IdxPQTR4TOcFgMAAAC4x1gZAAAgeBBEh09xWgwAAADgHmNlAACA4EAQHemC02IAAAAA9xgrAwAABDaC6AAAAAAAAAAAeEC6AwAAAAAAAAAAHhBEBwAAAAAAAADAA4LoAAAAAAAAAAB4QBAdAAAAAAAAAAAPCKIDAAAAAAAAAOABQXQAQLq58847pUePHl7f7pAhQ6Rq1ape3y4AAAAQ7BiDA0DaEUQHABgdO3YUm80mzz77bKLbXnjhBXObtkmOVatWmfYnTpzwQU8BAACA0MAYHACCA0F0AIBD8eLF5aOPPpLz58871sXGxsr8+fPlxhtv9GvfAAAAgFDEGBwAAh9BdACAw2233WYG8Z9++qljnV7WwXu1atUc6+Lj42XUqFFSqlQpyZYtm1SpUkUWLVpkbtu9e7c0atTIXM6XL1+i7Bm9b58+fSR//vxSpEgRcxqos71798qDDz4oOXPmlNy5c8tjjz0mhw4dcmkzevRoKVy4sOTKlUs6d+5sDjIAAACAYMQYHAACH0F0AICLJ598UubMmeO4Pnv2bOnUqZNLGx28z5s3T6ZPny6///679OzZU5544gn5/vvvzQHAJ598Ytrt3LlTDhw4IFOmTHHc991335UcOXLIunXrZOzYsfLaa6/J8uXLHYN7HbzHxMSYben6f/75R1q3bu24/8cff2wG/SNHjpQNGzZIVFSUvPnmm+nwygAAAAC+wRgcAAKbzW632/3dCQCA/2mmitZPnDlzphmE6+BblStXTv7991956qmnJG/evPL222+bDJZvv/1W6tat67i/3n7u3Dlz2qnWY9RMmOPHj5v7OE9qFBcXJz/++KNjXa1ateSuu+4ymS06YL/vvvtk165dpg/qjz/+kAoVKsj69eulZs2aUq9ePZORM23aNMc26tSpYzJhtmzZkk6vFgAAAJB2jMEBIDhk9ncHAACBpVChQtK8eXOZO3eu6O+serlgwYKO26Ojo81A/Z577nG538WLF11ON/WkcuXKLtc1i+Xw4cPm8vbt283A3Rq8q1tvvdUcBOhtOoDXvwknXtIDiZUrV6b6OQMAAAD+xBgcAAIbQXQAgNvTSbt27WouO2ebqDNnzpi/X3/9tRQrVszltoiIiOtuO0uWLC7XtV6jnkIKAAAAZGSMwQEgcFETHQCQSNOmTU1Wy6VLl6RJkyYut2lWig7UdfKhMmXKuCxW9kp4eLj5q6eNpkT58uXNaau6WPRUUj3FVR/XaqO1HJ2tXbs21c8VAAAACASMwQEgcJGJDgBIJFOmTOaUTeuys1y5csnLL79sJjLS7JX69evLyZMn5eeff5bcuXNLhw4dpESJEia75auvvpJmzZpJtmzZJGfOnNd93MaNG0ulSpWkXbt2MnnyZLl8+bI8//zz0rBhQ6lRo4Zp0717d1M7Uq/ffvvt8sEHH5iJlUqXLu2jVwMAAADwPcbgABC4yEQHALilg3Fd3Bk2bJgMHDhQRo0aZbJSNGtGTy0tVaqUuV1PMR06dKj07dtXChcu7Dgt9Xp00P/5559Lvnz5pEGDBmZArwPzBQsWONq0bt3aPHafPn2kevXqsmfPHnnuuee89KwBAAAA/2EMDgCByWbXGSsAAAAAAAAAAEAiZKIDAAAAAAAAAOABQXQAAAAAAAAAADwgiA4AAAAAAAAAgAcE0QEAAAAAAAAA8IAgOgAAAAAAAAAAHhBEBwAAAAAAAADAA4LoAAAAAAAAAAB4QBAdAAAAAAAAAAAPCKIDAAAAAAAAAOABQXQAAAAAAAAAADwgiA4AAAAAAAAAgAcE0QEAAAAAAAAA8IAgOgAAAAAAAAAAHhBEBwAAAAAAAADAA4LoAAAAAAAAAAB4QBAdAAAAAAAAAAAPCKIDAAAAAAAAAOABQXQACBIdO3aUkiVLuqyz2WwyZMiQ695X22hbb1q1apXZpv4NRevXr5fw8HDZs2dPmraze/du8zrNnTs32W3Hjx8vgcJd//v27Su1a9f2a78AAAACxS+//CL16tWTHDlymHHTli1bvDb+1vG/HgdkRMk91kmNO++80ywAkFwE0QEEpL///lueeeYZKV26tGTNmlVy584tt99+u0yZMkXOnz8vgWzTpk1mwDdgwACPbf766y/TplevXhLo3nzzzWQFgNOTDnj19bOWbNmySeXKlWXy5MkSHx/v0vb//u//pH79+ibo27BhQ9m+fXuyHqN///7Stm1bKVGihNf7v2TJEp8dEKTHY/fo0UN+/fVX+eKLL7zWLwAAgKT8/vvv8sQTT0ixYsUkIiJCihYtKu3atTPr/enSpUvSqlUriYmJkUmTJsl7773ncfw4cuRIWbx4caL1q1evNuOzEydOSCDSfukxmY67kzuWBoBQQxAdQMD5+uuvpVKlSvLxxx/LAw88IG+88YaMGjVKbrzxRundu7d0795dAtltt90m5cqVkw8//NBjm/nz55u/eiCQFvqDQlLBel8G0Rs0aGAeX//6ww033GAOUnTRz4cO7Hv27CkDBw50aafXf/rpJ1m3bp1Uq1ZNnn/++etuW7OHvv32W3n22WfT3E89iNLXSYP5zoHsoUOHij9447GLFCkiDz74YEBlzAMAgND16aefmjH2ihUrpFOnTmZ82rlzZ1m5cqVZ/9lnn/k1+UfPXHz55Zfl6aefNuP7fPnymTF6wuSfpILoOj5zF0TfuXOnzJw5U/xp4cKFJoCuY8APPvhAQsE333xjFgBIrszJbgkA6WDXrl3Spk0bE3j87rvvJCoqynHbCy+8INHR0SbI7olmIV+8eNEEVP1Js2I0eLt27VqpU6dOots1wK6Bdh30p4U/n2dYWJhfHz9PnjwuP0JowFtfU/3R5bXXXpNMmTKZ9TfffLOjjd1uN/2+njlz5pgfbdy9dymlBxz+/jz6wmOPPWayrv755x9zxggAAICvgtSajKDjjR9++EEKFSrkuE2Ta+644w5z+9atW9N1THL27FlTvuXw4cPmet68eV1uz5w5s1nSSrPu/e3999+XZs2amWM0TQYaPny4BDst2wgAKUEmOoCAMnbsWDlz5ozMmjXLJYBuKVOmjEsmugYou3btajIiKlSoYAaZS5cuNbdt3rxZ7rvvPlMKJmfOnHL33XeboHbC0y8166Ns2bIm0FmgQAFT+mP58uWONgcPHjQZL5r5rNvXfmkWrtaKTiqI7pxx7mzjxo0mo8Rq8/nnn0vz5s3NKam6/ZtuukmGDRsmcXFxqaoTqFnXNWvWNM9Ht/X22297DBTfddddEhkZaR731ltvlbfeeitRDUY9Rfb77793lE6xagd6qomumSrVq1c3JVYKFixoAt379u1zaaN1HfU90fUtW7Y0l/WASDN4kvO83dHnq8/79OnTjoMZZ5q59M4778jo0aOvuy3NENLXxrmOpZbe0c+HBuItL774omnz+uuvO9YdOnTIrLNey4Q1xfW5T5s2zVx2LkmT0IwZM8z7p++NPi+ttXk91/s8X++xNftJ2+gPFHog2KFDB4+nFTdu3Njx+QUAAPCVcePGyblz58zYyDmArnSsqWNdDWjrcYRatGiRGd/o+DUhbau3bdu2zbFux44d8uijj0r+/PnN+KlGjRqJStbpOM7app7VqONnPTbQcZOWC1SaXOA8Vk5YE10vaz/fffddxxhM76/t9GxbVapUKcdt1rFGwproVl9+/vlnMz7V10SD+Q899JAcOXIkUYKRbl+PM7Jnzy6NGjWSP/74I0V11vfu3Ss//vijSXTSRZOeNHM+IX3eFStWNNvXx9HH09I71vti0YSnQYMGmeMFHXNq3/WHED2rICl6uz5vd2cd6DGX3rZmzZpkH7+5q4muyTh6TKl917MJ9LPg7ngOQMZEJjqAgPLll1+aDBKdmCe5NGNdS79oMF0H0lbgVwdjGkDv06ePZMmSxQyadaCkg19rUkQdVGopkKeeekpq1aolp06dkg0bNpi65vfcc49p88gjj5jtacBUt60BWg1K6oAy4USfFh0A63PQfmltRCsrWlkDsccff9wxENYgsg6C9a8+Hx1Yal/0oCElfvvtN7n33nvNYFqf2+XLl2Xw4MFSuHDhRG01yKuDxBYtWpgsGX3t9aBAB9ua9a+0xrg+b+2X1ghX7rZl0eeiA1YN+urrqgFlrWOvg3z9UcM5Q0eD5U2aNDHvhZYF0fIpEyZMMIHj5557TlLDClgnzATSALRmTusPB9q3pGhgX9/bhGcJ6OdJ30v9LOgBgtIDCs1s17/dunVzrFOeytxorf/9+/ebz5CWonFHPyP6Y4C21eejBx8PP/ywyfrWz7In1/s8J/XY+uOAHlzojzCa1V++fHlzkKKBdHf0oEffK31vtYwOAACAL+gYVcfcOhZzR8dcert1tqomp+jYVcfhVoDbsmDBAjP+tcZyOq7TeZc02KsTp2tAV++nSR6ffPKJCUw707GyjrN1rK4BcX1sva+WadGxoI4zPY2VdexljdG07IvSsZQ+5p9//mnOVNWxph7PqIQ/GCSkY3QN9OpYX8fAOm7X4yF9jpZ+/fqZcaSWyNRxt85po39jY2MlubRf2sf777/fJMlonzWByd3x2vHjx6Vp06Zm3Kpjb/1B45VXXjGlOjW5Sen4VBNbdO6hLl26mDGvJlBpv9avXy9Vq1Z12w89jitevLh57ITvi67TftWtWzfVx29aMkffQ/1BRZO29DXSsxu0JKR13AYgg7MDQIA4efKkpvjaH3zwwWTfR9uHhYXZf//9d5f1LVu2tIeHh9v//vtvx7r9+/fbc+XKZW/QoIFjXZUqVezNmzf3uP3jx4+bxxg3blyKn8+0adPMfZctW+ZYFxcXZy9WrJi9bt26jnXnzp1LdN9nnnnGnj17dntsbKxjXYcOHewlSpRwaafbHzx4sMvzzpo1q33Pnj2OdX/88Yc9U6ZMpq0zd4/bpEkTe+nSpV3WVahQwd6wYcNEbVeuXGm2qX/VxYsX7ZGRkfaKFSvaz58/72j31VdfmXaDBg1yeS667rXXXnPZZrVq1ezVq1e3X4/2p1y5cvYjR46YZceOHfbevXubbSZ8P9evX28vUqSI/bPPPrMnx7fffmu28+WXX7qsP3z4sFn/5ptvmusnTpwwn71WrVrZCxcu7GjXrVs3e/78+e3x8fHm+q5du8z95syZ42jzwgsvJHo/nNsWKFDAHhMT41j/+eefu+1TQtf7PCf12IsXLzbrx44d61h3+fJl+x133JGo/5Z7773XXr58+SQfDwAAILV0vJWc44MWLVqYdqdOnTLX27Zta8alOpaxHDhwwIzdnMefd999t71SpUouY24dw9WrV89etmxZxzodB+n269ev77JN5zHxwoULXdbrGD3hmCtHjhxmHJyQHmtoWx0LJqTjf+f7WH1p3LixY7ypevbsacb8+pqpgwcP2jNnzmyOD5wNGTLE3N9dP9zR16ddu3aO66+++qq9YMGC9kuXLiUan+t2582b51h34cIFMw5/5JFHHOv09dP1CY+5dDz95JNPJnms069fP3tERITjOVpjdH2eVrvkHr9pf52PcfQzpsc9AOAJ5VwABAzNSlC5cuVK0f00w0RLkThnOOskMZpB4lwXUU/j0ywCzbS1HkszljVL4a+//nK7bc220Hp5WrJEMytSonXr1iZr2PkUQM2C10xnq5SL9RgWzcQ4evSoybTR01b19NLk0ue9bNky87y1nrdFM4o1s8Pdc7OcPHnSPK6+lprtrNdTSjOeNctDM3Sca4BrNpDWKndXyz7hxJ36vPXxk0NfG83Q0UW3r1n7mlWfcBJUfT00m1uzczSDRbOtk3Ls2DHzVzN7nFmPo7U4lWZg6xkGevqtZtxbnyHNRNcSKu5KtKTks+P8+Fbm1fVem+t9nq834aiekeB8FoA+P83g8UT7qJ8bAAAAX9CxcXKOD6zbrTG+jqV0XOpcdlCzovWMS71NxcTEmDNANWPaGoPromNBHTvreCphSULNnHY+w9SfNJvdebyp40U9HtBJTq1ShnpWqo7NnSU1tktIM7H1TFfNGrfoZX2d9LgjIT0DwHnOIj2O0sx75zGsvn5WPXJ9P/R90H5q6RQ9ezIp7du3lwsXLpj30qKZ93p/63FTe/ym4+j//vsvWSUUAWRMBNEBBAwtveI8WE4uLZ3iTGsBagD6lltuSdRWA8o6WPv333/NdZ2AUms+6+STepqhBkR1sGjRGnpjxoyR//3vf+bUTD1lU0+J1Dp7Fg0463Vr0YGg0nrUOgDXkhjWKZMaUNdApQ7WLRr01FMStTyGvgYarLUGgSkJZuvzPn/+vKmHnZC710KDwFrXWk/P1EGjPu6rr76a4se1WAN2d4+lwWfrdosG2hOepqpB2eQOdvVUTD0tUwfwb775pjmVVl+DhJN46sGPljDRgbQuya3h7Vz73PngxCrXon91sK+L1tDU63rgpqfJejrdOLmcfwRRVkD9eq/N9T7PSdH3R39o0oMfZ+7eT+fXKC0/FgAAACQnOH6944OEwXYtKaJja+fSJnpZS4VYk85HR0ebsczAgQMdiRnWoiVSVMJ5dhIed/jT9caL1thb55RypuPWhMkiSU0oqscKmpikr5cuOtbWcbiWUElIa5AnHBu6G99rXfjKlSs75vDR11wTbq53DKLHFFoyx/mx9XKdOnUczzM5x2/uaNkZHQdr0F+Pp7S8pR4vAYCFIDqAgKEBZJ30xnmin+RwzqhOKR1U/f333zJ79mxTG1Hr82ktbP1r6dGjh6lTqLWmdaCnA20NxmuNb6U18zT4aC1aA9CiwXANrH711VdmEh2trWjVLFca8NTsbw28agBUaz5qYFgHfkoD/r6gz1knWtUskokTJ5pBqz6uVdvaV4/rLK1ZPDqg1x8B9PXU7GnNpNY6itYPAamlA3lPAWvNMNegvGbTaNBcg+V6oKDr9bpOsqSvXVqD6J5eG3eB/ZR+nr1JXyOrbicAAIC3aSBcx9fXSwrQ2zWhwkrK0UCqno2oySyapazjNw2IWlnozuNdndhex8HuloQB6LQcd3hbaseLyaXb0XroWvtdz/rVwLK1aA12TUw5c+ZMivukgXmd1FRrmGst9KVLl5rX+q677krWMYhmo+vZvZo1ruPetWvXumS/J+f4zR29fefOnfLRRx+Zsb0et+lf6wcVACCIDiCg6IQ1OhiyZlZPDQ1Q64zqOghyVwJEJ4LUSWmcszF0MkwdJGqGumZF6ASNznSQ99JLL5kyMRrk14C4ToKpdOJS58G2tV5peRHNiNEMdM2G0KCjcykXzYzWU0a1BIkG4/X5a2A4udkhCZ+3DuzdlfJI+FposF5Phfziiy/MZJPNmjUzj+vuwCC5mcYlSpRw+1jWOut2X9H3TQfQOoGsThqUWprhonbt2pXoNis4ru+znuppXdfgtQbRddHgfvXq1ZN8DF9mb1/v8+zpsfX9OXDgQKKDIXfvp0VfIz3gAAAA8BUdH+uYQ0syuqPjLw3qajtnGjDXhBEta7Jw4UITyHUOoltlH7X8oo6D3S0pLTN5PZ7GYb4YG1pjb80ed6bHHsk589MKVGuij75+zsuMGTPMmb+LFy9Ocb+0FIu+9p9++qn83//9nzlzV1/r5E522qZNGxOs17GuZqHr++f8vibn+M0THcfrtubMmWOOJ7Qs5YgRI1I0ESuA0EUQHUBA0YC0Dl505nqtM52QBtinTJmS5DZ0UKXZyZodoQNqi25Pg9maUWBlqVj1ry16Cp9mnGiAWengMOGgSQdkOqC22mhmhvNg2zmAqkFpLdWiWdJvvfWWeW7ONbmtbA3n7Awd4Gl5kpTSbekgVAezzkHk7du3J6pZ6O5x9fRJHTAmpH3WjPnr0bImkZGRMn36dMdro/THA+2DDkLT4/Nz6dIlk12fWprFpD+yaI33hPQUXr190qRJ5nFuv/12s16D6frZ1IMCPZ1US/YkRV9TlZzXNSWu93lO6rH1hxTN1NLPqUXrar7xxhtuH0s/L/qc69Wr59XnAAAA4EzL0+mYWhM/Eo51tIyizrGjCTTazpmOyzW5QMu46KJlOpzLsei4VefL0QQMTSRISMsEepuncbUvxoZ61qmOSZ3Hdmrq1KkpKuWir+ujjz7qsmhteM1Id1fS5XrcHYesW7cu2UlUehbkfffdZ/qnj6+le5zPjEzO8Zs7CT9bWlddj/O0nzruB4Ckj/IBIJ3pAEcD3ZoBoBmuerqelqXQwLKWytDMBz3973qGDx9usoU1YK6T6egAUgfIOnDSmngWHRjp4FkD3zrI1sCpBkK7du1qbtfTAHUAqjXMta1uR08L1YC8ZkEkh2ZHz5s3zwSyNQvdGiQrDUBq1nmHDh2kW7duJgvlvffeS/VpmEOHDjWnRGpQV5+3BkU1CFqhQgWX02D1RwYdGD7wwAPmgESzj2fOnGkOJhIeROhro4NvfU01IKtt9HTLhDQLRMvQaBa0lqjRSYf0ddIfPbRuolUqxpf0PdJgsJYv0dM2rdIsKaU/dOj77K7mt762epqn1hy3zhjQkin6vurnRSevvR7rhxZ9z/WHDz2YSO7nKSnX+zwn9dj6WdAfBfr27Wt+fNJtaYaQp9qU3377rXl9rjdRKwAAQFposFZraOs4WsdfnTt3NsFwHa9oORDNNtesZD2OSDg21TKLOm7TkiTjx49PtO1p06aZ4wXdrgaGNUNax68a0NUsbC256E06DtMxlCZ8aBlLfR61a9d2jM/69+9vxmXadx2bOR83pJTWA9czXTX7Ws+O1WCzPh9NcNGgc1LZ73rMpOVM7rnnnkTzDVl0mzrO17rxenyQXHrGgI4xNdFIk2z0LANNwtGxZ8IzIj3RY0QN5qthw4a53Jba4zc9PipSpIgZD+trp0lA+oOD9tHbZyQACFJ2AAhAf/75p71Lly72kiVL2sPDw+25cuWy33777fY33njDHhsb62inX2MvvPCC221s2rTJ3qRJE3vOnDnt2bNntzdq1Mi+evVqlzbDhw+316pVy543b157tmzZ7OXKlbOPGDHCfvHiRXP70aNHzfZ1fY4cOex58uSx165d2/7xxx8n+7lcvnzZHhUVZfq6ZMmSRLf//PPP9jp16pjHL1q0qL1Pnz72ZcuWmfYrV650tOvQoYO9RIkSLvfVNoMHD3ZZ9/3339urV69uXrfSpUvbp0+fbtok/Mr/4osv7JUrV7ZnzZrVvM5jxoyxz54927TbtWuXo93BgwftzZs3N++B3tawYUOzXvuWsI9qwYIF9mrVqtkjIiLs+fPnt7dr187+33//ubTR56KvZ0Lu+umO9qFChQpub1u1apXb1yUl9LOj2/jxxx8T3TZt2jRz23PPPeeyvnHjxmb9ihUrXNbra6nr58yZ4/KZePHFF+2FChWy22w2x3O22o4bNy7R4ybnOV3v85zUY6tjx47Z/+///s+eO3du81nXy5s3b07Uf9W6dWt7/fr1k+wPAACAt2zdutXetm1bM67OkiWLvUiRIub6b7/95vE+y5cvN+MYHfP8+++/btv8/fff9vbt25vt6XaLFStmv//+++2LFi1ytNFxkG7nl19+SXR/a0y8cOHC645rd+zYYW/QoIEZp+ltOia2DBs2zDx2WFiYy3hcx//O7Tz1xd3YXMd9AwcONM9NH/Ouu+6yb9++3V6gQAH7s88+6/F1++STT8y2Zs2a5bGNNeaeMmVKkuPzhMcw8fHx9pEjR5p1erygxw1fffVVso911IULF+z58uUz49Xz58+73Jbc4zftr3Vco95++23z3uhro/266aab7L1797afPHnS42sAIGOx6T/+DuQDABBoNINFM4T0zAC4OnjwoMmc0swuMtEBAACCh5aM0bMp9SxTzXwPRnq2rY7TNVtfz0YAgPRATXQAANwYOXKkqZ+5Z88ef3cl4EyePNmc9kwAHQAAIHCdP3/e7ThOaQnAYKVzQGnNei3rAgDphUx0AAAAAACAEDN37lyz6JxBOuH8Tz/9ZOrHa/1vna8p2OgEpDrPk9ZB17rumzZt8neXAGQgTCwKAAAAAAAQYipXrmwm1hw7dqycOnXKMdmolnIJRm+99Za8//77UrVqVfPjAACkJzLRAQAAAAAAAADwgJroAAAAAAAAAAB4QBAdAAAAAAAAAAAPqInuBfHx8bJ//37JlSuX2Gw2f3cHAAAAQUirLJ4+fVqKFi0qYWHkuiSF8TcAAADScwxOEN0LdABfvHhxf3cDAAAAIeDff/+VG264wd/dCGiMvwEAAJCeY3CC6F6gGTDWi507d25/dwdeyGw6cuSIFCpUiCwwwEvYrwDfYN8KLadOnTKBYWtsCc8Yf4cWvssA32DfAnyDfStjjsEJonuBdQqpDuAZxIfGl2FsbKx5L/kyBLyD/QrwDfat0ER5kutj/B1a+C4DfIN9C/AN9q2MOQbnnQYAAAAAAAAAwAOC6AAAAAAAAAAAeEAQHQAAAAAAAAAAD6iJDgAAAAAAAGQAcXFxcunSJX93I+hroutrqHXRqYke+LJkySKZMmVK83YIogMAAAAAAAAhzG63y8GDB+XEiRP+7kpIvJYaSD99+jQTwgeJvHnzSpEiRdL0fhFEBwAAAAAAAEKYFUCPjIyU7NmzE/xNYxD98uXLkjlzZl7HIHivzp07J4cPHzbXo6KiUr0tgugAAAAAAABACJdwsQLoBQoU8Hd3gh5B9OCSLVs281cD6boPpLa0C4V7AAAAAAAAgBBl1UDXDHQgI8p+9bOflvkACKIDAAAAAAAAIY6saWRUNi989gmiAwAAAAAAAADgAUF0AAAAAAAAAEFr7ty5kjdv3hTdp2PHjtKyZUsJJjNmzJDixYtLWFiYTJ48WYYMGSJVq1ZN1baC8fn7ExOLIsM6cOCAWRKKj4+XmJgYyZ8/v/lSSkhn8k3LbL4AAAAAAABIXqBXJ0VdvHixy/pVq1ZJo0aN5Pjx4yZ43rp1a2nWrJmEslOnTknXrl1l4sSJ8sgjj0iePHlMDOvFF19M8vXavXu3lCpVSjZv3uwScJ8yZYqZJBXJQxAdGdbbb78tQ4cOTfH9Bg8ebH7pAwAAyCimTZsm48aNk4MHD0qVKlXkjTfekFq1arlt+/vvv8ugQYNk48aNsmfPHpk0aZL06NEjUbt9+/bJK6+8Iv/73//k3LlzUqZMGZkzZ47UqFFDAlqg9y8YaZ3SMmVEoqNFOJj3rg0b/N0DAEgX2bJlM0so0kB3XFyc7N2710yM2bx5c5fkzpw5c6ZquxqER/JRzgUZ1jPPPGMO7pyXn376yXH7Dz/8kOh2XfR+AAAAGcWCBQukV69eJpFg06ZNJojepEkTOXz4sNv2GhAvXbq0jB49WooUKeK2jWaN3X777ZIlSxYTRP/jjz9kwoQJki9fPh8/GwAA4OLiRc/L5cvJb3vp0vXbpnM5l+HDh0tkZKTkypVLnnrqKenbt6/b0ifjx483QekCBQrICy+8YALVSXnrrbekXLlyEhERIbfccou89957jtsef/xxkxXvTLdXsGBBmTdvnrmu2eOjRo0y2eEa+Nex1aJFi1yy7HUiTB0jVa9e3TzO+++/L5UqVTK36zhLb9cMc+dyLnr53Xfflc8//9zcrotuSx9HVatWzay788473ZZz0fXdunWTPn36mOoMOo5LmES6Y8cOqV+/vmTNmlVuvfVW+fbbb802E54pEIrIREeG5a4sy9mzZx2X9UtIv2gBAAAyMj1luEuXLtKpUydzffr06fL111/L7NmzzcFoQjVr1jSLcne7GjNmjKnnqZnnFusADwAApKORIz3fVrasSLt2166PG5c4WG4pWVKjsteuT56sv6y7tknHs/o/+OADGTFihLz55pvmh/uPPvrI/GCfcLyxcuVKExvSv9HR0SYArvEgHfu489lnn5kz7HRb9957rxkT6RjphhtuMOVl2rVrJ61atZIzZ844MsSXLVtmkgweeughc10D6BoU1zFV2bJlTRLnE088IYUKFZKGDRs6HkvHURrg16C5Bq01YN24cWNZv369GUdpe2cvv/yybN++3ZR9scZYGgzX9noGod6/QoUKEh4e7vF10yC8Jk+sW7dO1qxZYwLt+vrdc889Jhteg+433nijuf306dPy0ksvSUZBEB0AAACAWxcvXjRn4vXr18+xTueM0QM4PbBKrS+++MJks+tB5vfffy/FihWT559/3uMB64ULF8xi0YNDK5NLl3QvPQKvirfZxG6zmb/wsvTePxBQ9PtRy0Ck+/ckAvazYC0ukiqjpbc5357wekrbprJk11dffZWoZIkGdK9s0vV5WX+19NyTTz5pgsBq4MCB8s0335jgtvNroGfBadtMmTKZrHItlbJixQqTue6OBrU7dOhgqhToGXU333yzrF271qzXTG4NrOfIkUM+/fRT+b//+z9zn/nz50uLFi3Mc4iNjZWRI0fK8uXLpW7duuZ2Dez/+OOPpuxwgwYNHP3TEsQ65rJYZwFqVnvhwoVdnq/+1cfVzHYdM1m3W+2tgHrC+yW8XLlyZVOWT2mpvalTpzqC99988438/fff5gcH62xDzfbX5+z2sxVArP65Gzsm9zuSIDoAAAAAt44ePWoOUp0PxJRe19N5U+uff/4xp0JrptOrr74qv/zyizl9WDOj9MA0Ic3YcjeXzZEjR8zBaLrS2t3wKg2en4yKEj30DgvgA/Cg5KHsEjIGDQydPHnSBI70B1BkXFpORD8Ply9fNouLPn0831E/N87te/b03FZ/CHVu27Vr4jYJHzsZtN8anNZAtzPNrtYAufWcrECo9fx27twpTz/9tMvz1XlXNABsrdP7aEkS3UesdTrG2bZtW+LX6SrN9NbgvBXE11ImderUMcFm6z6PPvqoyYRv27atqXig5VU081xv1/GTZqVr4Dlh4oJmwGsba9vW9Wsv35XLzu+j9QOJ83XrvU7qfu7a6nYqVqzo0kZfj0OHDpl1+tw1A16D8lab2267zfzVPnt6zQKB9Rk5duyY+fHDmWbUJwdBdAAAAADpSg9i9EBWM7GsGp16wKqnNbsLomsmvAbcnTPRrdOYc+fOna59N5NfwutBdM1BLxQdTRDd2yIj/d0D+Pm7VgN8+l1JED1j0x+cNVCYOXNms7hIeD0pvmqbBP3saga31iB3ppOdX3mYK8/J+ow7Pz/NLne+btUJt9bpffQHfOc2uk6DyYleJye6XV2sYGzCx9bSLBr4j4mJMRnnmh2uGe56u/Xjv2bX65l4zrT2ubbRbVsTfzr3w7rs/D7qYyd8Trpc737u2up2Er4eVl8ye3iNrcsJX+tAY/Vf695raRxnCa973IaP+gYAAAAgyGmmkR4UaQaSM73uadLQ5NDao5r55ax8+fLyySefuG2vB5W6JGQd/KUrgrw+YdNM2asLvIjAaYanQTG/fFcioFiBVmsJRgn7bV1P+Lysv1qaZcOGDS4/zuv1pLaVcLvu6Hjl559/NrXPrXarV6824xrrPlpDXH/s//jjj83koFq+zqpDrjXJdUzz77//Oib49PRcE75f7tYn/KuPo1nhzvezxlDWD2tJvbaePiM2m838kKH91rIy1lmKzq9pIH+2rP65+z5M7vdj0H2LTps2TUqWLGl+Jahdu7Y5fcOT33//XR555BHTXl+oyTqpQRq3CQAAAGQUeiBWvXp1UxvUogdget2q45kaenCpp1k7+/PPP6VEiRJp6i8AAIB68cUXZdasWWaizL/++svU7t66dWuaA729e/c229T65bpdnYBd65/rpJ7OHn/8cXOGnWaiWwF3lStXLtO2Z8+eZjtaY3zTpk2mXI1eTyuNb+rz1HGWluXTUj6RkZEmG37p0qUmEULLPKXGPffcIzfddJP5YUIfQ39MGDBggLktkAPo3hJUmegLFiwwp3Hqh1CD3RoU1wmJ9IOhH4iEtMaQzmCrv/joh9Mb2wQAeHbgwAGzJKQBFz2VTScycfcrr2Yk6gIACDw6VtaDJS2/UqtWLTNe1vqenTp1Mre3b9/enI6sdcutmp5//PGH4/K+fftky5Yt5lRsnaBK6di8Xr16ppzLY489ZpJYZsyYYRYAAIC00sC1zsGiAWstoaLjDa2hntbE2ZYtW5qx0IQJE8wYSScFnTNnTqKscn38ESNGmAQBTR5wNmzYMFNmScdO2se8efOa2uI6T0xa6STtq1atMuM2nURVa8Br315//XV57bXXzKShd9xxh2mTUpkyZZLFixebSVdr1qxpYq7jxo2TBx54INklUYKZzR7IU6cmoEFufZO0WL8VlNHTI/TXpb59+173l5gePXqYxVvbdK7JqHWK9JecdK/JCK/SA0Jrxmd9X/UXQgDJN2TIELcTv13P4MGDzX0BpIyOW/R0Sv3hn9O0g18gjyl1rKwHSVp/VCe50gMxHUcrPTDTsfbcuXPN9d27d5sDyoQaNmzocsCmtUC11rlmcWl7PRDVA7+Af61q1Ejfx8sgNdEPlykjkdRE976rp9kjY2KcAIsGkXft2mX+v80Iwc6kMqm1HN17772Xpu1YE3lqne2MkIGdlJ9//lnq168v0dHRJks9GPeB5I4rgyYTXbNYNm7caAbaFv1PoHHjxrJmzZp03eaFCxfM4vxiO89qi+Dl/P7xfgIpp8GP+++/32Xd+fPnpUGDBuayBk9y5MiR6H6ahc7+BqSc7jc6iGf/CQ2B/D527drVLO4kzGTSgHpy8nT0/4uE/2cAAAB4g1an0KoTWm1CM6g//PBD+fbbb015FaTeZ599ZpJPy5YtawLn3bt3N5n2gRxA95agCaJrHR8tjG8Vrrfo9R07dqTrNvV0C3eZlkeOHHHMsovg/ZJ1fj81+Acg+XRwcsMNN3jcr/R0f+tsj4Q0SwZAyoOumjGhAUsyzILf6dOn/d0FAACAkKAZ4kuWLDElVTRWpxON6gTmmjiLtI1XX3nlFdm7d6+ZgF5fTy1tkxEETRA9kGjmup5u6pyJriVgtJ5RoJ16i5SXc7Ho+0k5FyDt2K8A3wbR9QBB9y2C6MEvI59eDQAA4E06kaZmnsO72rdvb5aMKGiC6PrrhmY46iyyzvS61jNKz21GRESYJSE9eOUANrg5v3+8n4B3sF8BvqVBdPat0MB7CAAAAASmoBmph4eHS/Xq1WXFihUu2Vd6vW7dugGzTQAAAAAAAABA6AiaTHSlJVQ6dOggNWrUkFq1asnkyZNNmYBOnTqZ2/V0Aq23qzXLrYlD//jjD8flffv2yZYtW0w93jJlyiRrmwAAAAAAAACAjCuoguitW7c2kz0OGjRIDh48KFWrVpWlS5c6JgbVovbOp8Hu379fqlWr5rg+fvx4szRs2FBWrVqVrG0CAAAAAAAAADKuoAqiq65du5rFHSswbilZsqTY7fY0bRMAAAAAAAAAkHEFTU10AAAAAAAAAADSG0F0AAAAAAAAABmSVraw2Wxy4sQJf3claOzYsUPq1KkjWbNmNaWxd+/ebV5DnYsyVF9/gugAAAAAAAAAAk7Hjh1NgPXZZ59NdNsLL7xgbtM2wWLIkCEm6BzsBg8eLDly5JCdO3fKihUrpHjx4nLgwAGpWLFikoHxO++8U3r06OGyrl69eua+efLkkUBGEB0AAAAAAABAQNIA7UcffSTnz593rIuNjZX58+fLjTfeKIHg4sWLkhFcvPo8//77b6lfv76UKFFCChQoIJkyZZIiRYpI5swpn34zPDzc3FeD7oGMIDoAAAAAAACQEcVd9LzEX05B20vXb5tKt912mwmkf/rpp451elkD6NWqVXNpe+HCBenWrZtERkaaUiMa6P3ll19c2ixZskRuvvlmyZYtmzRq1MiUIknop59+kjvuuMO00cfWbZ49e9Zxe9myZWXYsGHSvn17yZ07tzz99NNm/SuvvGK2nT17dildurQMHDhQLl268trMnTtXhg4dKr/++qsJGOui65RmbD/11FNSqFAhs7277rrLtEvKb7/9ZtppHzWQrX04c+aMue2bb74xzz9hJnj37t3NfZL7PEuWLJnoedpsNtm4caO89tpr5rJm1zuXc9HL+rqqfPnyOc4W0OX777+XKVOmOJ6/tk2Yta6vSd68eWXZsmVSvnx5yZkzpzRt2tRkq1suX75s+qrt9Lnr696hQwdp2bKl+ApBdAAAAAAAACAj+n2k52XPAte228d5brv7A9e2OycnbpMGTz75pMyZM8dxffbs2dKpU6dE7fr06SOffPKJvPvuu7Jp0yYpU6aMNGnSRGJiYszt//77rzz88MPywAMPmICvBq779u3rsg3Nstag7SOPPCJbt26VBQsWmGBz165dXdpNmDBBqlSpIps3bzbBcpUrVy4TBP7jjz9MsHjmzJkyadIkc1vr1q3lpZdekgoVKpiAsC66TrVq1UoOHz4s//vf/0yAWn84uPvuux39TkgD3fq8NEitPxIsXLhQvv32W0cf9b4aYNbXwhIXF2eeS7t27VL0PMePH+/yPLXf+hz0uejll19+2aW9BuOtx9VyL9pGXwtd6tatK126dHE8f23rzrlz58zjvvfee/LDDz/I3r17XR5nzJgx8sEHH5jPxM8//yynTp2SxYsXiy8RRAcAAAAAAAAQsJ544gkT4N2zZ49ZNHCq6xIGlt966y0ZN26c3HfffXLrrbeaILZmWc+aNcu00dtvuukmEwC/5ZZbTEA5YU31UaNGmfVau1szzrVm9+uvvy7z5s0zZWQsmtGtgWTdni5qwIABpr1mcGugXgO/H3/8sblN+6FZ1VryRMuX6KLr9HmtX7/eBMJr1KhhHlMDyBoEX7RokdvXQ0vZaF+0T1qHXPsydepUE3Q+dOiQKa/Spk0b086itcs121uD5ml5nkWulm3R56KX9a8zfez8+fOby3pGgLbReue6aOkWzdK3nr+2dUez96dPn25eD/1BQQP72n/LG2+8If369ZOHHnpIypUrZ567vl6+lPJCNQAAAAAAAACCX4VXPd9mS5B7W753Em0T1LO+xXXyyLTSMifNmzc3Wd52u91cLliwoEsbzazW4Ovtt9/uWJclSxapVauWbN++3VzXv7Vr13a5n2ZHO9MyKpqZrZnOFn3M+Ph42bVrlwnaqurVqyfqp2ZzayBa+6KlVbTsiJZBSYo+nrbVsiTOtAa8bscdfR6aHa6Te1r0eWsfNfu7cOHCJkBep04d2b9/vxQtWtQ8H33drGDz9Z6nllJRGshObxpot36YUFFRUSZTX508edL8UKDvq0WD8fp+aN99hSA6AAAAAAAAkBFlCvd/2xSUdLFKjUybNk18RQPazzzzjKm5nZDzRKbOAWy1Zs0aE7jWuudaakUzr3VCVM16v97jaZBYa4MnlJbs6po1a5pAtPbhueeek88++8xRgz0tzzM96I8fzrRmugb4/YkgOgAAAAAAAICApvW7L168aAKqGqROSAPGWi5ES72UKFHCrNPMdK0ZriVLlGZXf/HFFy73W7t2rct1LR+iNc21nro7noK5q1evNo/bv39/xzotPeNM+6e1yRM+3sGDB02JFC0Dkxz6PDQgriVsrCC3Pu+wsDBTpsaiQX3NNL/hhhvMbZqJntznmRbh4Vd+REn4XN09/5TSHyc0017f1wYNGjgeR2vgV61aVXyFmugAAAAAAAAAApqW7NAyJhr4dVdLW4PJmnHdu3dvWbp0qWmnk1jqJJWdO3c2bZ599ln566+/TBste6I1w52zs9Urr7xiAuKa9a6Tj2r7zz//PNGEmwlpXXGdAFMzv7UMi5Z10exvZxok11Iput2jR4/KhQsXpHHjxqakTMuWLeWbb76R3bt3m8fXYPyGDRvcPpYGx7NmzSodOnSQbdu2ycqVK+XFF1+U//u//zMBZud2GlweMWKEPProoxIREZHm55kc+mOC/tjx1VdfyZEjR0zWu/X8161bZ56jPv/Ull/R56o13bW/+j52795djh8/bh7TVwiiAwAAACFED8Z++OEHM7HU22+/LZ9++qk5WAMAAAh2Wl88qRrjo0ePNhNnajBZM62jo6Nl2bJlki9fPkeZkk8++UQWL15saorr5JUjR4502UblypXl+++/lz///FPuuOMOqVatmgwaNMjUFU9KixYtpGfPniYIrRnRGqAeOHCgSxvtm2bUN2rUyNR5//DDD03gd8mSJSarulOnTnLzzTebSUE1i905IJ6wZrg+r5iYGFO2RQPkd999t5lg05lmmWvtcK19rgF1bzzP5ChWrJgpa9O3b1/zHKzAvE60qj+A6KSv+vz1R4fU0B8A2rZtK+3btzc/QOjkpnp2gv6w4Cs2u78LyoSAU6dOmVMJtLD99SYLQGDT02CsWYX1fc2VK5e/uwQEPfYrwHc0c0Mn2NFZ7/X0TGTsMaWewjtlyhT58ssvzanLuq1s2bKZgysNrJcuXVqefvppk4EV7N/Ffh1/+2FyrVAXb7PJ4TJlJDI6WsI4PPUuDxmMyBgYJ8ASGxtrflAvVaqUT4OMGYWGUnXCUC2/4svMZ6Ts+05L3Dz22GMybNiwFO0DyR1X8i0KAAAABDnNfGrdurU5RVZPAz59+rQcO3ZM/vvvP3MKs56eO2DAAFmxYoXJblq+fLm/uwwAAACkimbpz5w502TR//bbb6aMjwbJH3/8cfEVJhYFAAAAgpxOEqWnJmfJksXt7ZqFrovWzdT6oAcOHEj3PgIAAADeoGfXaC17LQ+jZwZUrFhRvv32W5ON7isE0QEAAAKcBjzdBT31tEUt1ZE/f363p2lHRUWZBaHvmWeeSXZbrUGpCwAAABCMihcvbkoZpieC6AAAAAFOJ4fUiXlSavDgwTJkyBCf9AmB58SJE7Ju3TozqZLSCUUffvhhf3cLAAAACHoE0QEAAIIgy1hrXjs7f/681K9f31z+4YcfJEeOHInuRxZ6xtK2bVszydV7770n77//vowfP54gOgAAcNCyF0BGZPfCZ58gOgAAQIBzV5bl7NmzjstVq1aVXLly+aFnCCRHjx6VX375RWbNmmUmEQUAAFDWnCk62Xi2bNn83R0g3elnX3maPyg5CKIDAAAAISBfvnzmb+fOnaVbt26yY8cOf3cJAAAEgEyZMknevHnl8OHD5nr27NnFZrP5u1tBndWsZ/9lzpyZ1zEI3isNoOtnX/cB3RdSiyA6AAAAEAIeffRRxwHdhAkT3E42CwAAMqYiRYqYv1YgHWkLzMbHx5uxFkH04KABdGsfSC2C6AAAAEAIePrppx2X9VTVyZMnJ2qzb98+KVasWIq3PW3aNBk3bpwcPHhQqlSpIm+88YbUqlXLbdvff/9dBg0aJBs3bpQ9e/bIpEmTpEePHh63PXr0aOnXr590797dbZ8BAEDaabBXywNGRkbKpUuX/N2doKYB9GPHjkmBAgVIWggCOi5OSwa6hSA6AAAAEOI0+D1ixAhTL92qCZlcCxYskF69esn06dOldu3aJtDdpEkT2blzpzkQT0i3X7p0aWnVqpX07NkzyW1rDfe3335bKleunOLnBAAAUk6Did4IKGb0ILoGZrNmzUoQPQPhnQYAAABCwPHjx6Vt27ZSsGBBKVq0qLz++uvmIE+zwjWorQHrOXPmpHi7EydOlC5dukinTp3k1ltvNcF0raU6e/Zst+1r1qxpstbbtGkjERERHrd75swZadeuncycOdNRzx0AAAAIRGSiAwAAACGgb9++snr1aunYsaMsW7bMZIEvXbrUZEh99913UqdOnRRv8+LFi6Ysi5Zbsej2GjduLGvWrElTf1944QVp3ry52dbw4cOTbHvhwgWzWE6dOmX+6o8EuqQrap96XbzNJnabzfyFl6X3/oGAot+PVu1mAN7DvhVakvs+EkQHAAAAQsD//vc/mTt3rtx1113StWtXk31etWpVGTlyZKq3efToUYmLi5PChQu7rNfrO3bsSPV2P/roI9m0aZPJjk+OUaNGydChQxOtP3LkiMTGxkq6KlMmfR8vA9Dg+cmoKLHrjzR2/RdewwSCktEDQydPnjTBPkpOAN7DvhVaTp8+nax2BNEBAACAELB//34pX768uVyyZElTp/OJJ56QQPPvv/+aSUSXL19u+pgcmgmvddmdM9GLFy8uhQoVkty5c0u6io5O38fLIEF0zUEvFB1NEN3b3MxbgIwV6NPJJPW7kkAf4D3sW6ElueNRgugAAABACNBsqMyZrw3vddKwbNmypWmbWl9dt3Po0CGX9Xq9SJEiqdqmloc5fPiw3HbbbY51mu3+ww8/yNSpU03ZloQTnmltdXf11fXANd0PXgny+oRNs/muLvAigjsZngb6/PJdCYQ49q3Qkdz3kCA6AAAAECJB9LvvvtsRSD9//rw88MADEh4e7tJOy6gkl963evXqsmLFCmnZsqUj+0qva8mY1NA+/vbbby7rdNLScuXKySuvvJIogA4AAAD4G0F0AAAAIAQMHjzY5fqDDz7ole1qGZUOHTpIjRo1pFatWjJ58mQ5e/asCXyr9u3bS7FixUzdcmsy0j/++MNxed++fbJlyxbJmTOnlClTRnLlyiUVK1Z0eYwcOXJIgQIFEq0HAAAAAgFBdAAAACAEg+je0rp1azOB56BBg+TgwYNmstKlS5c6Jhvdu3evy2mwWpu9WrVqjuvjx483S8OGDWXVqlU+6SMAAADgSwTRAQAAACRJS7d4Kt+SMDCuk5pqaZmUILgOAACAQEb1ewAAAAAAAAAAPCCIDgAAAAAAAACABwTRAQAAAAAAAADwgCA6AAAAEELmzZsnFy5cSLT+4sWL5jYAAAAAKUMQHQAAAAghnTp1kpMnTyZaf/r0aXMbAAAAgJQhiA4AAACEELvdLjabLdH6//77T/LkyeOXPgEAAADBLLO/OwAAAAAg7apVq2aC57rcfffdkjnztaF+XFyc7Nq1S5o2berXPgIAAADBiCA6AAAAEAJatmxp/m7ZskWaNGkiOXPmdNwWHh4uJUuWlEceecSPPQQAAACCE0F0AAAAIAQMHjzY/NVgeZs2bSQiIsLfXQIAAABCAjXRAQAAgBBy1113yZEjRxzX169fLz169JAZM2b4tV8AAABAsCKIDgAAAISQxx9/XFauXGkuHzx4UBo3bmwC6f3795fXXnvN390DAAAAgg5BdAAAACCEbNu2TWrVqmUuf/zxx1KpUiVZvXq1fPDBBzJ37lx/dw8AAAAIOgTRAQAAgBBy6dIlRz30b7/9Vlq0aGEulytXTg4cOODn3gEAAADBhyA6AAAAEEIqVKgg06dPlx9//FGWL18uTZs2Nev3798vBQoU8Hf3AAAAgKBDEB0AAAAIIWPGjJG3335b7rzzTmnbtq1UqVLFrP/iiy8cZV4AAAAAJF/mFLQFAAAAEOA0eH706FE5deqU5MuXz7H+6aefluzZs/u1bwAAAEAwIhMdAAAACDF2u102btxoMtJPnz5t1oWHhxNEBwAAAFKBTHQAAAAghOzZs8fUQd+7d69cuHBB7rnnHsmVK5cp86LXtV46AAAAgOQjEx0AAAAIId27d5caNWrI8ePHJVu2bI71Dz30kKxYscKvfQMAAACCEZnoAAAAQAj58ccfZfXq1aZ8i7OSJUvKvn37/NYvAAAAIFiRiQ4AAACEkPj4eImLi0u0/r///jNlXQAAAACkDEF0AAAAIITce++9MnnyZMd1m80mZ86ckcGDB0uzZs382jcAAAAgGFHOBQAAAAgBmTJlkgMHDsiECROkSZMmcuutt0psbKw8/vjj8tdff0nBggXlww8/9Hc3AQAAgKBDEB0AAAAIAXa73fy94YYb5Ndff5WPPvpItm7darLQO3fuLO3atXOZaBQAAABA8hBEBwAAAEJM5syZ5YknnvB3NwAAAICQQBAdAAAACBHvvPOO5MyZM8k23bp1S7f+AAAAAKGAIDoAAAAQIqZPn25qo3uik4wSRAcQkGrU8HcPQo/NJlKmjEh0tNb88ndvQsuGDf7uAYB0RhAdAAAACBEbNmyQyMhIf3cDAAAACClh/u4AAAAAgLTTLHNfmTZtmpQsWVKyZs0qtWvXlvXr13ts+/vvv8sjjzxi2mufJk+enKjNqFGjpGbNmpIrVy4T9G/ZsqXs3LnTZ/0HAAAAMlQQPSUDeLVw4UIpV66caV+pUiVZsmSJy+0dO3Y0g3vnpWnTpj5+FgAAAIB32X10qv6CBQukV69eMnjwYNm0aZNUqVJFmjRpIocPH3bb/ty5c1K6dGkZPXq0FClSxG2b77//Xl544QVZu3atLF++XC5duiT33nuvnD171ifPAQAAAMgw5VysAbzWetQAuma16ABes1bcnba6evVqadu2rcl0uf/++2X+/Pkmy0UH/xUrVnS006D5nDlzHNcjIiLS7TkBAAAA3qBB7utNKpoaEydOlC5dukinTp3MdR2Lf/311zJ79mzp27dvovaaYa6Lcne7Wrp0qcv1uXPnmvH8xo0bpUGDBonaX7hwwSyWU6dOmb/x8fFmSVc+zPjPqOJtNrHbbOYvvCy994+04P33OvYtHwqmfQtep2MPTV5I9zEIfCK572NQBdFTOoCfMmWKCZD37t3bXB82bJjJdJk6daq5r3PQ3FOWDAAAABAsQXRvu3jxogls9+vXz7EuLCxMGjduLGvWrPHa45w8edL8zZ8/v9vbNSlm6NChidYfOXJEYmNjJV3pJH3wKg3wnYyKEj2XIozJD73LwxkjAYl9y+vYt3womPYt+CToqmMXDaTruAjB7fTp06EVRE/NAF7Xa+a6M81cX7x4scu6VatWmcyXfPnyyV133SXDhw+XAgUKeOxLQGXCwKuc3z/eT8A72K8A32DfCj2B+B4ePXpU4uLipHDhwi7r9fqOHTu89rx79Oght99+u8vZos70GMB5XK/j7+LFi0uhQoUkd+7ckq6io9P38TJIoE/zZAtFRxPo87ZgmmiYfcvr2Ld8KJj2LXidjl20HLSOQwiiBz8tAR5SQfTUDOAPHjzotr2ut2im+sMPPyylSpWSv//+W1599VW57777TAA+U6ZMgZ8JA6/SGp7O7+f58+f92h8gFLBfAb7BvpVxs2BCjdZG37Ztm/z0008e2+iZo+5KLuqBa7ofvBKI8gmbZvNdXeBFwRTc4b33CfYtHwmmfQs+oUF0v4xD4HXJfQ+DJojuK23atHFc1olHK1euLDfddJPJTr/77rsDPxMGXuU8mZW+n7ly5fJrf4BQwH4F+Ab7VsbNgklPBQsWNIklhw4dclmv171RDrFr167y1VdfyQ8//CA33HBDmrcHAAAA+ELQBNFTM4DX9Skd8JcuXdo8VnR0tMcgekBlwsCrnN8/3k/AO9ivAN9g3wo9vngPR48eLc8++6zkzZs3VfcPDw+X6tWry4oVK6Rly5aOU5j1ugbAU0triL744ovy2WefmeQVPSsUAAAACFRBc7TlPIC3WAP4unXrur2Prndur3RiUU/t1X///SfHjh2TqKgoL/YeAAAASH8jR46UmJiYNG1Dz8CcOXOmvPvuu7J9+3Z57rnnzJkQnTp1Mre3b9/eZd4inctoy5YtZtHL+/btM5c1ScW5hMv7778v8+fPN2dRaLlFXShLBAAAgEAUNJno1gC+Q4cOUqNGDalVq5ZMnjw50QC+WLFipma56t69uzRs2FAmTJggzZs3l48++kg2bNggM2bMMLefOXPG1DZ/5JFHTHa61kTv06ePlClTxkxACgAAAAQzzfhOq9atW5u6+4MGDTKB7qpVq8rSpUsdcw/t3bvXJYt+//79Uq1aNcf18ePHm0XH5Zp1rt566y3z984773R5rDlz5kjHjh3T3GcAAAAgwwbRUzqAr1evnsluGTBggJkwtGzZsrJ48WKpWLGiuV3Lw2zdutVk1Zw4cUKKFi0q9957rwwbNsxtuRYAAAAgI9LSLZ7Kt1iBcUvJkiWvG7z3RnAfAAAASC9BFURP6QBetWrVyizuZMuWTZYtW+b1PgIAAACB4I8//jCJIgAAAAAyUBAdAAAAQPIUL17c310AAAAAgl7QTCwKAAAAAAAAAEB6I4gOAAAAAAAAAIAHBNEBAAAAAAAAAPCAIDoAAAAQgi5cuODvLgAAAAAhgSA6AAAAEAJGjx4tUVFR8tVXX5nrjRo18neXAAAAgJCQ2d8dAAAAAJB27777rmzYsEFeeOEFyZMnj7+7AwAAAIQMgugAAABACChWrJhZPvjgA3nooYfk2LFj/u4SAAAAEBIo5wIAAACEgLx588rly5clR44c8sYbb8ihQ4f83SUAAAAgJJCJDgAAAISARYsWOS7fcsstcuLECb/2BwAAAAgVZKIDAAAAGcCmTZvk/vvv93c3AAAAgKBDEB0AAAAIEcuWLZOXX35ZXn31Vfnnn3/Muh07dkjLli2lZs2aEh8f7+8uAgAAAEGHci4AAABACJg1a5Z06dJF8ufPL8ePH5d33nlHJk6cKC+++KK0bt1atm3bJuXLl/d3NwEAAICgQyY6AAAAEAKmTJkiY8aMkaNHj8rHH39s/r755pvy22+/yfTp0wmgAwAAAKlEEB0AAAAIAX///be0atXKXH744Yclc+bMMm7cOLnhhhv83TUAAAAgqBFEBwAAAELA+fPnJXv27OayzWaTiIgIiYqK8ne3AAAAgKBHTXQAAAAgRGgd9Jw5c5rLly9flrlz50rBggVd2nTr1s1PvQMAAACCE0F0AAAAIATceOONMnPmTMf1IkWKyHvvvefSRjPUCaIDAAAAKUMQHQAAAAgBu3fv9ncXAAAAgJBETXQAAAAAAAAAALwdRI+OjpZly5aZCYyU3W5P7aYAAAAAAAAAAAiNIPqxY8ekcePGcvPNN0uzZs3kwIEDZn3nzp3lpZde8kUfAQAAAAAAAAAIjiB6z549JXPmzLJ3717Jnj27Y33r1q1l6dKl3u4fAAAAAAAAAADBM7HoN998Y8q43HDDDS7ry5YtK3v27PFm3wAAAAAAAAAACK4g+tmzZ10y0C0xMTESERHhrX4BAAAASKX4+Hgzh9Hhw4fNZWcNGjTwW78AAACADBFEv+OOO2TevHkybNgwc91ms5mB+dixY6VRo0a+6CMAAACAZFq7dq08/vjj5ixRu93ucpuO3ePi4vzWNwAAACBD1ETXYPmMGTPkvvvuk4sXL0qfPn2kYsWK8sMPP8iYMWN800sAAAAAyfLss89KjRo1ZNu2beZs0ePHjzsWvZ4a06ZNk5IlS0rWrFmldu3asn79eo9tf//9d3nkkUdMew3aT548Oc3bBAAAAIIqiK4B8z///FPq168vDz74oCnv8vDDD8vmzZvlpptu8k0vAQAAACTLX3/9JSNHjpTy5ctL3rx5JU+ePC5LSi1YsEB69eolgwcPlk2bNkmVKlWkSZMmplSMO+fOnZPSpUvL6NGjpUiRIl7ZJgAAABA05VwuXbokTZs2lenTp0v//v191ysAAAAAqaJZ3VoPvUyZMl7Z3sSJE6VLly7SqVMnc12PBb7++muZPXu29O3bN1H7mjVrmkW5uz012/To4sUrS0JhYSKZnQ513LWx2GwiWbIkv60zLZeToGSOS1vn9t5qaz0/f7d17nNa2+ptWmbIqt/vre3SNvHnOVOmK4vS1/vyZc/bdW6r27x0yTttnfdP57YJ5m+47nNLqq2n7aV320D4jtD+6uKpfTB8RwRiW4t+dyVVJk0/69brlpK2Kdk/A6FtenxHeLPt9f6/v15bfW2sMYg+r5SMI5zban+T2u990VaFh6eu7eXLSX8HpqSt9tfal3zV9nr7nHNbbwfRs2TJIlu3bk3JXQAAAACkoxdffFFeeuklOXjwoFSqVMmM4Z1Vrlw52dvS8o0bN26Ufv36OdaFhYVJ48aNZc2aNanqX2q2eeHCBbNYTp06Zf7ax48Xe0REovZ2/QGhXbtrK8aOFZuHA2t7iRIiHTteWzFpktjOnXPfNirK9WDr8GGxeQgw2PV1j4y8tuLIEc990AP1woWvrTh6VGweDsLtemCv/bAcOyY2p9fGpa32tWjRaytiYsQWG+u2rWlfrNi1K8ePi+38ec9tnV+LEyc8vmamrZ6RYAWGTp4U29mzrg1sNsmhzzcmRuL1NbMCF6dOie3MGc/b1bbW5/v0abGdPu25baFC1w7uz5wR29XPkNu2BQuKWJ+rs2fFdvKk57YFCohkzXrlyvnzYjt+3HPb/PlFsmW7ciU2VmxJlFey58snkj37lSsXLojt2DHPbfUMk5w5r1y5eFFsR49eu3HECNe2jRuL3H77lSv79ontnXc8b7dhQ5E777z2WX/rLc9t69YVuffea5+HKVM8t61RQ6R582uv7/jxVy4fPJi4rb4G+lqYK3axuWnjaKuvrb7GVyXZVt8zfe8shw6JzUMQyXzH6GfCoq+FhwCOXT9j+lkLkO+IeJtNsu7ZY17bhHNkBNV3hHNbfR0C4TvC+gz88ovY/vc/z23bthW5+eYrV379VWyff+657aOPilSocOXK77+LbdEiz20ffFCkatUrV/78U2wffui57X33idSqdeXK7t1ie/ddz20D+TvCXdsqVURatrz2/TdqlOe25cuLPPaY47otwfdjisYRdrvk0M9pjhwSX7JkysYRTz99bcXUqWI7ccLzZ/L556+tePttsR054r5t3rwi3btfWzFrltgOHHDfVr9Xe/e+tuK998Sm3xPu2ur+8+qr11Z8+KHYoqPdtjXtBw++dmXRIrFt3+65rY4DrX3uiy/E9uuvntu+/LJ5rY3//U9sGzZ4bquvg74eavlysSUxXrU/95z5Hta5Pn0ysegTTzwhs2bNMqdnAgAAAAgsWo9cPfnkk451WptcAygpnVj06NGjpn1h5+CNaCynsOzYsSNV/UvNNkeNGiVDhw5NtF5LS2ZyE6C6fOqUxDqVhsmhAREPgay406flfMK2HgJDpq1Thn/2uDgJ8xDIis+aVc45t7XbJcxDcCo+PNylrYZZM3nogwbTzjq3DQuTTB4CThpMc26bNXNmyZxEEOmMc9u9eyVzEsHjM1rK82pWYcR//0mWJILHZ2+66UoQUNvu3y9ZEgSENZAXezUIfK506SuBSE1oO3BAwp0DwgmcK1XKvM6m7aFDEp5EOaBzJUtK/NWgdJYjRyQiiQDr+RIlJO5qf7SvEVb2pLu2N94ocblzm8uZjx+XrElktMXeeKNcvlpSSV/bq6F3921vuEEuXw0eZzp1SrIlkSV4oWhRuXQ1IJzpzBnJ5hwMSPDZuHD8uFy6+jqFHT0q2ZMIVl48cUIupqKt/uhggkseXDp5Ui5Ybc+du9bWKQDuaJsvn1y44YYrV+LiJKeHH4yUvrb6GltyJhHgvZwrl8Rq4OuqHPpDhYcgSlyOHHK+dOlrbS9d8vx9ki1bQH1HaBBdA+P6HeHuR4Jg+Y5waRso3xFXt6XPKyKJz/v5Y8ccbTPHxEjWJNrGxsTI5VS0zXTsmGRLoq3zfp+StgH3HXGdthpEz5lE24Rjg5S0TTiO0DFV7NX9NT6l4winttlPn5YwD/0w3xHJbZspk0vbbKdPex4bxMfL2eS21e8Tp7ZZT52SzEm8bmdS2vbqvhxx8qRkSaLt2SNHxH719mS1vfq9G37ihIQn0fbc0aOi3/ynk/jec2azu/s58jqZLfPmzZOyZctK9erVJYf1S4DTqZkZjWbCaH3JkydPSu6rAygEJz0Qy3l1wKzva65cufzdJSDosV8BvsG+FXq8Nabc4yGbyFJCM6+Taf/+/VKsWDFZvXq11NUssqv69Okj33//vaxbty7J++vEoT169DBLWrbpLhO9ePHicvzQIfevlS/LudSvf+369TKXEpZU8HfbAC3VoIG+IzfdJIX+/vvKpF2BUKohVNr+/HPwlGqwsl+T+9ySauvNEi1Wn4Owrdm3SpeWQtHREkY5F++2tSbDppxLhiznopnLR44ckUKFCkkY5VyCvpyLjivz5ct33TF4ijPRt23bJrfddpu5rBOMOtPMFgAAAAD+k5Ig+fUULFhQMmXKJIcOHXJZr9c9TRrqi21GRESYJaGwrFnNcl3JaZPcts4Hmtc7/gm0ttdrn5K2aelHwrY2m9jCwiTMZrsS6Au01y2Y2yb1eU4YJLqeJDLyvdLW03MM5Nc3CNpqnMbsW8nZbqB+RwRiWyvYrX8TlE1L8j4paZvc/TMQ2qbHd4S326ZlbBAfL7aIiCvjEOcfl1K6XTdjm4BuGx4eXG2Tuc8leg+9FURfuXJlSu8CAAAAwIe++OILue+++0z9c72clBYtWiR7u+Hh4ebs0xUrVkjLqzVHNftKr3ft2jVVffXFNgEAAABfSnEQ3dl///1n/t5g1SgDAAAAkO40GK0TiUZGRjoC0+6ktCa66tWrl3To0EFq1KghtWrVksmTJ5tyQp06dTK3t2/f3pRn0brl1sShf/zxh+Pyvn37ZMuWLab8UJmr9XSvt00AAAAgqIPomiUyfPhwmTBhgpy5Oguy1uB86aWXpH///slOgQcAAADgHTpGd3fZG1q3bm3qfg4aNMgE6qtWrSpLly51TAy6d+9el2MArXlerVo1x/Xx48ebpWHDhrJq1apkbRMAAAAI6iC6BspnzZolo0ePltuvTvzx008/yZAhQ8zMtCNGjPBFPwEAAAD4iZZZ8VRqxQqMO08mak9qgqpkbBMAAAAI6iD6u+++K++8845LLcXKlSubUziff/55gugAAACAn2lplO+//95kiWtJFWfdunXzW78AAACADBFEj4mJkXLlyiVar+v0NgAAAAD+s3nzZmnWrJmcO3fOBNPz588vR48elezZs5ua6QTRAQAAgJRJcQHzKlWqyNSpUxOt13V6GwAAAAD/6dmzpzzwwANy/PhxyZYtm6xdu1b27Nkj1atXN7XJAQAAAPg4E33s2LHSvHlz+fbbb6Vu3bpm3Zo1a+Tff/+VJUuWpHRzAAAAALxoy5Yt8vbbb5vJPjNlyiQXLlyQ0qVLm3F8hw4d5OGHH/Z3FwEAAIDQzkRv2LCh7Ny5Ux566CE5ceKEWXQgruvuuOMO3/QSAAAAQLJkyZLFBNCVlm/RuugqT548JvEFAAAAgI8z0ZVOIsoEogAAAEDgqVatmvzyyy9StmxZkwAzaNAgUxP9vffek4oVK/q7ewAAAEDoB9HnzJkjOXPmlFatWrmsX7hwoZm8SE8RBQAAAOAfI0eOlNOnT5vLmvjSvn17ee6550xQffbs2f7uHgAASE9La/i7B6HHbhOJLyMSFi1is/u7N6Gl6QYJmXIuo0aNkoIFCyZar6eK6oAdAAAAgH/Y7XYzLrfmLtLLS5culVOnTsnGjRulSpUq/u4iAAAAEHRSHETXmoqlSpVKtL5EiRKOeosAAAAA/BNEL1OmDLXPAQAAAH+Wc9Fslq1bt0rJkiVd1v/6669SoEABb/YNAPzmwsR3/d2FkHHhwoVrl6d+KOEREX7tT6iJ6EUZNQDX6ISiWrbl2LFj5i8AAAAAP2Sit23bVrp16yYrV66UuLg4s3z33XfSvXt3adOmjRe6BAAAACC1Ro8eLb1795Zt27b5uysAAABAxsxEHzZsmOzevVvuvvtuyZz5yt3j4+PNhEXURAcAAAD8Y968efLYY4+Zcfm5c+dM/fPw8HDJli2bS7uYmBi/9REAAADIEEF0HYgvWLBAhg8fLlu2bDGD8kqVKpma6AAAAAD8o1OnTtK0aVOZPHmyv7sCAAAAZOwgukVrLOpy+fJliY2N9W6vAAAAAKR4UlHVoQNzJQAAAAB+CaJ/+eWXZoKijh07OtaNGDHClHfRQPpdd91lMtTz5cvn1Q4CAIDQwIS93sWkvb4VrJP22mw2f3cBAAAAyLhB9IkTJ8qjjz7quL569WoZNGiQvPbaa1K+fHnp37+/CahrOwAAAADpz3neIk82bdqUbv0BAAAAMlQQ/ffff3cJkC9atEjuueceEzxXWbNmle7duxNEBwAAAPykSZMmkjNnTn93AwAAAMiYQfTTp09LgQIFHNd/+uknadWqleN6hQoVZP/+/d7vIQAAAIBk6d27t0RGRvq7GwAAAEBICUtuw2LFisn27dvN5TNnzsivv/4q9erVc9yu9dKzZ8/um14CAAAASBL10AEAAAA/B9E167xHjx7y3nvvSZcuXaRIkSJSp04dx+0bNmyQW265xUfdBAAAAJAUu93u7y4AAAAAGbuci04ium/fPunWrZsJoL///vuSKVMmx+0ffvihPPDAA77qJwAAAIAk7Nq1SwoWLOjvbgAAAAAZN4ieLVs2mTdvnsfbV65c6a0+AQAAAEiB0aNHS/fu3SUs7Ponmq5bt06OHj0qzZs3T5e+AQAAABkmiI7AdGHiu/7uQki5cOHCtctTP5TwiAi/9ifURPTq4O8uAAAQkv744w+58cYbTQlGPTu0Ro0aUqhQIXPb5cuXze0//fSTOZt0//79SSbHAAAAAHBFEB0AAAAIchoU//XXX2Xq1Kny+OOPy6lTp0zpxYiICDl37pxpU61aNXnqqaekY8eOkjVrVn93GQAAAAi9iUUDxbRp06RkyZJm4F+7dm1Zv359ku0XLlwo5cqVM+0rVaokS5YsSTQBk9Z7j4qKMiVrGjduLH/99ZePnwUAAADgXVWqVJGZM2fKsWPHZOPGjWYcrNeXLVsmhw4dkg0bNsizzz5LAB0AAAAI5SD6ggULpFevXjJ48GDZtGmTOVBo0qSJHD582G371atXS9u2baVz586yefNmadmypVm2bdvmaDN27Fh5/fXXZfr06aY+ZI4cOT2Z6jgAAGjQSURBVMw2Y2Nj0/GZAQAAAN6hddGrVq0qDz74oLRp08YkiTDhKAAAAJBBgugTJ06ULl26SKdOneTWW281ge/s2bPL7Nmz3bafMmWKNG3aVHr37i3ly5eXYcOGyW233WZOc7Wy0CdPniwDBgwwBxmVK1c2p8JqncjFixen87MDAAAAAAAAAARtTfR3333XBK01a7tmzZrSrFmzRKVRfOnixYvmtNR+/fq5ZNloZs2aNWvc3kfXa+a6M80ytwLku3btkoMHD5ptWPLkyWPKxOh9NXPHHZ180nkCSq05qeLj482SntL30UJffILLvL7eld77R1oET08DH/uVb7FfZVzsW6G3bwXT/gwAAABkJMkOomvZkxkzZpggtgbSjx8/Lunp6NGjEhcXJ4ULF3ZZr9d37Njh9j4aIHfXXtdbt1vrPLVxZ9SoUTJ06NBE648cOZL+ZWAeb5K+jxfizMRbr165fKrVXRKfM6e/uxRaPJReCkjsW17DfuVj7FcZFvtW6O1bp0+flkCel2jcuHFmjKwlFd944w2pVauWx/Zaj33gwIGye/duKVu2rIwZM8Yk4VjOnDkjffv2NcktWsO9VKlS0q1bN1OzHQAAAAjaILoGlm+//XaZP3++PP7443L27FnJqPSHBOcMd81EL168uBQqVEhy587t174hbZw/1/p+5sqVy6/9AUIB+xXgG+xboSdQJ/y05iXSs1L1jE0th6hnd+7cuVMiIyM9zkukiSf333+/OX7QeYl0TqOKFSuaNrq97777Tt5//30pWbKkfPPNN/L8889L0aJFpUWLFn54lgAAAIAXgugRERHmFFMdKA8fPlwaNGgg6UknQ8qUKZMcOnTIZb1eL1KkiNv76Pqk2lt/dV1UVJRLG52MKanXQpeEtLyMLghezu8f7yfgHexXgG+wb4Ueb7+H0dHR8vfff5txe7Zs2cx8QDabLU3zEikNpn/99ddmXiLNJk9qXiKl8xItX77czEuk97UC7R06dJA777zTXH/66afl7bfflvXr1xNEBwAAQPAG0RctWuQYdNepU0f27dsn6Sk8PFyqV68uK1asMJksSoP6er1r165u71O3bl1ze48ePRzrdACv65WeNqqBdG1jBc01q3zdunXy3HPPpcvzAgAAALxJy6O0bt3aZHrr+P2vv/6S0qVLS+fOnSVfvnwyYcIEv85LpOrVqydffPGFPPnkkyb7fNWqVfLnn3/KpEmTAn5OIknFDxFIWrzNJnabzfyFlwXTXAu8/17HvuVDwbRv2Xn/vS3ebhO73Wb+wssCeF6iZAfRc+TI4XJdTxt2duDAARkxYoTJMPEVHYxrxkqNGjVMDUY9lVRPZbayYtq3by/FihUzp46q7t27S8OGDc2BQvPmzeWjjz6SDRs2mNruSg8qNMCumfVaq1GD6lq7UQfyVqAeAAAACCY9e/aUzJkzy969e6V8+fKO9RpY1/F0SoLovpiXSGlNdc0+v+GGG0xfNTA/c+ZMj2e7BtScRGXKpO/jZQAa4DsZFSV2/ZHGrv8iQ86dwr7ldexbPhRM+1Y8+5a3xYtNTtqjRHcr9q2MMy9RsoPo6vfff5eVK1earPDHHntM8ubNawbWGjzXUzM1w8WXdOCvA+VBgwaZQbhmjy9dutQxSNcDBefTYDXDRWswDhgwQF599VUTKNcMGKsWo+rTp48JxOsg/sSJE1K/fn2zzUCtSQkAAAAkReuLL1u2zASonelYeM+ePRIINIi+du1ak41eokQJ+eGHH+SFF14wySya5R7QcxJFR6fv42WQQJ/m8hWKjiYY4W1u5i0IWOxbXse+5UPBtG+FsW95m2ag6wkehWzREmZj3wr2fSu5MeBkB9F1gPvoo4/K5cuXzfWxY8eabBENpmuZlc8++8zUPvQ1Ld3iqXyLngaaUKtWrcziiWajv/baa2YBAAAAgp0miGTPnj3R+piYGLfz+qT3vETnz583CS56/KBni6rKlSvLli1bZPz48W6D6AE1JxGBKJ+w2e0myEegz8uCab4M3nufYN/ykWDatwjy+oTNZjcBdILowb9vJXcsmeyeackTzQ7RrA+dXOiff/6Rbt26yZIlS0zmdnoE0AEAAAAk7Y477pB58+a5JI1orUdNgmnUqFGq5yWyWPMSWfMMeZqXyJnzvESXLl0yS8IDFg3Wp3t9cwAAAMCbmeg7d+40pVFy5swpL774orz88stm4p+aNWsmdxMAAAAAfEyD5XfffbeZC0gnBtXyhVqWUTPRf/75Z7/PS6TlV/T23r17S7Zs2Uw5l++//94E/jVZBwAAAAjaILoWWbfqDWqWiA54fV0DHQAAAEDK6Pw/f/75p0ydOlVy5colZ86ckYcffticVRoVFRUQ8xJpYF3rnLdr184E9zWQrvMsPfvss156FQAAAADvSdHEojpBUZ48eVxO49y2bZtLmxYtWnixewAAAACSS8ukaJnF6dOnS//+/QN2XiKtjz5nzhyv9Q8AAAAImCC6nsbp7JlnnnG5rvUW4+LivNMzAAAAACmSJUsW2bp1q7+7AQAAAISUZE8sqpnn11sIoAMAAAD+9cQTT8isWbP83Q0AAAAgY2aiAwAAAAhsly9fltmzZ8u3334r1atXlxw5crjczuSdAAAAQMoQRAcAAABCiM5ZdNttt5nLOsFowvKLAAAAAFKGIDoAAAAQQlauXOnvLgAAAAAZsyY6AAAAgODy33//mQUAAABAOgTRmTQUAAAACHzx8fHy2muvSZ48eaREiRJmyZs3rwwbNszcBgAAAMBH5VyKFSsmHTt2lCeffFJuvvnmFD4MAAAAgPTQv39/mTVrlowePVpuv/12s+6nn36SIUOGSGxsrIwYMcLfXQQAAABCMxP9hRdekEWLFkn58uXljjvukLlz58q5c+d82zsAAAAAKfLuu+/KO++8I88995xUrlzZLM8//7zMnDnTjOEBAAAA+CiIPnDgQImOjpYVK1ZI6dKlpWvXrhIVFSVdunSRdevWpfBhAQAAAPhCTEyMlCtXLtF6Xae3AQAAAPDxxKJ33nmnyW45ePCgTJgwQbZv3y5169aVChUqyMSJE1O6OQAAAABeVKVKFZk6dWqi9bpObwMAAADgo5roCeXMmVOeeuops3z99dfSvn176d27t/Tq1Su1mwQAAACQRmPHjpXmzZvLt99+a5Jd1Jo1a+Tff/+VJUuW+Lt7AAAAQOhnolu0HrrWVGzYsKG0aNFCChQowCRFAAAAgJ/p+Hznzp3y0EMPyYkTJ8zy8MMPm3U6txEAAAAAH2eir169WmbPni0LFy6Uy5cvy6OPPirDhg2TBg0apHRTAAAAAHygWLFiJLgAAAAA6R1E19NC58yZI3/++afUqFFDxo0bJ23btpVcuXJ5qy8AAAAA0kjH7Fp6sVWrVi7rNQlGzybt0KGD3/oGAAAAhHQ5Fw2aN23aVH799VdZt26dPP300wTQAQAAgAAzatQoKViwYKL1kZGRMnLkSL/0CQAAAMgQmej79++XLFmy+LY3AAAAANJk7969UqpUqUTrS5QoYW4DAAAA4KNM9B9//FFuvfVWOXXqVKLbTp48KRUqVDBtAAAAAPiPZpxv3bo10Xo9o7RAgQJ+6RMAAACQIYLokydPli5dukju3LkT3ZYnTx555plnZOLEid7uHwAAAIAU0HmLunXrJitXrpS4uDizfPfdd9K9e3dp06aNv7sHAAAAhG4QXTNXtCa6J/fee69s3LjRW/0CAAAAkArDhg2T2rVry9133y3ZsmUzi47V77rrLmqiAwAAAL6siX7o0KEka6JnzpxZjhw5kpo+AAAAAPCS8PBwWbBggQwfPly2bNliguiVKlUyNdEBAAAA+DCIXqxYMdm2bZuUKVPG7e1adzEqKioVXQAAAADgbWXLljXL5cuXJTY21t/dAQAAAEK/nEuzZs1k4MCBbgfg58+fl8GDB8v999/v7f4BAAAASIYvv/xS5s6d67JuxIgRkjNnTsmbN68p6XL8+HG/9Q8AAAAI+SD6gAEDJCYmRm6++WYZO3asfP7552YZM2aM3HLLLea2/v37+7a3AAAAANyaOHGinD171nF99erVMmjQIJMI8/HHH8u///5r6qUDAAAA8FE5l8KFC5uB+HPPPSf9+vUTu91u1ttsNmnSpIlMmzbNtAEAAACQ/n7//XcTSLcsWrRI7rnnHkeiS9asWaV79+4ubQAAAAB4MYiudDKiJUuWmNNAo6OjTSBd6yzmy5cvJZsBAAAA4GWnT5+WAgUKOK7/9NNP0qpVK8f1ChUqyP79+/3UOwAAACADlHNxpkHzmjVrSq1atQigAwAAAAGgWLFisn37dnP5zJkz8uuvv0q9evUctx87dkyyZ8/uxx4CAAAAGSiIDgAAACCwaNZ5jx495L333pMuXbpIkSJFpE6dOo7bN2zYYOYyAgAAAJAyBNEBAACAEKCTiOrZot26dZMtW7bI+++/L5kyZXLc/uGHH8oDDzyQqm3r/EclS5Y0ddVr164t69evT7L9woULpVy5cqZ9pUqVTEnIhDRrvkWLFpInTx7JkSOH6fvevXtT1T8AAAAgYGqiAwAAAAhM2bJlk3nz5nm8feXKlana7oIFC6RXr14yffp0E0CfPHmyNGnSRHbu3CmRkZGJ2q9evVratm0ro0aNkvvvv1/mz58vLVu2lE2bNknFihVNm7///lvq168vnTt3lqFDh0ru3LnNxKgadAcAAAACDUF0AAAAAB5NnDjRlIfp1KmTua7B9K+//lpmz54tffv2TdR+ypQp0rRpU+ndu7e5PmzYMFm+fLlMnTrV3Ff1799fmjVrJmPHjnXc76abbvLYhwsXLpjFcurUKfM3Pj7eLOnKZkvfx8sA4m02sdts5i+8LL33j7Tg/fc69i0fCqZ9y877723xdpvY7TbzF8G/byV3LEkQHQAAAIBbFy9elI0bN0q/fv0c68LCwqRx48ayZs0at/fR9Zq57kwz1xcvXuw4UNEgfJ8+fcz6zZs3S6lSpcxjaMa6O5rVrhnrCR05ckRiY2MlXZUpk76PlwFogO9kVJTY9fNl13/hNYcPS9Bg3/I69i0fCqZ9K559y9vixSYn7VGiuxX7VvDvW6dPn05WO4LoAAAAANw6evSoxMXFSeHChV3W6/UdO3a4vc/Bgwfdttf16vDhw3LmzBkZPXq0DB8+XMaMGSNLly6Vhx9+2JScadiwYaJtaoDdOTCvmejFixeXQoUKmVIw6So6On0fL4ME+jSXr1B0NMEIb3NTcilgsW95HfuWDwXTvhXGvuVtmoGuJ3gUskVLmI19K9j3reSWEySIDgAAACDdT5l98MEHpWfPnuZy1apVTS11LffiLogeERFhloQ0K16XdEUgyidsdrsJ8hHo87L03j/SgvfeJ9i3fCSY9i2CvD5hs9lNAJ0gevDvW8kdSwbRXg8AAAAgPRUsWFAyZcokhw4dclmv14sUKeL2Pro+qfa6zcyZM8utt97q0qZ8+fKyd+9erz8HAAAAIK3IRAcAAABCyOuvv+52vc1mM6erlilTRho0aGCC49cTHh4u1atXlxUrVjjqlWsmuV7v2rWr2/vUrVvX3N6jRw/HOp1YVNdb26xZs6bs3LnT5X5//vmnlChRIkXPFQAAAEgPBNEBAACAEDJp0iQz4ea5c+ckX758Zt3x48cle/bskjNnTlOTvHTp0qb+uNYVvx6tRd6hQwepUaOG1KpVSyZPnixnz56VTp06mdvbt28vxYoVM5N/qu7du5uSLBMmTJDmzZvLRx99JBs2bJAZM2Y4ttm7d29p3bq1CeY3atTI1ET/8ssvZdWqVT57XQAAAIDUopwLAAAAEEJGjhxpMr3/+usvOXbsmFk0y7t27doyZcoUUzJFS6tY9civR4Pd48ePl0GDBpna5Vu2bDFBb2vyUN3egQMHHO3r1asn8+fPN0HzKlWqyKJFi2Tx4sVSsWJFR5uHHnrI1D8fO3asVKpUSd555x355JNPpH79+j54RQAAAIC0sdntzC6RVqdOnZI8efLIyZMnJXfu3P7uDtJAs6o0Q8t6X3PlyuXvLgFBj/0K8A32rdDjrTHlTTfdZALSGvB2tnnzZnnkkUfkn3/+MZN46mXn4Hcw8ev4u0aN9H28DCDeZpPDZcpIZHQ0kx9624YNEjTYt7yOfcuHgmnfWsq+5W3xdpscji8jkWHRTCzqbU03BOy4kkx0AAAAIIRoYPzy5cuJ1uu6gwcPmstFixaV06dP+6F3AAAAQPAhiA4AAACEEK0x/swzz5jMc4tefu655+Suu+4y13/77TcpVaqUH3sJAAAABA+C6AAAAEAImTVrluTPn1+qV68uERERZtFJQXWd3qa0FJBO/AkAAADg+jInow0AAACAIKGThi5fvlx27NhhJhRVt9xyi1mcs9UBAAAAJA9BdAAAACAElStXziwAAAAA0oYgOgAAABBC4uLiZO7cubJixQo5fPiwxMfHu9z+3Xff+a1vAAAAQDAiiA4AAACEkO7du5sgevPmzaVixYpis9n83SUAAAAgqBFEBwAAAELIRx99JB9//LE0a9bM310BAAAAQkKYvzsAAAAAwHvCw8OlTJky/u4GAAAAEDIIogMAAAAh5KWXXpIpU6aI3W73d1cAAACAkEA5FwAAACCE/PTTT7Jy5Ur53//+JxUqVJAsWbK43P7pp59KyIi7eGVJyBYmEuZ0qOOujaOtTSQsS/LbOgvTHyo8/VhhE4m3+aCtiMSHpa6tzX5l8Upb7a/NO231dbXFiYTFi8SZFenfh/Ruq++Zee88sNuuLGltm/DzbMskEpbpatN4kfjLnrfr0tYuEn/JS22d9k/ntvr+p+S5JdnWw/ZS0zbR/pmStgHwHWGLv9JnTz+sBsN3REC2tS7HidjNF5d7+lnXz3xK26Zo/7xOW33vrf+/zOcgqR/ZbRmjrWkfn/q25jsk7tr6VG/3Ovu9T9pe/R72d1vx8N65G4fpOM1qq5/1pF5jl7bX2+ec2iYDQXQAAAAghOTNm1ceeughyRC2TxDJGZF4fa6yIqXaObUb5zmol7OkSOmO167vnCxy+Zz7ttmLul6vclgkq4eDs3OZRX6NvHa90hGR7B6CHLGZRDYXvna9wlGRnB76eylMZEORa9fLHRPJ4yHwH2cTWR917fotMSL5LohHa5yeX9njIgViPbddV+RaIKn0SZFID6+Z+qWwyOWrwZ6Sp0SKnHW5WbeSM/8FsRWIEdkUKXLh6mHqjadFip7xvN0thUTOX/0BpNgZkeKnPbf9raDImfArl6POipQ45bnt7wVETl39XBU+J1LqpOe22/OLnMh65XLB8yJlTnhu+2c+kWPZrlzW1/bm457bRucVOZL9yuW8F0TKx3huuyuPyMEcVy7nvihS4ZjTcxnp2jbqHpFCt1+5fP6ASPRMz9stfOeVRV04IvLnm57bFqonEnXvlcuXTorsmOy5bYGaIsWaX7kcd07kj3FXLtc+mLjt4ewif+e9Fjh218ZyLKvIn/mvXU+q7fEIkR0Frl2vcUgkk4dgz8lwkT8KXrt+22GRLB4COGeyiPxWKKC+I7IV2y22AgeC+jvCRaB8R1hiNorsX+K5bcnHRXLffOXyyd9E/l3sue2NrUTyVrjadrvI3oWe2xZvKZKv6pXLp6NFds/33Fb3s8xXvyPiL4pccPqOSChLbpEsOa9ctl8SiT2aRNtcVxbT9rJI7JEk2ua8sm3TNk4k9rDnttrX8DxXr8SLnD+URNvsIuFXvyM0CHs+if0+U1aRCKfviCTbRohEOL3PsYdcfogy/2/ZL4jNFiOSKVwkwuk7Qp+bpyCvBm2zOn1HXDh8JdDrtm1mkaxO3xH6PezpxxL9QSWr03fEhaOexz0a6M7mNI64eMxzAoEGmLM5fUdcjBGJS+I7wnmcdPG4SFwS3xGmD7Zr/29YY6+E/2+pW3tf+wwfWCZy7BfP2y3X49pn4tAKkSOrPbe9+XnX1/g6CKIDAAAAIWTOnDn+7gIAAAAQUmx2iiWm2alTpyRPnjxy8uRJyZ376i9rCEpnz56VnDlzOt7XXLmu/qoKINXYrwDfYN8KPYwpU/FaxRxx/1r5spxLrbqBVaohBMq5xNtscvimmyTy778ljHIu3i3nsmZt8JRzqVsnZc8tybaUc3HsW2VLS+Tf0RJGORfvtt2wMXjKuXxTN7BKqQRC2zSWc4m32+Rw/E0SGfa3hJmPDuVcvFbO5d4E/2+lQzmX5I7ByUQHAAAAgtxtt90mK1askHz58km1atXElkR9x02bNknI0FOodUlOu5RsM7lcgqJB0DZh4DBQ2urn1Z7paoDOHvj99UbbhEFRX7VN6vOswY7kft71PfJ1W+cAbZpfh+RsLz3aBsJ+H3alz8nJnwyEfSPY2ppAdibvt03R/nmdts5jAnM5mc8tlNua9mFpaKsB4ExX1iea5yMl2w2A1yLQ2ma6zufeOUnCm/tcMhBEBwAAAILcgw8+KBEREY7LSQXRAQAAAKQMQXQAAAAgyA0ePNhxeciQIX7tCwAAABBqUnCOAQAAAIBAV7p0aTl27Fii9SdOnDC3AQAAAAjRIHpMTIy0a9fOFHjPmzevdO7cWc6cOZPkfWJjY+WFF16QAgUKmIm3HnnkETl06JBLGz3VNeHy0Ucf+fjZAAAAAL6xe/duiYtLPInShQsX5L///vNLnwAAAIBgFjTlXDSAfuDAAVm+fLlcunRJOnXqJE8//bTMnz/f43169uwpX3/9tSxcuNDMstq1a1d5+OGH5eeff3ZpN2fOHGnatKnjugbpAQAAgGDyxRdfOC4vW7bMjH8tGlTXiUdLlSrlp94BAAAAwSsogujbt2+XpUuXyi+//CI1atQw69544w1p1qyZjB8/XooWLZroPidPnpRZs2aZIPtdd93lCJaXL19e1q5dK3Xq1HEJmhcpUiQdnxEAAADgXS1btnRc7tChg8ttWbJkkZIlS8qECRP80DMAAAAguAVFEH3NmjUm0G0F0FXjxo0lLCxM1q1bJw899FCi+2zcuNFkrGs7S7ly5eTGG28023MOomvJl6eeesrUiHz22WdNlruWdfFET4XVxXLq1CnzNz4+3iwIXs7vH+8n4B3sV4BvsG+FnrS+h9b9Ndtck08KFizopZ4BAAAAGVtQBNEPHjwokZGRLusyZ84s+fPnN7d5uk94eHii0iyFCxd2uc9rr71mMtWzZ88u33zzjTz//POm1nq3bt089mfUqFEydOjQROuPHDli6rAjeJ07d87l/Tx//rxf+wOEAvYrwDfYt0LP6dOnvbIdHafmypUr0fqLFy+auX/at2/vlccBAAAAMgq/BtH79u0rY8aMuW4pF18aOHCg43K1atXk7NmzMm7cuCSD6P369ZNevXq5ZKIXL15cChUqZCY+RfDS99+i76e7A1AAKcN+BfgG+1boyZo1q1e2o2dV6nw/CZNQNEivtxFEBwAAAIIoiP7SSy9Jx44dk2yjJVa0Xvnhw4dd1l++fFliYmI81jLX9Zptc+LECZds9EOHDiVZ/7x27doybNgwU64lIiLCbRtd7+42LS+jC4KX8/vH+wl4B/sV4BvsW6HHW++h3W53W5rwv//+c5lsFAAAAEAQBNE1a0qX66lbt64Jhmud8+rVq5t13333nan7qEFvd7SdTqC0YsUKeeSRR8y6nTt3yt69e832PNmyZYvky5fPYwAdAAAACER6VqUGz3W5++67TflDS1xcnOzatctkqAMAAAAIwZro5cuXNwP+Ll26yPTp082EoV27dpU2bdpI0aJFTZt9+/aZg4V58+ZJrVq1TJZN586dTdkVrZ2uZVZefPFFE0C3JhX98ssvTWa6XtfTZ5cvXy4jR46Ul19+2c/PGAAAAEiZli1bOpJCmjRpIjlz5nTcpnMFlSxZ0pFcAgAAACDEgujqgw8+MIFzDZTrqa56APD66687btfAumaaO0+yNWnSJEdbLc+iBxNvvvmm43bNVJ82bZr07NnTnPZapkwZmThxognWAwAAAMFk8ODB5q8Gy1u3bu21GusAAABARhc0xTM1m3z+/PlmQqSTJ0/K7NmzXbJr9GBBA+F33nmnY50eOGiQXGun6+Rbn376qUs9dM1u37x5s9nmmTNnTNbOM888Q01RAAAABK0OHTp4PYCuY2odb+t2tZzi+vXrk2y/cOFCKVeunGlfqVIlWbJkice2zz77rClBM3nyZK/2GQAAAPAWosUAAABAkNOEk6NHj5rLOr+PXve0pNSCBQtMiUTNdN+0aZNUqVLFnOF5+PBht+1Xr14tbdu2NaUVNWFFy8zosm3btkRtP/vsM1m7dq2jRCMAAAAQiIKmnAsAAAAA97SMYa5cucxlb2d0W+UOO3XqZK7rHEVff/21OTO0b9++idpPmTLFnPHZu3dvc33YsGFm7qGpU6ea+1p0TiOds2jZsmXSvHnzJPugpRl1sZw6dcr8jY+PN0u6stnS9/EygHibTew2m/kLL0vv/SMteP+9jn3Lh4Jp37Lz/ntbvN0mdrvN/EXw71vJHUsSRAcAAACC3K+//iqPPvqoRERESKlSpaRevXqSOXPah/oXL16UjRs3Sr9+/RzrtPRh48aNZc2aNW7vo+s1c92ZZq4vXrzY5WDl//7v/0ygvUKFCtftx6hRo2To0KGJ1h85ckRiY2MlXZUpk76PlwFogO9kVJTY9fNl13/hNR7OGAlI7Ftex77lQ8G0b8Wzb3lbvNjkpD1KdLdi3wr+fUvLfCcHQXQAAAAgyL3xxhvyyiuvSI4cOaRRo0Zy4MABiYyMTPN2tURMXFycFC5c2GW9Xt+xY4fb+xw8eNBte11vGTNmjAnyd+vWLVn90CC+c2BeM9GLFy8uhQoVkty5c0u6io5O38fLIIE+zeUrFB1NMMLbvPA9kG7Yt7yOfcuHgmnfCmPf8jbNQNcTPArZoiXMxr4V7PtWcucSIogOAAAABDmd9PP111+Xe++9V+x2u8kG19ro7jRo0ED8STPbteSL1lfXCUWTQzPsdUlIs+J1SVcEonzCZrebIB+BPi9L7/0jLXjvfYJ9y0eCad8iyOsTNpvdBNAJogf/vpXcsSRBdAAAACDIjRs3Tp599llT9kQD0w899JDbdnqbZpYnV8GCBSVTpkxy6NAhl/V6vUiRIm7vo+uTav/jjz+aSUlvvPFGx+3ap5deesnUc9+9e3ey+wcAAACkhyD66QwAAACAOy1btjTlUrTMiWai//nnn3L8+PFES0xMTIq2Gx4eLtWrV5cVK1a41DPX63Xr1nV7H13v3F7pxKJWe62FvnXrVtmyZYtjKVq0qKmPrpOMAgAAAIGGTHQAAAAgRGhNxzlz5pjSJ3ny5PHKNrUWeYcOHaRGjRpSq1Ytky1+9uxZ6dSpk7m9ffv2UqxYMZMFr7p37y4NGzaUCRMmSPPmzeWjjz6SDRs2yIwZM8ztBQoUMIuzLFmymEz1W265xSt9BgAAALyJIDoAAAAQInSyzueee062b9/utW22bt1ajhw5IoMGDTLZ7lWrVpWlS5c6Jg/du3evSy3JevXqyfz582XAgAHy6quvStmyZWXx4sVSsWJFr/UJAAAASE8E0QEAAIAQotniWiKlRIkSXttm165dzeLOqlWrEq1r1aqVWZKLOugAAAAIZATRAQAAgBDy/PPPmxIs//77r6lnniNHDpfbK1eu7Le+AQAAAMGIIDoAAAAQQtq0aWP+duvWzbHOZrOZCUf1b1xcnB97BwAAAAQfgugAAAAB7sCBA2Zxdv78ecdlLd2RMNtYRUVFmQUZy65du/zdBQAAACCkEEQHAAAIcG+//bYMHTrU4+0NGjRwu37w4MEyZMgQH/YMgcibtdABAAAAEEQHAAAIeM8884y0aNEi0fr4+HiJiYmR/PnzS1hYWKLbyULPuN577z2ZPn26yUpfs2aNCaxPnjxZSpUqJQ8++KC/uwcAAAAEFYLoAAAAAc5TWRYNoh8+fFgiIyPdBtGRMb311lsyaNAg6dGjh4wYMcJRAz1v3rwmkE4QHQAAAEgZjrYAAACAEPLGG2/IzJkzpX///pIpUybH+ho1ashvv/3m174BAAAAwYggOgAAABBCtIRLtWrVEq2PiIiQs2fP+qVPAAAAQDAjiA4AAACEEK17vmXLlkTrly5dKuXLl/dLnwAAAIBgRk10AAAAIIT06tVLXnjhBYmNjRW73S7r16+XDz/8UEaNGiXvvPOOv7sHAAAABB2C6AAAAEAIeeqppyRbtmwyYMAAOXfunDz++ONStGhRmTJlirRp08bf3QMAAACCDkF0AAAAIMS0a9fOLBpEP3PmjERGRvq7SwAAAEDQoiY6AAAAEEKGDx9uJhdV2bNnJ4AOAAAApBFBdAAAACCELFy4UMqUKSP16tWTN998U44ePervLgEAAABBjSA6AAAAEEJ+/fVX2bp1q9x5550yfvx4Uw+9efPmMn/+fFPeBQAAAEDKEEQHAAAAQkyFChVk5MiR8s8//8jKlSulZMmS0qNHDylSpIi/uwYAAAAEHYLoAAAAQAjLkSOHZMuWTcLDw+XSpUv+7g4AAAAQdAiiAwAAACFGJxYdMWKEyUivUaOGbN68WYYOHSoHDx70d9cAAACAoJPZ3x0AAAAA4D116tSRX375RSpXriydOnWStm3bSrFixfzdLQAAACBoEUQHAHjNgQMHzOLs/PnzjstbtmwxZQUSioqKMgsAIO3uvvtumT17ttx6663+7goAAAAQEgiiAwC85u233zblAjxp0KCB2/WDBw+WIUOG+LBnAJBxaBkXdfToUfO3YMGCfu4RAAAAENwIogMAvOaZZ56RFi1aJFofHx8vMTExkj9/fgkLSzwdB1noAOAdJ06ckP79+8uCBQvk+PHjZl2+fPmkTZs2Mnz4cMmbN6+/uwgAAAAEHYLoAACv8VSWRYPohw8flsjISLdBdABA2umPlXXr1pV9+/ZJu3btpHz58mb9H3/8IXPnzpUVK1bI6tWrTVAdAAAAQPIRRAcAAABCwGuvvSbh4eHy999/S+HChRPddu+995q/kyZN8lsfAQAAgGBEOiAAAAAQAhYvXizjx49PFEBXRYoUkbFjx8pnn33ml74BAAAAwYwgOgAAABACDhw4IBUqVPB4e8WKFeXgwYPp2icAAAAgFBBEBwAAAEJAwYIFZffu3R5v37Vrl5ngGQAAAEDKEEQHAAAAQkCTJk2kf//+cvHixUS3XbhwQQYOHChNmzZN1banTZsmJUuWlKxZs0rt2rVl/fr1SbZfuHChlCtXzrSvVKmSLFmyxHHbpUuX5JVXXjHrc+TIIUWLFpX27dvL/v37U9U3AAAAwNcIogMAAAAhQCcN3blzp5QtW9bUP//iiy/k888/l9GjR5t127dvl6FDh6Z4uwsWLJBevXrJ4MGDZdOmTVKlShUTsD98+LDb9qtXr5a2bdtK586dZfPmzdKyZUuzbNu2zdx+7tw5sx0N6uvfTz/91PS7RYsWaX4NAAAAAF+w2e12u0+2nIGcOnVK8uTJIydPnpTcuXP7uztIg7Nnz0rOnDkd72uuXLn83SUgJMTHx5tgS2RkpISF8fst4C3sW6HFG2NKLdny/PPPyzfffCPWMN9ms8k999wjU6dOlTJlyqR4m5p5XrNmTXN/63NXvHhxefHFF6Vv376J2rdu3dqMqb766ivHujp16kjVqlVl+vTpbh/jl19+kVq1asmePXvkxhtvDOzxd40a6ft4GUC8zSaHy5SRyOhoCePw1Ls2bJCgwb7ldexbPhRM+9ZS9i1vi7fb5HB8GYkMi5YwG/uWVzVN/30ruePKzOnaKwAAAAA+U6pUKfnf//4nx48fl7/++sus08B5amuha2mYjRs3Sr9+/Rzr9Aebxo0by5o1a9zeR9dr5rozzVxfvHixx8fRgxYN9ufNm9ft7VqORhfngx0roK9LurLZ0vfxMkigz26zmb/wsvTeP9KC99/r2Ld8KJj2LTvvvy+C6Ha7zfxF8O9byR1LEkQHAAAAQky+fPlMZndaHT16VOLi4qRw4cIu6/X6jh073N7n4MGDbtvrendiY2NNjXQtAeMp+2fUqFFuS9EcOXLE3D9dpSKbH0nTAN/JqCjRXD6yZb3MQ9mlgMS+5XXsWz4UTPtWPPuWt8WLTU7ao0R3K/at4N+3Tp8+nax2BNEBAAAA+IVOMvrYY4+Z0jNvvfWWx3aaCe+c3a6Z6FpSplChQulfziU6On0fL4ME+jSXrxAlJ7wvMlKCBvuW17Fv+VAw7Vth7FvephnoeoJHIRvlXEJh38qaNWuy2hFEBwAAAOBWwYIFJVOmTHLo0CGX9Xq9SJEibu+j65PT3gqgax307777LslgeEREhFkS0tIy6T4fAIEon7DZ7SbIR6DPy4Jpvgzee59g3/KRYNq3CPL6hM1mNwF0gujBv28ldywZRHs9AAAAgPQUHh4u1atXlxUrVrjUjdTrdevWdXsfXe/cXi1fvtylvRVA17rt3377rRQoUMCHzwIAAABIGzLRAQAAAHikZVQ6dOggNWrUMHXWJ0+eLGfPnpVOnTqZ29u3by/FihUzdctV9+7dpWHDhjJhwgRp3ry5fPTRR7JhwwaZMWOGI4D+6KOPyqZNm+Srr74yNdeteuk6AaoG7gEAAIBAQhAdAAAAgEetW7c2E3gOGjTIBLurVq0qS5cudUweunfvXpfTYOvVqyfz58+XAQMGyKuvviply5aVxYsXS8WKFc3t+/btky+++MJc1m05W7lypdx5553p+vwAAACA6yGIDgAAACBJXbt2NYs7q1atSrSuVatWZnGnZMmSZiJRAAAAIFhQEx0AAAAAAAAAAA8IogMAAAAAAAAA4AFBdAAAAAAAAAAAPCCIDgAAAAAAAACABwTRAQAAAAAAAADwgCA6AAAAAAAAAAAeEEQHAAAAAAAAAMADgugAAAAAAAAAAHhAEB0AAAAAAAAAAA8IogMAAAAAAAAA4AFBdAAAAAAAAAAAPCCIDgAAAAAAAACABwTRAQAAAAAAAAAI9iB6TEyMtGvXTnLnzi158+aVzp07y5kzZ5K8z4wZM+TOO+8097HZbHLixAmvbBcAAAAAAAAAkDEETRBdA92///67LF++XL766iv54Ycf5Omnn07yPufOnZOmTZvKq6++6tXtAgAAAAAAAAAyhswSBLZv3y5Lly6VX375RWrUqGHWvfHGG9KsWTMZP368FC1a1O39evToYf6uWrXKq9sFAAAAAAAAAGQMQRFEX7NmjSm1YgW6VePGjSUsLEzWrVsnDz30ULpu98KFC2axnDp1yvyNj483C4LDgQMHzOLs/PnzjsubNm2SHDlyJLpfVFSUWQAkn3432u12viMBL2PfCi28jwAAAEBgCoog+sGDByUyMtJlXebMmSV//vzmtvTe7qhRo2To0KGJ1h85ckRiY2NT3R+kr0mTJsmECRM83q719N156aWX5OWXX/Zhz4DQDAydPHnSBPv0h0oA3sG+FVpOnz7t7y4AAAAACLQget++fWXMmDFJttGSK4GmX79+0qtXL5dM9OLFi0uhQoXMBKUIDj179pQ2bdq4DUgcP35c8uXL5zYgoVnoCX98AZA03a90gmf9niTQB3gP+1ZoyZo1q7+7AAAAACDQguia0duxY8ck25QuXVqKFCkihw8fdll/+fJliYmJMbelVmq3GxERYZaE9OCVA9jgUaxYMbO4C0jo50ID5byfgPdooI/vScD72LdCB+8hAAAAEJj8GkTXrCldrqdu3bpy4sQJ2bhxo1SvXt2s++6770yws3bt2ql+fF9tFwAAAAAAAAAQGoIi3aV8+fLStGlT6dKli6xfv15+/vln6dq1qynFUbRoUdNm3759Uq5cOXO7Reuab9myRaKjo8313377zVzXTPPkbhcAAAAAAAAAkHEFRRBdffDBByZIfvfdd0uzZs2kfv36MmPGDMftly5dkp07d8q5c+cc66ZPny7VqlUzQXLVoEEDc/2LL75I9nYBAAAAAAAAABmXX8u5pET+/Pll/vz5Hm8vWbKk2O12l3VDhgwxS1q2CwAAAAAAAADIuIImEx0AAAAAAAAAgPRGEB0AAAAAAAAAAA8IogMAAAAAAAAA4AFBdAAAAAAAAAAAPCCIDgAAACBJ06ZNk5IlS0rWrFmldu3asn79+iTbL1y4UMqVK2faV6pUSZYsWeJyu91ul0GDBklUVJRky5ZNGjduLH/99ZePnwUAAACQOgTRAQAAAHi0YMEC6dWrlwwePFg2bdokVapUkSZNmsjhw4fdtl+9erW0bdtWOnfuLJs3b5aWLVuaZdu2bY42Y8eOlddff12mT58u69atkxw5cphtxsbGpuMzAwAAAJKHIDoAAAAAjyZOnChdunSRTp06ya233moC39mzZ5fZs2e7bT9lyhRp2rSp9O7dW8qXLy/Dhg2T2267TaZOnerIQp88ebIMGDBAHnzwQalcubLMmzdP9u/fL4sXL07nZwcAAABcX+ZktMF16IGAOnXqlL+7Ai+Ij4+X06dPm9OPw8L4nQnwBvYrwDfYt0KLNZa0xpaB4OLFi7Jx40bp16+fY51+1rT8ypo1a9zeR9dr5rozzTK3AuS7du2SgwcPmm1Y8uTJY8rE6H3btGmTaJsXLlwwi+XkyZPm74kTJ8x+kK7S+/EygHibTU5dvizh8fESFkCf/5Bw4oQEDfYtr2Pf8qFg2rfOsm95W7zdJqfiL0t4WLyE2di3gn3fSu4YnCC6F+jBqypevLi/uwIAAIAQGFtqUDkQHD16VOLi4qRw4cIu6/X6jh073N5HA+Tu2ut663Zrnac2CY0aNUqGDh2aaH2JEiVS+IwQsDZt8ncPQlO+fP7uAfyNfcs32Lcg7Fu+kS9gx+AE0b2gaNGi8u+//0quXLnEZrP5uzvwwi9Q+oOIvqe5c+f2d3eAkMB+BfgG+1Zo0ewXHbzr2BKuNBPeObtds89jYmKkQIECjL9DAN9lgG+wbwG+wb6VMcfgBNG9QE9pveGGG/zdDXiZfhHyZQh4F/sV4BvsW6EjUDLQLQULFpRMmTLJoUOHXNbr9SJFiri9j65Pqr31V9dFRUW5tKlatarbbUZERJjFWd68eVP5rBCo+C4DfIN9C/AN9q2MNQaneCYAAAAAt8LDw6V69eqyYsUKlyxwvV63bl2399H1zu3V8uXLHe1LlSplAunObTSja926dR63CQAAAPgTmegAAAAAPNIyKh06dJAaNWpIrVq1ZPLkyXL27Fnp1KmTub19+/ZSrFgxU7dcde/eXRo2bCgTJkyQ5s2by0cffSQbNmyQGTNmmNu1/EqPHj1k+PDhUrZsWRNUHzhwoDmFtmXLln59rgAAAIA7BNGBBPRU4cGDByc6ZRhA6rFfAb7BvoX00Lp1azly5IgMGjTITPypJVeWLl3qmBh07969pryhpV69ejJ//nwZMGCAvPrqqyZQvnjxYqlYsaKjTZ8+fUwg/umnn5YTJ05I/fr1zTazZs3ql+cI/+K7DPAN9i3AN9i3MiabXaunAwAAAAAAAACARKiJDgAAAAAAAACABwTRAQAAAAAAAADwgCA6AAAAAAAAAAAeEEQHAAAAAAAAAMADgugAAAAAAAAAAHhAEB0IAna73d9dAAAAADIUxuAAAMBCEB0IcPHx8WKz2czly5cvy6VLl8xlBvVA2vYrAN7H/00AQgVjcMD7GIMDvsH/TemDIDoQ4MLCruymo0ePltatW0uTJk3kxx9/dAzqAaR88G7tV8uXL5cvv/xStm7d6u9uASEVcDp37pycPXvW310CgFRjDA54F2NwwDcYg6cfguhAEPxKP2rUKJk4caIUL15cwsPDpXHjxjJr1ixHRgyA5P9Cbw3eX3rpJenQoYO0b99ennrqKRk8eLC/uwcENWvfGjZsmAk23XvvvfLmm2/6u1sAkCKMwQHvYwwO+A5j8PSTOR0fC0Aqvgj37t0rJ06ckIULF0rDhg3NOh1oPPvss2aQ37FjR8mSJYufewsEx+Dd+oV+06ZNsnbtWpMBkz17dnnvvfdkyZIlcubMGZkwYYK/uwoEbWaZBpumTp0qzz//vOzfv1+6du0qe/bskTFjxvi7mwCQLIzBAe9iDA74BmPw9EcQHQhgX331lbRo0UKKFSsmzZs3d6wfOnSoGYi88MIL5q/+iq/ZMQA8swbvCxYsMAfEVapUkerVq5t1ffr0MQP5xYsXm+wYBvFA8lmD9y1btpj9aM6cOdKsWTNz0NyoUSMTaNLLY8eO9XdXASBZGIMD3sMYHPANxuDpjyA6EMD0C7B79+4yZcoU+eeff6RBgwaOX/KHDBlivjSffvppiYyMNAN9AEk7ffq0fP311/LTTz9JhQoVHOvz5s1rfq1Xmhmjp5a+8847fuwpEFw0q6xevXqSI0cOmT9/vlmn/1e1adPGXNZBvF4nGwZAMGAMDngXY3DANxiDpy9qogMBPFO5DtAnTZokTz75pBlcLF261GUyo0GDBsnMmTPNQB+A51nKrb+5cuWSkSNHmgnCdu7caSYLSziI11O2tT0znAPJV7FiRRNs0v/L9FRtZzqInzdvnowbN06mTZvmtz4CgDuMwQHvYwwOpA/G4OnLZucbCgioWlaffPKJ7N69W7JmzSq1atWSmjVrOn5B1Nv0FLimTZsm2sbly5clc2ZOLgHc7Vf//vuvREREmH0kf/78pk7ciBEjZOPGjfLoo4/Kyy+/7Lif1mTUX/L1YNl5GwCu8LRfXLx40QziX3nlFZk8ebJ069bN5fYVK1aYA2T+rwIQKBiDA97HGBzwDcbg/kcQHQggvXv3NnWsqlatKlu3bpXixYubOoyvvfaauV2zYT777DPTpmXLlv7uLhAUExjpJGBaZ1EH5lq3VAfuDz/8sBw8eNDMYK6/2Ldq1Up69erlcRsAEg/e3333XRNwOnr0qDz++OMmE0YzzfR00X79+pnB/IsvvphoGwScAAQaxuCAdzAGB3yDMXiA0CA6AP+Ii4tzXP7888/tUVFR9rVr15rrR44csQ8YMMBeo0YN+6hRoxztHnnkEXvjxo390l8g2AwfPtxeoEAB+6JFi+wffvih/cUXX7SHhYXZJ02aZG7fu3evvWvXrvbSpUvb58+f7+/uAkHjpZdeMvvWo48+ar/55pvN0qtXL/N/lxo7dqw9S5Ys9hEjRvi7qwCQCGNwwLcYgwO+wRjcv8hEB/ygZ8+eMnDgQHNKm0V/LZw7d66sX79esmTJYtbpr/SaAbNjxw756quvzIzLitPbgMR0fylSpIjjuma9aK1SzXBx/iV+/Pjx0qdPH/n+++/ljjvukD179siiRYukR48ekilTJj/1Hggey5YtMxN/ff7553LbbbeZdZpdpjWDGzVqZP5/00wXzYb59ttv5ccffySjDEBAYAwOeB9jcCB9MAb3P0YAQDr77rvv5MSJE5I7d26X9Tqhin7haZ04pb9v6WCkU6dOsmrVKtm2bZujrQ7e3U2CBGRUd999t0yYMMFl3blz5yQ6Otqc2qZ0/9L9Smsv6sB+xowZcunSJSlRooS89NJLZvAeFxfnp2cABCY9xXrz5s0u644fP25Oyy5WrJhj8q9XX31Vbr/9dlmwYIGpy5gtWzazzhq8k7MB4P/buxNwncv8j+Pfrv9k38uSXZrGEiLZsxWVdSQkW5ZBEZWyhJIlopBoRImS7EVJyBSSXZR9ikmyhizZmpn+1+fb9Ts95zhHmp7H8xzer+s6l+M5zzlzmrp/v8/vvu/v97YoI4MD4UcGByKDDB6bmEQHLrHq1avbhAkTvBfVlClTfOVe1INRfa1efvllO336dNyKoS6CxYoV80NWQrELBviVThsfOHCgf378+HH/M1u2bFapUiUP6ocPH/YxFwR07UBToAh2nAXYBQP8asWKFXb27Fm/ByWkB+IzZ874vUoPwvqzZ8+evqtME1WikB+Ed3bBAIg2MjgQfmRwIPzI4LGLBABcQsHOFV3ItmzZYoMHD7ZmzZp5iC9RooSNHz/ey9x0uJFKR3WwkVbsVUJauHDhaP/6QMyOq0KFClnKlCltyJAh1rhxY9u5c6d/rWXLlv6wq10uJ06ciAvxe/bssaxZs0b7VwdiWvny5W306NFxE05BMNehegrlKtHW+AsehA8dOmQFCxb0h+dQhHcA0UYGB8KPDA5EBhk8dtETHbhEEvZQ1NCbPn26jR071i9+b7zxhpeO6gRzld+o3DRjxox+IVQ/K72HPoxAfAlX11euXGmVK1e2Bg0aeI9TjSntgtHOM4X2cuXK+W4z7TTbuHEjp5MDSQjuNxpjKslu1aqVTyap12KVKlVszZo1VrduXStSpIh16tTJ2yOMHDnSQ7x2z7CjDECsIIMD4UcGByKDDB7bmEQHLoHQ4K2SN10EW7Ro4Re4GTNm+CpjqlSpbNKkSXbdddfZd999573k9KESHn2vynYIG0Di42rt2rWWN29ef+BV7ziVkN51113+gKzdLl9++aXNmjXLw0X27Nn9IVnjiXEFnC+xySLtzNR40pjp06ePj7EdO3b4TrPvv//ex5HG4Lx583zCSbvNCPEAoo0MDoQfGRyIDDJ47GMSHbiEevTo4btd1LOqadOmHjY0BBXiR40a5T0X9XUFjFDsfgEsyTGhMLFkyRLr0KGD3XPPPf6AHBriNbZ0+EpCBAzgwmNr+PDh3t/0qaee8tfmz5/v40nv6d27t+84C0qzdS9TgGfCCUAsIoMD4UEGByKDDJ48MIkOXCI6rKhfv35eFlq8eHF/Tacn69AHmTt3rvdi1M4XvSdTpkxR/o2B2KcQoVLRt99+22655RbLnDlzXHmpdsZUrVrV6tSp4wce3XDDDdH+dYFko3v37t6DsWvXrnbfffdZnjx5/HXtcnnppZf88169enlZaSgmnADEGjI4EH5kcCAyyOCxjSUK4BJQoNi+fbuXjyq8q7fV8uXLfTUxf/781qRJEz+I5ejRo7Z69WrvawXgwjZs2GCzZ8/2HqYVK1b0HqZbt261RYsWWYUKFax06dK+O+bWW2/18K4QD+C3TZw40T8WLlxoN998s7929uxZ3zVWu3ZtS5cunQ0dOtQPC3vttdf8UL4A4R1ALCGDA+FHBgcigwwe+5hEByIg4SqgVuQPHz7sYUKBXSv2WbJk8ZChYK8eVw0bNvS+Vjo4IrGfAVzpEh5gpNLrM2fO2JEjR+yLL77wcbR48WJ/3yOPPOIHq5QtW9bHWIECBaL6uwPJhe49mmSqV6+eh/fNmzf7vUs7OVOmTGkPPfSQtW3b1sedJqLUMxgAYgUZHAg/MjgQeWTw5IF2LkCYhQZvXfC0o6V58+ZeNqqQ/u2339r9999vNWvW9IvjnDlz7LnnnrP33nvPrrnmmmj/+kDMjysdUJQzZ04P6h07dvSdLzt37rQ2bdpY9erVrUaNGlatWjXfXfbEE0/EhX56xAHnS2yyaNCgQda3b1/fOTZ16lTfRabdZBp7K1eu9B1oobs1mXACEAvI4ED4kcGByCCDJ09cyYAwCy5iOsDorbfe8hXD/fv3W44cOTykq1xUPeOCQKGV+9y5c/uuGADnCw0HChWfffaZderUyQ8wGjBggB+okipVKi8n1fuCkjcdGha6a4bwDiQ9ttRjUTtbnn76ae/FqNLsd955x3e8aMKpcOHCtnHjRmvfvr2/LzTAE94BxAIyOBBeZHAgMsjgyRc70YEI0IWwf//+fjhR0KcqdAX+p59+sjfffNNmzpzp4WPdunV29dVXn1cqByD+AUbjx4+3SZMmWZkyZc7bNXb69Gnbu3evdenSxQ4cOOCr9YR24LcpsGu3S+fOna1p06ZxBxidOHHC0qdPH3cPq1u3rj8cazKKexWAWEQGB8KPDA5EBhk8+eHKBoSZLnKbNm3yVXqF9x07dtiaNWs81KsnnA42Ugmp3qPdMHPnzvWQQZkbkLS1a9fatGnT/AAj9TFVsNDYWrVqlfeD05h6/fXX/dTy48ePey9Gjaf//Oc/HjgAJE6HEumh+IMPPrBbbrnFX9O4UUBXeNe9SRNO2tX5/fff+/1MX6N8FECsIYMD4UcGByKDDJ48kRaAPyjhzhWFBpXgzJ8/3woWLGjjxo3zw1d0uMqnn35qo0aN8pChErg0adL49+piSXgHkpY6dWpLkSKFnTp1ynvBKXR8+OGH/jXtfFGQVx9G7Yy59957PbTzUAz89v1LPRYbNWrk4X3Lli1+n1KLA32tX79+Vrp0aQ/uOpBPY44JJwCxggwORB4ZHAg/MnjyRTsX4A8IXQVUeag+FMpVxqYDVnQx/Nvf/mZ33nmnXxxnz55tI0eO9J0vmTJl8u+jfBSIL7HVdR1a9MADD3i5qAJH69at7fbbb/dw0aBBA+97qrEWYPcLcL7gfhN633nmmWds8ODB3odR7Q3UH1il2uvXr7fPP//cx5vCuh6ggwknxhaAaCODA+FHBgcigwx++WAJAwhDyBgxYoQtXrzYVwpV0qZejNrpEnqAkd6vHTG6OGbMmDHu5xDegcTH1dKlS+27776znDlz+gPw9OnTvURU4+e2227zHqZ6aFaY0KFGoQgYQNJjSzs1NX60u6xbt25+r3rjjTfiJpyKFi3q/Uwff/xx+/HHH/2AMFHwZ2wBiDYyOBB+ZHAgMsjglxd2ogN/0JNPPul94J544gkrUqSI1apVyw9+UH8r7XTRxe/999+3iRMnehjhACPgt/Xo0cNmzJjhYT1lypS+8q6epuXKlfOvnzlzxg4dOmQdO3a0gwcPetggWACJC73fDB061EtC1dNUE0w6KCxfvnx28uRJS5cunb9H46127do+9tQDlXsVgFhEBgfCjwwOhA8Z/PJDN3rgD1CJzZw5c/ywh8cee8xXFPVRp06duFJR9YpTuFAQUWmOwrt6WXFBBBKnQKEV+cmTJ3spm4KExpqCehAuxowZY+3atfPV/M8++8zDu14HcL7gftOnTx97/vnnvRR7+PDhtn37dp90Onz4sId39TvVbrOaNWva/v37vbQ0KD0FgFhCBgfCjwwOhBcZ/PJDOxfgD/SJO3funF/Yqlev7kG+efPmflFUOc6xY8dsyZIlVq9ePevbt6+vNnKAEZD0uApW6vWg27JlS6tQoYKPq2HDhvlhYBpLChh6v3aaaUy1atWKA4yAi/DNN9/YwoUL/cFYAV27M48fP249e/b0w8BEQX7btm2+K2bBggUcYAQgZpDBgfAjgwORRwa/vPBvBPgdgvCuwx/y5s3rq4cqtXnqqafsxRdf9NXFDh06+Ht0EVTJji6EJUqU8NfoZQXEpzERjCvteClWrJh//pe//MXDhh6KFeDbt2/vD7/Tpk3z79GhYTfeeKO/l4di4MITTppsUhDftWuX3XHHHTZ//nxr2rRp3D1LZaTaeabPtaMzbdq0TDgBiClkcCC8yOBAZJDBL2+0cwEu8kIYmDVrlvdaVMBQ2Wjx4sV954sCRRDez549awMHDvSDIIJAIpSPAvHHVTAmHn30UQ8UKg3NkiWLde3a1Ro2bOgPxuq5KNpZprLtffv2xfs5PBQDF55w0mSS7kelS5f2g4oaN27s963gnrV792575513vCxbJaVB+ShjC0C0kcGB8CODA5FDBr+8sbQB/I4L4T/+8Q/76KOPrEuXLlapUiV/TavzX3/9ta1Zs8aeeeYZ77uoEp0DBw54SZy+N2EJKoBfx5VOJf/+++/t73//u2XNmtUGDRrkfeI++eQTH2cK7FrB11jTQSw68AhA4kLvN7Nnz/bdLeqxqDCeI0cOH2dt27b1lgdy+vRpP5QvRYoUVrFixbifw4QTgFhABgfCjwwOhB8Z/Mpw1c90qgd+k4bJpk2b7J577vEwoVXEfv36xX192bJlHtp1kSxcuLDlzp3bXn75ZXpZAb9BYUKl2DfccINNmTLFChQo4K//85//9JCxdetWS5UqleXMmdNDydKlS/1gMJW4sUIPJE3loosXL/Zei7169fLXdFBRo0aNvLS0aNGiPt40KaWH6HXr1vnYYsIJQCwhgwORQQYHIoMMfnljEh1IQnDASiidkqyynDRp0tjo0aOtbNmy8b6uA1f0tQDhHYgvYThYvXq1l41++eWXtnbtWitUqFC8sadDjVSarfLSatWqcYARkIRg3GiMaVeZ2hwcPHjQH4THjx8f9769e/fauHHj/NA9HQyWP39+LzVlwglArCCDA+FHBgcigwx+ZWESHUhEaIB4++23fQeMyttkxowZNmTIELvppps8eJQqVcpfT7gqn9gDAIBfKLAHvUp1mJEOL9LDr3aUadfLTz/95CvyCbH7BThf6P3mxx9/9EOJtIOsWbNmPpZGjBjhhxldCGMLQCwggwORRQYHwocMfuWhVgC4wEErWqGfPHmyh/hRo0b5ayrD6datm23ZssVfU/iQhBc+wjuQ+MFgK1assBIlStjUqVP97yVLlvRxdvz4cd/pol0vQbloQgQMIOl7lg79Umm2dr+orcGbb77p40iHgy1fvjzuexhbAGIRGRwIPzI4EBlk8CsTk+hAAkGZmw550IVQF0cd+jBy5Eh77rnn/Gv333+/n2S+bds2Ly3dsWNHlH9rILZX6INxpYfelStX+ucPPvigB4wgxCvQqy9c9erV7cyZMwQK4HeUZmtn2YQJE+zdd9/1UlGVk6rn4rRp0/zgPd2/ghDP2AIQi8jgQHiRwYHIIINfuZhEBxKhXS+vvvqqDRgwwC+G2ulSuXJl78c4bNiwuBCvk5V1krkOZAGQuGCFXg/EAwcOtFy5cvmhX/Xq1fNecTq5PDTEb9682bp06RLl3xqIfUF414RSp06dLEOGDJY6dWovHVXPYO2GUcm2QvyuXbt8YuqLL76I9q8NAEkigwPhQwYHIoMMfuWiJzqQCIUMHaayatWquAvk7t27/QK5fv16vwg+8sgj8XpYcZoykLTDhw/b7bffbh07dvQPUZ+43r17+w6ziRMn+kOxaFdZwYIFWakHLsLs2bOtXbt2tnjxYt/1kiJFCnv44Yf90KKGDRv6fevaa6/1e9cLL7zgO8+4VwGIVWRwILzI4EBkkMGvTPwbBEIEPaq0s+XcuXP23Xff+d8VzvPmzWu9evWykydP+kq9+luJQkZoqRyA82k87dmzx9KnTx83pnQCuXbGlC1b1stKtVIv2lWmcZVYzzgA8f3www8e0HWPCg4Ce+mll6x06dIe2MeMGeO7YXQAn/qe6l4V2h8VAGIBGRyIDDI4EBlk8CsTiQNXtIQXsSCEV6pUyXbu3Okh/dSpU3GvK6hrJf8vf/mLl5geOHDAX+cAI+BXiYWD6667zqpUqeIlpIcOHfIxpfGULl06+/Of/+yhvXnz5vF2nrELBogvtHgweMDVONFBYOobrHuRHpalf//+viNm3rx5vvNFPU6DexUTTgCijQwOhB8ZHIgMMjgC/BvEFSu09HP8+PHWrVs3q1+/vl/sgoMgVOLWvXt3W7BggR9gNGjQILvxxhutZ8+eXqazZs2aaP9jADE7rrZs2eJjZOvWrf73zp07ewDRWDt+/Li/T2FDq/jqc1q3bl0vLVUQodMYcP7YCp0sCsbIfffd5zvK2rdv76FeoV2OHDlid911lxUuXNjGjh3rfweAWEAGB8KPDA5EBhkcoZhExxUrCBkK6P369fPecPnz5/cQofK2WrVq2dy5c23RokXWpk0bq1mzpu96efrppy1btmx+UVT5DoBfhJZUP/nkk9asWTOrU6eOB/dWrVpZtWrVvG+cdpjddNNN1qJFC7v11lvtq6++sqpVq1qBAgU8gOhQFnaWAYk/GKs0tGXLln6Peu655yxlypQ2Y8YMP6zojjvusPfee8+WLl1qPXr0sDRp0thrr71m3377rd/PACAWkMGB8CKDA5FBBkdCTKLjirZw4ULf7fL+++/bqFGj7IEHHvDXFc5FF0jtdlGInzlzpq1du9bDhVbsVbqj/lcAfhGE7qFDh9q4ceN8TCmc62RylbKtW7fOH4Z1YrkCvQJJjRo1fFzpc63S586d28cWu2CAXwXhXTswn332WcuTJ4/de++93iO4S5cu3t7gk08+8bGjA/dUln3ixAlvh6DXVKqdL1++aP9jAEAcMjgQPmRwIDLI4EjoT+e9AlxBdIFT2WjJkiXt7bff9lIcrTA2bdrUjh075ocaFSlSxHLkyOHv37Bhgw0ZMsT+8Y9/ePjPmTNntP8RgJii/qXqqThixAi77bbb7IMPPrAJEyZ4oL/lllvs3//+t918883+EdBYUxCZM2eOffrpp76qDyC+lStX+kTS9OnTrWLFirZ8+XLvxaixlDZtWg/pek0PzXqYLliwoP/Zt29fH5e61wFArCCDA+FFBgcigwyOUOxExxVD/d++//77eK+pD9zevXs9OHTs2NFX73VCucyfP98GDx5shw8f9r9rVT5z5sxebqrVxtAAAuAXOplcY+qaa67x3qZNmjTxcaUSUpVrK8hr11lgz549/nXtNtODsR6YAfwq2BGm+1X27Nk9vM+aNct7LWpHmXaW6WsaQwrswSFh6oeqcm31Ypw9eza7NgFEDRkciDwyOBBeZHAkhkl0XBFULtqoUSNfhVe56IoVK/z12rVrW8aMGa1BgwbegzEI7zpUZcqUKV6+kyVLFn9NF0aV4qiMh5ABmO9YUTgYPny4B3FRSNc4UQmbwoPCuR6OZf/+/R7eg4diUelo69atfbcMD8XALzTZpB6KoSXaGTJk8J2bKtFWaFdLgw4dOvjXtPNswIABtmvXrrifoZ0xZcuWtWXLljG2AEQNGRwIPzI4EBlkcPyWq36m6RUuc6+88oo98cQT3qNKBzz079/fDyiaOnWqr9jrwAe9RyuEOohFQUQr9SojXb9+vZ+4HHqgBADzcaPDwLJmzerBXGNJu8N0MJEekHW4ih6YtcNMD8lHjx71g1i0G03vUwmcbj8cXgTEp7YGCum7d+/2h2G1LyhfvrwdOnTIJ5nUxqBbt242cOBAf/+ZM2escePGHtjfeuutePcq7l0AookMDoQfGRyIDDI4LgaT6LjsQ0anTp381OS6dev6a4899piNHDnSw7lWBnXgw+TJk23SpEl+uIpOLNeBEUHA10nlChsAfqEH3IcfftjHzZ133mkffvih3XffffbQQw95H0aNGwV3lZGWKlXKx5genk+ePGmrV69mXAFJ0GSSwrl6KKrfr0J6pkyZvMxaAV39GLUTUz2CNeZEoX3fvn1MOAGIKWRwIPzI4EBkkMFxsZhEx2VJ/1mrFEe9qypVquR94dKnT+9f0+q8LoYqadMKfNWqVS116tT+Na06qlxHq/b6mg5g0QURwC80djSGdFCRyrJFO1u0i0xhXmXbCuw6mGjTpk22ePFiX73XyeX333+/h3bGFZD4hJN2uWjCqX79+v6awnqfPn281Fr9F0Vj7L333vMPHciXK1cumzhxIg/GAGICGRyIDDI4EBlkcPweTKLjsqaQfu+993o/uEGDBnng0E6XKlWqeOBQuY52vegAlnr16vlOmWzZsvn3spIInE8r7uq/qPGjz7W7RWNMu1502IoemrXbRX3itMss4UEqBAzgfBoz2jGmfqabN2/2cSVqe/DRRx/57hjdk9RXWBNMGkPqa6odMrpPMeEEINaQwYHwIoMD4UcGx+/FJDouW0EAV4jXiqKCuT60I0YHqcg333xjGzZs8INX9DWdtkxoBy48rrRK/9JLL/mBX/q7ythUQlqoUCFbunSpf2iFXq+rZ2PPnj2j/WsDMW/Lli1Wq1YtPzRv9uzZfijY559/bs2bN/ddZSrd1k4Xfa7JpqZNm1rBggX9e+ltCiCWkMGB8CODA5FBBsfvwSQ6rogQr1VElbnpgqedLwoeCS94wd+5EAKJC8aGxpXK2UaPHm3r1q2zzz77zFfwQ23bts3++c9/2t13383KPHCRtm7dajVq1PAdLjfccIOXkKo/cEATUPr46quvvA8qE04AYhUZHAgfMjgQWWRwXCwm0XHFhHhdCLVyqLLSp556ykveEpa2UT4KXHyInz59uod4lbPpUDCVZJ87d85SpEgR73socQMunh5+VZ6tklHdt/RnUmOIexaAWEYGB8KHDA5EFhkcF4N/67gs6CIW2tcqlC5u+rpKdObOnet9rdSbUWVuEtobjgshcGHBTjGNlcaNG1unTp3shx9+sJYtW/rKvcJ76HgUwjtw8VSSrXJttTrQGNOhYMEYCvY96M9gHAJANJHBgUuDDA5EFhkcF4N/87gsBBexRx991IYNG5ZkiK9du7aHeK3cv/3221H6bYHkI7RYKfg8NMQ3adLEOnfu7GNOD8nHjx8nVAB/UOHChW3RokVeWqqH4wMHDvjrQZsD/UnLAwCxgAwORAYZHLj0yOD4LbRzQbIW2jtRhxPVqVPHVw/Lly+f6PuDshv1jytTpgyr80AiQsvTEvYnDS29Di0rnThxoq1Zs8bGjBlDgAeS8HtLP1VWWqxYMX9I1sFhABAryOBA+JHBgcgggyNcmETHZeH555/31Xf1ghsyZMgF3xsaSOgTByQdMFR2vWLFCh8nWpXv3bv3ee9P7DAwesQB5wsdF9u3b/eya/09X758F/w+lZTmzp07XtsDAIgVZHAgPMjgQGSQwRFOXGGR7J0+fdpX3wcOHOgrhr8ldEWf8A7EFwSMHj16WL9+/Tw4FClSxJ5++mlr1arVee9PGN5DfwaAX4T2TtSDcP369a1y5cq+Y1MTUAnbH4RSwFd41w40AIglZHAgfMjgQPiRwRFupBckOwnDQurUqb3EJnv27DZu3DhbuHCh1axZ87z3Abg4K1eutHfeecdmzpxpFStWtDlz5liqVKmsXLly8d4XjDHGGXBhwRgZOnSo7y5TP2CNH006PfbYY37I3gsvvHDBn8EuGADRRgYHIosMDoQXGRzhxiQ6km0pjj7/6aefLGXKlJYzZ07r06ePHTt2zBo0aGALFiywSpUqEeKB/4HCRNq0aT28v/vuu9aiRQsPFx06dLATJ07YkiVLvPcpYwu4eCrJXrZsmXXp0sVq1Kjhr2myKX/+/PbXv/7VSpYsac2bN4/2rwkAiSKDA5FHBgfCjwyOcKLeB8kyvI8ePdoaN27s5Tgqw5Fs2bLZ8OHDrWHDhnbXXXfZ8uXL48rcACQ9rhLKlSuXl5COHTvWw7vGmMK7rFu3zqZNm2Zff/11FH5bIPlIeO85deqU92EMXtfYU6ivV6+etW/f3t566y07e/ZsomMSAKKJDA6EHxkciAwyOCKJSXQkG0F479Wrlw0ePNiuv/56X0ns3r2794778ccf7ZprrvGy0kaNGtltt91mX3zxBSv1wEX0iFMZ9pEjR/zz9OnT286dO+2hhx7y3WVBeFfvU5XC6fs0/gAkTiE8uPf861//8qCeIUMG3+0yZcoU27x5s4+9YPxpzOn92tVJP1MAsYYMDoQXGRyIDDI4Io3/SpCsqD/cjBkzbNasWR4kihcv7j2qVOb28MMP+yqjQry+1r9/fz+MBcCFA8aOHTv8ofjuu+/2cuzChQv7mLr66qtt69at9uqrr3pJad26dW3Pnj02ceJEdpgBF7FjU/ehnj172scff+x/Vwm2Hn41EaWxpffpwXjjxo2++wwAYhUZHAgPMjgQGWRwXApX/cwVGMmETkXW6qECRufOne2DDz6wZs2aeZmbyki1uqjDIXSCebp06eK+T6uPf/oT7f+BQGif0oEDB9r69evtq6++sk2bNtnNN99sH330kWXJksUPNtIBLBs2bLAbb7zRrrvuOps8ebIHe41HDlkBkqbg/tprr9n48eP9QLAcOXL463PnzvV2CDo8rESJEnb8+HEP/RqHGlv0EQYQa8jgQHiQwYHII4MjkphER8wKLmKhF7PDhw/7xU4BXT0XmzRp4qWk6g2nA1gOHjzoq44qfwNwYepfqgfeOXPmeDhfsWKFvfTSSx7OP/nkEw/xR48e9cPDUqVKFVfuxkMxcGELFy70HovaPaaHYo0h3Z9Uol22bFn/+/Tp0/3elT17dnvwwQd9TDG2AMQCMjgQWWRwIDLI4Ig0/itBzJfiqHRNPaoUHLJmzeqlouplpf6LwenKChc6zEinKpcpUybKvz0Qez777DOrUKFCXPnnuXPnbM2aNd5rsXr16v6adrrkyZPHd5npAVm7YTJnzhzvIVqfEzCA+BLuXNE9TA+81157rZeMaveYDi1SQM+UKZMfute6det4P0MPzowtANFGBgfCiwwORA4ZHJcaPdER0wetDBgwwO69914PGJUrV7YPP/zQX0+dOrWvHmqFURfCv/3tb37issp1VN6miySAX7z55ptWqVIlX3VXyAgOT9FK/Oeffx73Po0dPRSrLHvt2rVWs2ZNL93W+4PTyilxA5Lubbp37964A4x0H1O7A9279u/f77szp06daidOnLClS5ee93MozQYQbWRwILzI4EDkkMERDUyiI+YEF8J+/frZiy++aE899ZTNnj3bcubMaU2bNrVdu3b5oRDqE6eDWNq0aeMnms+fPz+u9JSVROBXOoxIJdcKE9OmTYt7/c477/TSbB0UFvrQW7RoUR9rKtnu1KmTB31OKwcu7gAj9VXUjjNNQNWrV8/7MapvcLt27XynmUq006ZNG+1fHQDOQwYHwosMDkQGGRzRQspBTFIPOK0STpgwwWrXru394rRa/+yzz1qBAgU8pCu4a3eMSkp1krkuovSyAuLTWFHpmsKEdo/df//9/qeCxT333GOzZs2yl19+2U6ePGmNGjXysK4H5pIlS1qpUqX8QXnfvn2WN2/eaP+jADEnCO8K7q+//rr3M1U5tmiMBTSuDh06ZG3btrU0adJYlSpVovY7A8CFkMGB8CCDA5FDBke0sKyJmKSVea0kKkSoJ5z6LCq86+CHU6dO+Wnm6tOYP39+X7HXRVSrkYR3IPEecVqJD15TcFdJm3qbTpo0yXsujhw50kO6Sk63bdvmhx3deuut/lDM+dNA0rQDU/0WFyxYYI0bN/ZDig4cOGCrVq3yg/hE46tFixb+ug4MU9mo+i8CQKwhgwN/HBkciDwyOKKBtIOYOwxC8uXL54eq9O3b12bOnOkXP5XhiC6An376qRUvXtxy584d9z2UugHxBeOqd+/evqNs6NChXtKmHqYqK1WA0J8TJ070/qY6+EgHh+mAMFH/xly5cvkuGgCJ00Ou7kVqd7BlyxZ/OFYP1KuvvtrDvAL+TTfd5OPxkUce8YkmdmwCiAVkcCAyyOBA5JHBEQ3814OY6WWlnor6u05Slj//+c82YsQIDxNBeNdhEOoPp+BRp06dqP7uQCxSaXVorzeVr82bN893kWkVXhTateOlZcuWfriRDg7TjjN9yIoVK7xvo04y14p9xowZo/bPA8TihFPoxJN2tHz77bfe3mD16tXe/uDJJ5/0e9njjz9uGzZssLvvvts/RPcvwjuAaCODA+FFBgcihwyOWMF/QYiqILzr4CKFDPVhVLhQ7zh97N6929asWeOHr2hnzObNm71vnE4t10Uz9AEAuNJp51ixYsVs2LBhca+dO3fODwILPXVcK/OPPvqoffzxxz7ezpw54+XagZ07d/pYU09U/TwA8Sec1NJAu1xSpEhhtWrV8t1lO3bssFatWlm1atU8vOvhOX369PEODJPQsQgA0UIGB8KHDA5EDhkcseSqn2m0hSjQKmBwEfv73//u/RV79OhhP/zwgz333HN+GITK27RC/+qrr9rKlSv9IqidMXofpTjA+b788ks/eVzjRsFd4UJ0kNGxY8f8gKLQ8muFdj0gZ8uWzZYtWxZvZV87zhQ+AMQP72ptoPJQvVawYEEbO3Zs3MFFCvW6N2miSQ/HGndLliwhtAOIGWRwIPzI4EBkkMERa5hER1R7LyqY60OhQuVsop5w2vWichyF93Tp0l3wAQBA/LGlFXkdBqZyUJWBvvHGG/6gXKFCBX8AVmBXyamCfceOHX33TPC9ifVHBfCLXr16+eRS165dvU+pegZXrFjR+wZrQkm7Y4YPH+47yLSrU/czhXruWQCijQwORAYZHIg8MjhiBTV4uGR0YvLGjRvj/q5SNQWKxx57zI4fPx4XHvTawoUL7YMPPrAHH3zQ9u/ff97P4kIIxBcausuXL+/BoX379nb27Fnvu3jPPff4YUa33Xabl7vpT5Vq16xZ079XK/oJfw6AX82dO9c/Zs+ebT179vRDjE6fPm2LFi2y22+/3XfBpEmTxooUKeL3MfU1DXbFcM8CEE1kcCByyOBAZJHBEUuYRMclo7I2XdiCoF60aFGbNWuW73LRhU494YLDIhRAFOJ1qMq4ceOi/asDMWvTpk1eqqbDhwJaldcuGH1op4uCxRNPPOFl2vq7Qn3lypX9ABYFC63Q09cUuDCVZ6s8VPcnTTC1bt3ahg4dau+//77fwzRJpbCuh+V+/fr5rhgOMAIQC8jgQPiRwYFLgwyOWEI7F0RcwhKaMWPGeJBXgNDrKndTX7hu3br5QUZaNQzK2RROChUqxAUQSIRK2gYPHuy9EzWW7rjjDnv99dfjvr5q1So/cKVq1ao2efJkS5069Xljkr6mwMXTzrHMmTP77jG1O+jTp4/t27fPDzLSoUbt2rVj0glAzCCDA5FBBgcuLTI4YgXLnoi4ICgE6zWjRo2yBx54wPswKkg0adLE3nzzTXvhhRe8t5UCRbAb5qabboo7wAjAr3RAUYcOHXzM6ICV+vXr+64xPRCLSkPLli3rq/XaJaMVe/WHk9AHasI7kLigvDr0/pU3b17bu3evtzhQgBfdr8qUKeO7ytT3FABiBRkcCD8yOBBZZHDEMibRcclt377d8uTJ4z3hVH6jEH/fffd5iNeJyw8//LC/FtoXjpAB/Ordd9/1XqU6SEU7yEqUKOHjSQ+6e/bs8fcEpaEK8fPmzbPp06f7YSsAkrZ48WIbOHBg3BhKrE9p9uzZvTxbO9B0eFGLFi081JcqVSquNBsAYhEZHPhjyOBAZJDBkVwwiY6IC70ABrtZPv30U7v22mt9N0xoiNcK4pYtW+gNByRBvRQXLFhg119/ve3atSvudfWFk7Vr11qPHj1s2LBhvuvl5MmTHuK//PJLe/rpp6P4mwOxP7b0oKsPjZ+EIT7YDZMpUyYbPXq073pp27atf592ogXv5QAjALGCDA6EDxkciAwyOJITeqIjYiuJCubqVSW6qAWhPLT/W7ly5ezw4cPeV06fh174gp6MAOJT/zcdUKR+iyrFXr58ue8ue/zxx61gwYK+o+yLL77w3nFp06b1B+Pq1av799J/EUiadrPoYVitDho0aOAPwwnvYaJD+HTI0ZEjR7y8VF9jbAGIBWRwIHLI4EBkkMGRXDCJjrDTimCXLl08wKvERieSJ7wAqgxHhxdJpUqVbP369X7BLF68eFR/dyC5UD+4QYMG+ankx48f98CeK1eueGNNBxl9/fXX1rt3b4IF8DvH1po1axIN8fq6HpbVA7VRo0bxvgYA0UQGByKPDA5EBhkcyQGT6IjaSmLo5wr8I0aMoAQH+B0OHDhgzz77rO+CUSm2QoVodT5FihTx3qtybcYX8PtD/F//+lfr2bNn3A40hfaDBw962wMejAHEGjI4EHlkcCAyyOCIdUyiI2oriQofXbt29UNZ6tSp418jZADhGWeMJSB8Y6thw4bWpk0bD++6d23YsMF3cjLOAMQiMjgQeWRwIDLI4IhlTKIjolhJBC7NONNumHXr1lm1atXiTjYHEJ6xpQOMtm3bZjlz5rSNGzd6eKf/IoBYRgYHIo8MDkQGGRyxikl0RBwricClGWfdu3e3VKlS2SuvvMKBYEAYx5Z2lx06dMjmzJlDeAeQbJDBgcgjgwORQQZHLGISHZcEK4lA5OmU8kyZMnmpti7thHggPI4ePWoZM2b0scU9C0ByQgYHIo8MDkQGGRyxhkl0XDKsJAKXBqeUA5HB2AKQHJHBgUuDnABEBmMLsYJJdFxSrCQCAAAAlxYZHAAA4I9hEh1RwUoiAAAAcGmRwQEAAP43TKIDAAAAAAAAAJAEtiEAAAAAAAAAAJAEJtEBAAAAAAAAAEgCk+gAAAAAAAAAACSBSXQAAAAAAAAAAJLAJDoAAAAAAAAAAElgEh0AAAAAAAAAgCQwiQ4AuGSqVq1qjzzySNh/br9+/ezmm28O+88FAAAAkjsyOAD8cUyiAwDcAw88YFdddZV17NjxvK916tTJv6b3XIxPPvnE3//DDz9E4DcFAAAALg9kcABIHphEBwDEyZMnj02dOtVOnz4d99qZM2dsypQpljdv3qj+bgAAAMDliAwOALGPSXQAQJxSpUp5iJ89e3bca/pc4b1kyZJxr/33v/+1wYMHW4ECBSx16tRWokQJmzlzpn/tX//6l1WrVs0/z5w583m7Z/S93bt3tyxZsliOHDm8DDTU7t27rX79+pYuXTrLkCGDNW7c2A4cOBDvPUOGDLHs2bNb+vTprW3btv6QAQAAACRHZHAAiH1MogMA4mnTpo29/vrrcX+fMGGCtW7dOt57FN7feOMNGzt2rG3evNkeffRRa968uS1ZssQfAGbNmuXv2759u+3bt89efPHFuO+dNGmSpU2b1latWmVDhw61/v3726JFi+LCvcL7kSNH/Gfp9Z07d1qTJk3ivn/69Oke+p999llbu3atXXfddfbyyy9fgv9nAAAAgMgggwNAbLvq559//jnavwQAIPq0U0X9E8ePH+8hXOFbChUqZN9++621a9fOMmXKZK+88orvYPnoo4+sfPnycd+vr586dcrLTtWPUTthjh496t8TeqjRf/7zH1u2bFnca2XKlLHq1av7zhYF9rvvvtt27drlv4Ns2bLFihYtaqtXr7Zbb73VKlSo4DtyxowZE/czypUr5zthNmzYcIn+3wIAAAD+ODI4ACQPf4r2LwAAiC1Zs2a12rVr28SJE03rrPr82muvjfv6V1995UG9Ro0a8b7v3Llz8cpNk1K8ePF4f9culoMHD/rnW7du9eAehHcpUqSIPwToawrw+jPhwUt6kPj444//539mAAAAIJrI4AAQ25hEBwAkWk7auXNn/zx0t4mcPHnS/5w3b57lypUr3tdSpkz5mz/76quvjvd39WtUCSkAAABwJSODA0Dsoic6AOA8d911l+9q+emnn+zOO++M9zXtSlFQ1+FDN9xwQ7yPYPdKihQp/E+Vjf4ehQsX9rJVfQRUSqoSV/3vBu9RL8dQK1eu/J//WQEAAIBYQAYHgNjFTnQAwHn+7//+z0s2g89DpU+f3h5//HE/yEi7VypVqmTHjh2z5cuXW4YMGaxVq1aWL18+393y/vvvW61atSx16tSWLl263/zfveOOO6xYsWLWrFkzGzlypP373/+2hx56yKpUqWKlS5f293Tt2tV7R+rvFStWtLfeessPVrr++usj9P8GAAAAEHlkcACIXexEBwAkSmFcH4kZMGCA9e3b1wYPHuy7UrRrRqWlBQoU8K+rxPSZZ56xnj17Wvbs2ePKUn+LQv+cOXMsc+bMVrlyZQ/0CubTpk2Le0+TJk38f7t79+52yy232DfffGMPPvhgmP6pAQAAgOghgwNAbLrqZ51YAQAAAAAAAAAAzsNOdAAAAAAAAAAAksAkOgAAAAAAAAAASWASHQAAAAAAAACAJDCJDgAAAAAAAABAEphEBwAAAAAAAAAgCUyiAwAAAAAAAACQBCbRAQAAAAAAAABIApPoAAAAAAAAAAAkgUl0AAAAAAAAAACSwCQ6AAAAAAAAAABJYBIdAAAAAAAAAABL3P8DX3jtk9mIyzsAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Performance comparison charts\n", "plt.figure(figsize=(15, 10))\n", "\n", "methods = [r['Method'] for r in results_summary]\n", "train_r2s = [r['Train R²'] for r in results_summary]\n", "test_r2s = [r['Test R²'] for r in results_summary]\n", "cv_means = [r['CV R² Mean'] for r in results_summary]\n", "\n", "x = np.arange(len(methods))\n", "width = 0.25\n", "\n", "# R² comparison\n", "plt.subplot(2, 2, 1)\n", "plt.bar(x - width, train_r2s, width, label='Train R²', alpha=0.8)\n", "plt.bar(x, test_r2s, width, label='Test R²', alpha=0.8)\n", "plt.bar(x + width, cv_means, width, label='CV R² Mean', alpha=0.8)\n", "plt.xlabel('Method')\n", "plt.ylabel('R² Score')\n", "plt.title('R² Comparison Across Methods')\n", "plt.xticks(x, methods, rotation=45)\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# RMSE comparison\n", "train_rmses = [r['Train RMSE'] for r in results_summary]\n", "test_rmses = [r['Test RMSE'] for r in results_summary]\n", "\n", "plt.subplot(2, 2, 2)\n", "plt.bar(x - width/2, train_rmses, width, label='Train RMSE', alpha=0.8)\n", "plt.bar(x + width/2, test_rmses, width, label='Test RMSE', alpha=0.8)\n", "plt.xlabel('Method')\n", "plt.ylabel('RMSE')\n", "plt.title('RMSE Comparison Across Methods')\n", "plt.xticks(x, methods, rotation=45)\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Cross-validation with error bars\n", "plt.subplot(2, 2, 3)\n", "cv_stds = [r['CV R² Std'] for r in results_summary]\n", "plt.bar(x, cv_means, yerr=cv_stds, capsize=5, alpha=0.8)\n", "plt.xlabel('Method')\n", "plt.ylabel('CV R² Score')\n", "plt.title('Cross-Validation R² (with std)')\n", "plt.xticks(x, methods, rotation=45)\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Overfitting comparison\n", "plt.subplot(2, 2, 4)\n", "overfitting_scores = [r['Train R²'] - r['Test R²'] for r in results_summary]\n", "colors = ['red' if score > 0.1 else 'orange' if score > 0.05 else 'green' for score in overfitting_scores]\n", "plt.bar(x, overfitting_scores, color=colors, alpha=0.8)\n", "plt.xlabel('Method')\n", "plt.ylabel('Overfitting Score (Train R² - Test R²)')\n", "plt.title('Overfitting Analysis')\n", "plt.xticks(x, methods, rotation=45)\n", "plt.axhline(y=0.1, color='red', linestyle='--', alpha=0.5, label='High overfitting')\n", "plt.axhline(y=0.05, color='orange', linestyle='--', alpha=0.5, label='Moderate overfitting')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('updated_performance_comparison.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 7. Feature Importance Comparison\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************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", "text/plain": ["<Figure size 1500x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Coefficient Comparison:\n", "                         Feature  Linear   Ridge   Lasso\n", "0              ABI_Duration_Days -0.3952 -0.3463 -0.3385\n", "1                       Avg Temp  0.1418  0.1206  0.0000\n", "2                            KSM  0.0310  0.0015 -0.0000\n", "3   ABI vs Segment PTC Index Agg -0.1700 -0.0912 -0.0000\n", "4   ABI_Promo_W_Num_Distribution  0.1136  0.0088 -0.0000\n", "5               ABI Mechanic_FID  0.5276  0.1420  0.0000\n", "6         ABI Mechanic_Immediate  0.6047  0.1650  0.0000\n", "7                ABI Mechanic_LV  0.6950  0.1845  0.0000\n", "8                 Same Week_True  0.3494  0.2485  0.1609\n", "9                   1 wk after_1  0.0831  0.0680  0.0000\n", "10                  2 wk after_1  0.0869  0.0471  0.0000\n", "11                 1 wk before_1 -0.0445 -0.0044  0.0000\n", "12                 2 wk before_1  0.0095 -0.0033 -0.0000\n"]}], "source": ["# Feature coefficients comparison\n", "plt.figure(figsize=(15, 8))\n", "\n", "# Create coefficient comparison dataframe\n", "coef_comparison = pd.DataFrame({\n", "    'Feature': feature_columns,\n", "    'Linear': lr_model.coef_,\n", "    'Ridge': ridge_model.coef_,\n", "    'Lasso': lasso_model.coef_\n", "})\n", "\n", "# Plot coefficients for each method\n", "x_pos = np.arange(len(feature_columns))\n", "width = 0.25\n", "\n", "plt.bar(x_pos - width, coef_comparison['Linear'], width, label='Linear Regression', alpha=0.8)\n", "plt.bar(x_pos, coef_comparison['Ridge'], width, label='Ridge Regression', alpha=0.8)\n", "plt.bar(x_pos + width, coef_comparison['Lasso'], width, label='Lasso Regression', alpha=0.8)\n", "\n", "plt.xlabel('Features')\n", "plt.ylabel('Coefficient Value')\n", "plt.title('Feature Coefficients Comparison Across Methods')\n", "plt.xticks(x_pos, feature_columns, rotation=45, ha='right')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig('updated_feature_coefficients.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"\\nCoefficient Comparison:\")\n", "print(coef_comparison.round(4))\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}