#!/usr/bin/env python3
"""
Focused Outlier Removal Based on Regression Analysis
This script targets the specific outliers visible in the regression plots.
"""

import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load the current dataset"""
    return pd.read_csv('cleaned_business_logic.csv', index_col=0)

def identify_regression_outliers(df, target_col='ABI MS Promo Uplift - rel'):
    """Identify outliers that are affecting regression performance"""
    print("🎯 IDENTIFYING REGRESSION OUTLIERS")
    print("=" * 50)
    
    outliers_to_remove = set()
    
    # 1. Remove extreme values in target variable (visible in your plots)
    print(f"\n📊 Analyzing {target_col}:")
    
    # Use a more aggressive approach for the target variable
    Q1 = df[target_col].quantile(0.05)  # 5th percentile
    Q3 = df[target_col].quantile(0.95)  # 95th percentile
    
    target_outliers = df[(df[target_col] < Q1) | (df[target_col] > Q3)]
    outliers_to_remove.update(target_outliers.index)
    
    print(f"  Extreme values (outside 5%-95% range): {len(target_outliers)}")
    print(f"  Range: [{Q1:.3f}, {Q3:.3f}]")
    print(f"  Current range: [{df[target_col].min():.3f}, {df[target_col].max():.3f}]")
    
    # 2. Remove high-leverage points in key predictor variables
    key_predictors = [
        'ABI Coverage',
        'ABI Base W_Distribution', 
        'ABI_Promo_W_W_Distribution',
        'ABI Base Num_Distribution',
        'ABI_Promo_W_Num_Distribution'
    ]
    
    for col in key_predictors:
        if col in df.columns:
            print(f"\n📊 Analyzing {col}:")
            
            # Use 2.5% and 97.5% percentiles for predictors
            p_low = df[col].quantile(0.025)
            p_high = df[col].quantile(0.975)
            
            col_outliers = df[(df[col] < p_low) | (df[col] > p_high)]
            outliers_to_remove.update(col_outliers.index)
            
            print(f"  Extreme values (outside 2.5%-97.5% range): {len(col_outliers)}")
            print(f"  Range: [{p_low:.3f}, {p_high:.3f}]")
    
    # 3. Remove multivariate outliers using Mahalanobis distance
    print(f"\n📊 Analyzing multivariate outliers:")
    
    # Select numeric columns for multivariate analysis
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    # Remove columns with missing values
    clean_cols = [col for col in numeric_cols if df[col].isnull().sum() == 0]
    
    if len(clean_cols) > 1:
        try:
            from sklearn.preprocessing import StandardScaler
            
            df_numeric = df[clean_cols].copy()
            scaler = StandardScaler()
            df_scaled = scaler.fit_transform(df_numeric)
            
            # Calculate covariance matrix and Mahalanobis distance
            cov_matrix = np.cov(df_scaled.T)
            inv_cov_matrix = np.linalg.inv(cov_matrix)
            
            mahal_distances = []
            for i in range(len(df_scaled)):
                diff = df_scaled[i] - np.mean(df_scaled, axis=0)
                mahal_dist = np.sqrt(diff.T @ inv_cov_matrix @ diff)
                mahal_distances.append(mahal_dist)
            
            # Use 97.5% threshold for Mahalanobis distance
            threshold = np.percentile(mahal_distances, 97.5)
            mahal_outliers = df.iloc[[i for i, d in enumerate(mahal_distances) if d > threshold]]
            outliers_to_remove.update(mahal_outliers.index)
            
            print(f"  Multivariate outliers (top 2.5%): {len(mahal_outliers)}")
            
        except Exception as e:
            print(f"  Could not calculate multivariate outliers: {str(e)}")
    
    print(f"\n🎯 TOTAL OUTLIERS IDENTIFIED: {len(outliers_to_remove)}")
    print(f"📊 Percentage to remove: {len(outliers_to_remove)/len(df)*100:.2f}%")
    
    return outliers_to_remove

def apply_focused_cleaning(df, outliers_to_remove):
    """Apply the focused cleaning"""
    print("\n🧹 APPLYING FOCUSED CLEANING")
    print("=" * 40)
    
    df_clean = df.drop(outliers_to_remove)
    
    print(f"📊 Original rows: {len(df)}")
    print(f"📊 Cleaned rows: {len(df_clean)}")
    print(f"📊 Removed: {len(outliers_to_remove)} ({len(outliers_to_remove)/len(df)*100:.2f}%)")
    print(f"📊 Retained: {len(df_clean)/len(df)*100:.1f}%")
    
    return df_clean

def validate_cleaning(df_original, df_clean, target_col='ABI MS Promo Uplift - rel'):
    """Validate the cleaning results"""
    print("\n✅ VALIDATION RESULTS")
    print("=" * 30)
    
    # Basic statistics comparison
    print(f"\n📊 {target_col} Statistics:")
    print(f"{'Metric':<12} {'Original':<10} {'Cleaned':<10} {'Change':<10}")
    print("-" * 45)
    
    orig_mean = df_original[target_col].mean()
    clean_mean = df_clean[target_col].mean()
    orig_std = df_original[target_col].std()
    clean_std = df_clean[target_col].std()
    orig_skew = stats.skew(df_original[target_col])
    clean_skew = stats.skew(df_clean[target_col])
    orig_kurt = stats.kurtosis(df_original[target_col])
    clean_kurt = stats.kurtosis(df_clean[target_col])
    
    metrics = [
        ('Mean', orig_mean, clean_mean),
        ('Std', orig_std, clean_std),
        ('Skewness', orig_skew, clean_skew),
        ('Kurtosis', orig_kurt, clean_kurt)
    ]
    
    for name, orig, clean in metrics:
        change = ((clean - orig) / orig * 100) if orig != 0 else 0
        print(f"{name:<12} {orig:<10.3f} {clean:<10.3f} {change:<+10.1f}%")
    
    # Check remaining extreme outliers
    z_scores = np.abs(stats.zscore(df_clean[target_col]))
    extreme_outliers = len(z_scores[z_scores > 3])
    
    print(f"\n🎯 Remaining extreme outliers (Z > 3): {extreme_outliers}")
    print(f"📊 Data quality improvement:")
    print(f"  • Skewness: {orig_skew:.2f} → {clean_skew:.2f}")
    print(f"  • Kurtosis: {orig_kurt:.2f} → {clean_kurt:.2f}")
    print(f"  • CV: {orig_std/orig_mean:.3f} → {clean_std/clean_mean:.3f}")
    
    return {
        'extreme_outliers': extreme_outliers,
        'skewness_improvement': (orig_skew - clean_skew) / abs(orig_skew) * 100,
        'kurtosis_improvement': (orig_kurt - clean_kurt) / abs(orig_kurt) * 100,
        'data_retention': len(df_clean) / len(df_original) * 100
    }

def create_summary_report(df_original, df_clean, validation_results):
    """Create a summary report"""
    print("\n" + "=" * 60)
    print("📋 FOCUSED CLEANING SUMMARY REPORT")
    print("=" * 60)
    
    print(f"""
🎯 CLEANING RESULTS:
• Original dataset: {len(df_original)} rows
• Final dataset: {len(df_clean)} rows
• Data retained: {validation_results['data_retention']:.1f}%
• Removed: {len(df_original) - len(df_clean)} rows ({100 - validation_results['data_retention']:.1f}%)

📈 QUALITY IMPROVEMENTS:
• Skewness improvement: {validation_results['skewness_improvement']:.1f}%
• Kurtosis improvement: {validation_results['kurtosis_improvement']:.1f}%
• Remaining extreme outliers: {validation_results['extreme_outliers']}

✅ RECOMMENDATION:
Use 'final_cleaned_ads_v3.csv' for your regression analysis.
This dataset should show much better regression performance
with fewer outliers affecting the model fit.
""")

def main():
    """Main function"""
    print("🚀 FOCUSED OUTLIER REMOVAL FOR REGRESSION ANALYSIS")
    print("=" * 60)
    
    # Load data
    df = load_data()
    print(f"📊 Starting dataset: {len(df)} rows, {len(df.columns)} columns")
    
    # Identify outliers
    outliers_to_remove = identify_regression_outliers(df)
    
    # Apply cleaning
    df_clean = apply_focused_cleaning(df, outliers_to_remove)
    
    # Validate results
    validation_results = validate_cleaning(df, df_clean)
    
    # Save the final dataset
    df_clean.to_csv('final_cleaned_ads_v3.csv')
    print(f"\n💾 Saved: final_cleaned_ads_v3.csv")
    
    # Create summary report
    create_summary_report(df, df_clean, validation_results)
    
    return df, df_clean, validation_results

if __name__ == "__main__":
    df_original, df_final, results = main()
