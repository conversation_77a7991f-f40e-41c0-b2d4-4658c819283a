#!/usr/bin/env python3
"""
Simple Validation Script for Outlier Removal
"""

import pandas as pd
import numpy as np

def main():
    print("🔍 SIMPLE VALIDATION OF OUTLIER REMOVAL")
    print("=" * 50)
    
    try:
        # Load datasets
        df_original = pd.read_csv('clean_new_test_output_ads_v3.csv', index_col=0)
        df_cleaned = pd.read_csv('cleaned_business_logic.csv', index_col=0)
        
        print(f"📊 Original dataset: {len(df_original)} rows, {len(df_original.columns)} columns")
        print(f"📊 Cleaned dataset: {len(df_cleaned)} rows, {len(df_cleaned.columns)} columns")
        print(f"📊 Removed: {len(df_original) - len(df_cleaned)} rows ({(len(df_original) - len(df_cleaned))/len(df_original)*100:.2f}%)")
        
        # Check key statistics
        target_col = 'ABI MS Promo Uplift - rel'
        
        print(f"\n📈 {target_col} Statistics:")
        print(f"Original - Mean: {df_original[target_col].mean():.3f}, Std: {df_original[target_col].std():.3f}")
        print(f"Cleaned  - Mean: {df_cleaned[target_col].mean():.3f}, Std: {df_cleaned[target_col].std():.3f}")
        
        # Check business logic constraints
        print(f"\n✅ Business Logic Validation:")
        
        # Check promo uplift
        max_uplift = df_cleaned[target_col].max()
        print(f"Max promo uplift: {max_uplift:.3f} (should be ≤ 50)")
        
        # Check coverage
        min_coverage = df_cleaned['ABI Coverage'].min()
        print(f"Min coverage: {min_coverage:.3f} (should be ≥ 0.1)")
        
        # Check duration
        max_duration = df_cleaned['ABI_Duration_Days'].max()
        print(f"Max duration: {max_duration} days (should be ≤ 25)")
        
        # Validation results
        validations = [
            max_uplift <= 50,
            min_coverage >= 0.1,
            max_duration <= 25
        ]
        
        if all(validations):
            print("\n🎯 ✅ ALL VALIDATIONS PASSED!")
            print("📁 Recommendation confirmed: Use cleaned_business_logic.csv")
        else:
            print("\n🎯 ❌ Some validations failed")
            
        print(f"\n📋 Summary:")
        print(f"• Original data: {len(df_original)} rows")
        print(f"• Cleaned data: {len(df_cleaned)} rows") 
        print(f"• Data preserved: {len(df_cleaned)/len(df_original)*100:.1f}%")
        print(f"• Business constraints: {'✅ Satisfied' if all(validations) else '❌ Violated'}")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
