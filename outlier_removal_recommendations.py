#!/usr/bin/env python3
"""
Outlier Removal Recommendations
This script provides final recommendations based on the analysis performed.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def analyze_cleaned_datasets():
    """Analyze the quality of different cleaned datasets"""
    print("🔍 ANALYZING CLEANED DATASETS QUALITY")
    print("=" * 60)
    
    datasets = {
        'Original': 'original_dataset.csv',
        'Business Logic': 'cleaned_business_logic.csv',
        'Z-Score (3)': 'cleaned_z-score_3.csv',
        'IQR (2.0)': 'cleaned_iqr_2.0.csv',
        'Hybrid': 'cleaned_hybrid.csv'
    }
    
    analysis_results = {}
    
    for name, filename in datasets.items():
        try:
            df = pd.read_csv(filename, index_col=0)
            
            # Key metrics analysis
            key_col = 'ABI MS Promo Uplift - rel'
            
            analysis_results[name] = {
                'rows': len(df),
                'mean_uplift': df[key_col].mean(),
                'std_uplift': df[key_col].std(),
                'median_uplift': df[key_col].median(),
                'skewness': stats.skew(df[key_col]),
                'kurtosis': stats.kurtosis(df[key_col]),
                'cv': df[key_col].std() / df[key_col].mean(),  # Coefficient of variation
                'q95_q05_ratio': df[key_col].quantile(0.95) / df[key_col].quantile(0.05)
            }
            
            print(f"\n📊 {name}:")
            print(f"  Rows: {len(df)}")
            print(f"  Mean Uplift: {df[key_col].mean():.3f}")
            print(f"  Std Uplift: {df[key_col].std():.3f}")
            print(f"  Skewness: {stats.skew(df[key_col]):.3f}")
            print(f"  Kurtosis: {stats.kurtosis(df[key_col]):.3f}")
            print(f"  CV: {df[key_col].std() / df[key_col].mean():.3f}")
            
        except FileNotFoundError:
            print(f"❌ {name}: File not found")
            analysis_results[name] = None
    
    return analysis_results

def create_data_quality_metrics(analysis_results):
    """Create data quality metrics for comparison"""
    print("\n📊 DATA QUALITY METRICS COMPARISON")
    print("=" * 60)
    
    print(f"{'Dataset':<20} {'Rows':<6} {'Mean':<8} {'Std':<8} {'Skew':<8} {'Kurt':<8} {'CV':<8}")
    print("-" * 70)
    
    for name, result in analysis_results.items():
        if result is not None:
            print(f"{name:<20} {result['rows']:<6} {result['mean_uplift']:<8.3f} "
                  f"{result['std_uplift']:<8.3f} {result['skewness']:<8.3f} "
                  f"{result['kurtosis']:<8.3f} {result['cv']:<8.3f}")

def generate_recommendations():
    """Generate final recommendations"""
    print("\n" + "=" * 60)
    print("🎯 OUTLIER REMOVAL RECOMMENDATIONS")
    print("=" * 60)
    
    recommendations = {
        "Conservative Approach (Recommended)": {
            "method": "Business Logic Only",
            "file": "cleaned_business_logic.csv",
            "description": "Remove only clearly problematic data points",
            "pros": [
                "Minimal data loss (only 3.1% removed)",
                "Preserves natural data distribution",
                "Removes only clearly erroneous values",
                "Maintains statistical power",
                "Low risk of removing valid extreme values"
            ],
            "cons": [
                "Some statistical outliers remain",
                "May still have some noise in the data"
            ],
            "use_case": "When you want to preserve as much data as possible and only remove clearly problematic values"
        },
        
        "Moderate Approach": {
            "method": "Z-Score (threshold = 3)",
            "file": "cleaned_z-score_3.csv",
            "description": "Remove statistical outliers beyond 3 standard deviations",
            "pros": [
                "Balanced approach (12.5% removed)",
                "Removes clear statistical outliers",
                "Well-established statistical method",
                "Good for normally distributed data"
            ],
            "cons": [
                "May remove some valid extreme values",
                "Assumes normal distribution",
                "More aggressive than business logic"
            ],
            "use_case": "When you want a balance between data preservation and outlier removal"
        },
        
        "Aggressive Approach": {
            "method": "IQR (multiplier = 2.0)",
            "file": "cleaned_iqr_2.0.csv",
            "description": "Remove outliers using modified IQR method",
            "pros": [
                "Very clean dataset",
                "Robust to distribution shape",
                "Removes most extreme values",
                "Good for skewed distributions"
            ],
            "cons": [
                "Significant data loss (75.6% removed)",
                "May remove valid business cases",
                "Reduced statistical power",
                "Risk of bias introduction"
            ],
            "use_case": "When data quality is more important than quantity and you can afford significant data loss"
        }
    }
    
    for approach, details in recommendations.items():
        print(f"\n🔹 {approach}")
        print(f"   Method: {details['method']}")
        print(f"   File: {details['file']}")
        print(f"   Description: {details['description']}")
        print(f"   \n   ✅ Pros:")
        for pro in details['pros']:
            print(f"      • {pro}")
        print(f"   \n   ❌ Cons:")
        for con in details['cons']:
            print(f"      • {con}")
        print(f"   \n   🎯 Use Case: {details['use_case']}")
    
    return recommendations

def create_decision_framework():
    """Create a decision framework for choosing the right approach"""
    print("\n" + "=" * 60)
    print("🧭 DECISION FRAMEWORK")
    print("=" * 60)
    
    framework = """
    Choose your approach based on these criteria:
    
    📊 DATA AVAILABILITY:
    • High (>1000 samples): Consider Aggressive or Moderate approach
    • Medium (500-1000): Use Moderate approach
    • Low (<500): Use Conservative approach ⭐ (YOUR CASE: 762 samples)
    
    🎯 ANALYSIS PURPOSE:
    • Exploratory Analysis: Conservative approach ⭐
    • Predictive Modeling: Moderate approach
    • Statistical Testing: Conservative or Moderate
    • Reporting to Stakeholders: Conservative approach ⭐
    
    📈 DATA DISTRIBUTION:
    • Highly skewed: IQR-based methods
    • Approximately normal: Z-score methods ⭐
    • Unknown distribution: Business Logic ⭐
    
    🔍 DOMAIN KNOWLEDGE:
    • Strong business understanding: Business Logic ⭐
    • Limited domain knowledge: Statistical methods
    
    ⚖️ RISK TOLERANCE:
    • Low risk tolerance: Conservative approach ⭐
    • Medium risk tolerance: Moderate approach
    • High risk tolerance: Aggressive approach
    """
    
    print(framework)

def final_recommendation():
    """Provide the final recommendation"""
    print("\n" + "=" * 60)
    print("🏆 FINAL RECOMMENDATION")
    print("=" * 60)
    
    recommendation = """
    🎯 RECOMMENDED APPROACH: Conservative (Business Logic Only)
    📁 USE FILE: cleaned_business_logic.csv
    
    🔍 RATIONALE:
    
    1. 📊 DATA PRESERVATION: Only removes 3.1% of data (24 rows), preserving 
       valuable information for analysis
    
    2. 🎯 BUSINESS RELEVANCE: Removes only clearly problematic values:
       • Promo uplift > 50% (unrealistic)
       • Coverage < 0.1 (data quality issues)
       • Duration > 25 days (unusual campaign length)
    
    3. 📈 STATISTICAL INTEGRITY: Maintains natural data distribution and 
       variability, crucial for accurate analysis
    
    4. 🔬 ANALYSIS SUITABILITY: Perfect for exploratory data analysis and 
       business reporting
    
    5. ⚖️ LOW RISK: Minimal chance of removing valid business cases
    
    📋 NEXT STEPS:
    
    1. Use 'cleaned_business_logic.csv' for your main analysis
    2. Document the 24 removed rows and reasons for removal
    3. Consider the moderate approach (Z-score) if you need cleaner data later
    4. Monitor results and adjust if needed based on domain expertise
    
    🚨 IMPORTANT: Always validate results with business stakeholders before 
    finalizing any outlier removal strategy!
    """
    
    print(recommendation)

def create_summary_report():
    """Create a comprehensive summary report"""
    print("\n" + "=" * 60)
    print("📋 EXECUTIVE SUMMARY")
    print("=" * 60)
    
    summary = """
    🔍 ANALYSIS PERFORMED:
    • Explored 762 rows × 20 columns dataset
    • Identified outliers using 5 different methods
    • Analyzed impact of outlier removal on data quality
    • Generated 4 cleaned datasets with different strategies
    
    📊 KEY FINDINGS:
    • 13 extreme outliers in promo uplift (Z-score > 3)
    • 54 multivariate outliers (7.09% of data)
    • 1 business logic violation (uplift > 50%)
    • 15 data quality issues (coverage < 0.1)
    
    🎯 RECOMMENDATION:
    • Use Business Logic approach (cleaned_business_logic.csv)
    • Removes only 24 rows (3.1% of data)
    • Preserves data integrity while removing clear problems
    • Suitable for business analysis and reporting
    
    📁 DELIVERABLES:
    • cleaned_business_logic.csv (RECOMMENDED)
    • cleaned_z-score_3.csv (Alternative)
    • cleaned_iqr_2.0.csv (Aggressive cleaning)
    • Analysis scripts and documentation
    """
    
    print(summary)

def main():
    """Main function"""
    print("🚀 OUTLIER REMOVAL FINAL RECOMMENDATIONS")
    print("=" * 60)
    
    # Analyze cleaned datasets
    analysis_results = analyze_cleaned_datasets()
    
    # Create quality metrics comparison
    create_data_quality_metrics(analysis_results)
    
    # Generate recommendations
    recommendations = generate_recommendations()
    
    # Create decision framework
    create_decision_framework()
    
    # Final recommendation
    final_recommendation()
    
    # Summary report
    create_summary_report()
    
    print("\n✅ ANALYSIS AND RECOMMENDATIONS COMPLETE!")
    print("📁 Check the generated CSV files for cleaned datasets")
    
    return analysis_results, recommendations

if __name__ == "__main__":
    analysis_results, recommendations = main()
