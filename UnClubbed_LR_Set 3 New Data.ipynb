# Linear Regression Analysis with Regularization Options - UNCLUBBED

## Overview
This notebook compares three regression approaches to reduce overfitting:
- **Linear Regression** (Baseline)
- **Ridge Regression** (L2 Regularization)
- **Lasso Regression** (L1 Regularization)

**Target Variable:** ABI MS Promo Uplift - rel
Removed Weighted Distribution, replaced with Numeric Dist

**Features:**

- ABI Mechanic (Encoded)
- Same Week
- 1 week after
- 2 week after
- 1 week before
- 2 week before
- Overlapping
- Avg Temp
- KSM
- ABI_Duration_Days
- ABI Depth_numeric
- ABI vs Segment PTC Index
- ABI Coverage




## 1. Data Loading and Preparation


import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearRegression, Lasso, Ridge
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import statsmodels.api as sm
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

print("Libraries imported successfully")


# Load the dataset
print("Loading dataset...")
df = pd.read_csv('cleaned_business_logic.csv')
print(f"Dataset shape: {df.shape}")
df.drop(columns=['Unnamed: 0'], inplace=True)
df.columns.tolist()



# Define the target variable and features
target_variable = 'ABI MS Promo Uplift - rel'

requested_features = [
    'ABI_Duration_Days',
    'ABI Mechanic',
    'Same Week',
    '1 wk after',
    '2 wk after',
    '1 wk before',
    '2 wk before',
    'Avg Temp',
    'KSM',
    'ABI vs Segment PTC Index Agg',
    'ABI Coverage'
]

# Check feature availability
available_features = [f for f in requested_features if f in df.columns]
missing_features = [f for f in requested_features if f not in df.columns]

print(f"Available features: {len(available_features)}")
print(f"Missing features: {len(missing_features)}")

if missing_features:
    print(f"Missing: {missing_features}")

# Prepare data
feature_columns = available_features
X = df[feature_columns].copy()
y = df[target_variable].copy()

print(f"Final shapes - X: {X.shape}, y: {y.shape}")


# Handle missing values and prepare data
print("Missing values analysis:")
missing_info = X.isnull().sum()
print(missing_info[missing_info > 0] if missing_info.sum() > 0 else "No missing values found.")

# Explicitly define categorical and numeric columns
# Categorical columns include timing indicators and mechanic type
categorical_columns = [
    'ABI Mechanic',
    'Same Week', 
    '1 wk after', 
    '2 wk after', 
    '1 wk before', 
    '2 wk before'
]

# Filter to only include categorical columns that exist in the dataset
categorical_columns = [col for col in categorical_columns if col in X.columns]

# Numeric columns are all the remaining columns
numeric_columns = [col for col in X.columns if col not in categorical_columns]

print(f"\nCategorical columns: {categorical_columns}")
print(f"Numeric columns: {numeric_columns}")

# Handle missing values separately for categorical and numeric columns
X_processed = X.copy()

# For numeric columns: fill with median
if len(numeric_columns) > 0:
    numeric_missing = X_processed[numeric_columns].isnull().sum()
    if numeric_missing.sum() > 0:
        print(f"\nNumeric missing values: {numeric_missing[numeric_missing > 0].to_dict()}")
        X_processed[numeric_columns] = X_processed[numeric_columns].fillna(X_processed[numeric_columns].median())
        print("Numeric missing values filled with median.")

# For categorical columns: fill with mode (most frequent value)
if len(categorical_columns) > 0:
    categorical_missing = X_processed[categorical_columns].isnull().sum()
    if categorical_missing.sum() > 0:
        print(f"\nCategorical missing values: {categorical_missing[categorical_missing > 0].to_dict()}")
        for col in categorical_columns:
            if X_processed[col].isnull().sum() > 0:
                mode_value = X_processed[col].mode()[0] if len(X_processed[col].mode()) > 0 else 'Unknown'
                X_processed[col] = X_processed[col].fillna(mode_value)
        print("Categorical missing values filled with mode.")

# Apply dummy encoding to categorical variables with 'No NIP' as base category
if len(categorical_columns) > 0:
    print(f"\nApplying dummy encoding to categorical variables...")
    for col in categorical_columns:
        print(f"Unique values in {col}: {X_processed[col].unique()}")
        
        # Create dummy variables, dropping 'No NIP' as base category
        dummy_vars = pd.get_dummies(X_processed[col], prefix=col, drop_first=False)
        
        # If 'No NIP' exists, drop it to use as base category
        no_nip_col = f"{col}_No NIP"
        if no_nip_col in dummy_vars.columns:
            dummy_vars = dummy_vars.drop(no_nip_col, axis=1)
            print(f"Using 'No NIP' as base category for {col}")
        else:
            # If 'No NIP' doesn't exist, drop the first category
            dummy_vars = dummy_vars.iloc[:, 1:]
            print(f"'No NIP' not found in {col}, using first category as base")
        
        # Add dummy variables to the dataset
        X_processed = pd.concat([X_processed, dummy_vars], axis=1)
    
    # Remove original categorical columns
    X_processed = X_processed.drop(categorical_columns, axis=1)
    print(f"Dummy encoding completed. New shape: {X_processed.shape}")

# Update feature columns list
feature_columns = X_processed.columns.tolist()
print(f"Final feature columns: {feature_columns}")

# Basic statistics
print("\nTarget variable statistics:")
print(y.describe())

# Split and scale data
X_train, X_test, y_train, y_test = train_test_split(
    X_processed, y, test_size=0.1, random_state=42
)

print(f"\nTraining samples: {len(X_train)}")
print(f"Test samples: {len(X_test)}")

# Scale features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Storage for results
models = {}
results_summary = []

print("Data preparation completed!")


## 2. Linear Regression (Baseline Model)


print("="*60)
print("LINEAR REGRESSION (BASELINE)")
print("="*60)

# Train Linear Regression
lr_model = LinearRegression()
lr_model.fit(X_train_scaled, y_train)

# Predictions
y_train_pred_lr = lr_model.predict(X_train_scaled)
y_test_pred_lr = lr_model.predict(X_test_scaled)

# Performance metrics
train_r2_lr = r2_score(y_train, y_train_pred_lr)
test_r2_lr = r2_score(y_test, y_test_pred_lr)
train_rmse_lr = np.sqrt(mean_squared_error(y_train, y_train_pred_lr))
test_rmse_lr = np.sqrt(mean_squared_error(y_test, y_test_pred_lr))
cv_scores_lr = cross_val_score(lr_model, X_train_scaled, y_train, cv=5, scoring='r2')

print(f"Training R²: {train_r2_lr:.4f}")
print(f"Test R²: {test_r2_lr:.4f}")
print(f"Training RMSE: {train_rmse_lr:.4f}")
print(f"Test RMSE: {test_rmse_lr:.4f}")
print(f"Cross-validation R² (mean ± std): {cv_scores_lr.mean():.4f} ± {cv_scores_lr.std():.4f}")

# Store results
models['Linear Regression'] = {
    'model': lr_model,
    'predictions_train': y_train_pred_lr,
    'predictions_test': y_test_pred_lr
}

results_summary.append({
    'Method': 'Linear Regression',
    'Train R²': train_r2_lr,
    'Test R²': test_r2_lr,
    'Train RMSE': train_rmse_lr,
    'Test RMSE': test_rmse_lr,
    'CV R² Mean': cv_scores_lr.mean(),
    'CV R² Std': cv_scores_lr.std()
})


### Linear Regression - OLS Statistical Analysis


# OLS Analysis for Linear Regression
print("OLS STATISTICAL ANALYSIS - LINEAR REGRESSION:")

# Create DataFrame with proper feature names for OLS
# Reset index to ensure alignment between y_train and X_train_df
X_train_df = pd.DataFrame(X_train_scaled, columns=feature_columns, index=y_train.index)
X_train_sm = sm.add_constant(X_train_df)
ols_model_lr = sm.OLS(y_train, X_train_sm).fit()
print(ols_model_lr.summary())

# Feature importance
feature_importance_lr = pd.DataFrame({
    'Feature': feature_columns,
    'Coefficient': lr_model.coef_,
    'Abs_Coefficient': np.abs(lr_model.coef_)
}).sort_values('Abs_Coefficient', ascending=False)

print("\nLinear Regression Feature Importance:")
print(feature_importance_lr)

# Create a mapping table to show which features correspond to which coefficients
print("\nOLS Coefficient Mapping:")
coef_mapping = pd.DataFrame({
    'Feature_Name': ['Intercept'] + list(feature_columns),
    'Coefficient': [ols_model_lr.params[0]] + list(ols_model_lr.params[1:]),
    'P_Value': [ols_model_lr.pvalues[0]] + list(ols_model_lr.pvalues[1:]),
    'Significant': ['Yes' if p < 0.05 else 'No' for p in ([ols_model_lr.pvalues[0]] + list(ols_model_lr.pvalues[1:]))]
}).round(4)
print(coef_mapping)


## 3. Ridge Regression (L2 Regularization)


print("="*60)
print("RIDGE REGRESSION (L2 REGULARIZATION)")
print("="*60)

# Hyperparameter tuning for Ridge
ridge_alphas = np.logspace(-3, 3, 20)
ridge_grid = GridSearchCV(Ridge(), {'alpha': ridge_alphas}, cv=5, scoring='r2')
ridge_grid.fit(X_train_scaled, y_train)
best_alpha_ridge = ridge_grid.best_params_['alpha']

print(f"Best Ridge alpha: {best_alpha_ridge:.4f}")

# Train Ridge model
ridge_model = Ridge(alpha=best_alpha_ridge)
ridge_model.fit(X_train_scaled, y_train)

# Predictions
y_train_pred_ridge = ridge_model.predict(X_train_scaled)
y_test_pred_ridge = ridge_model.predict(X_test_scaled)

# Performance metrics
train_r2_ridge = r2_score(y_train, y_train_pred_ridge)
test_r2_ridge = r2_score(y_test, y_test_pred_ridge)
train_rmse_ridge = np.sqrt(mean_squared_error(y_train, y_train_pred_ridge))
test_rmse_ridge = np.sqrt(mean_squared_error(y_test, y_test_pred_ridge))
cv_scores_ridge = cross_val_score(ridge_model, X_train_scaled, y_train, cv=5, scoring='r2')

print(f"Training R²: {train_r2_ridge:.4f}")
print(f"Test R²: {test_r2_ridge:.4f}")
print(f"Training RMSE: {train_rmse_ridge:.4f}")
print(f"Test RMSE: {test_rmse_ridge:.4f}")
print(f"Cross-validation R² (mean ± std): {cv_scores_ridge.mean():.4f} ± {cv_scores_ridge.std():.4f}")

# Store results
models['Ridge Regression'] = {
    'model': ridge_model,
    'predictions_train': y_train_pred_ridge,
    'predictions_test': y_test_pred_ridge,
    'alpha': best_alpha_ridge
}

results_summary.append({
    'Method': 'Ridge Regression',
    'Train R²': train_r2_ridge,
    'Test R²': test_r2_ridge,
    'Train RMSE': train_rmse_ridge,
    'Test RMSE': test_rmse_ridge,
    'CV R² Mean': cv_scores_ridge.mean(),
    'CV R² Std': cv_scores_ridge.std()
})


# Ridge coefficient analysis
print("RIDGE REGRESSION COEFFICIENTS ANALYSIS:")
ridge_coef_df = pd.DataFrame({
    'Feature': feature_columns,
    'Coefficient': ridge_model.coef_,
    'Abs_Coefficient': np.abs(ridge_model.coef_)
}).sort_values('Abs_Coefficient', ascending=False)

print(ridge_coef_df)


## 4. Lasso Regression (L1 Regularization)


print("="*60)
print("LASSO REGRESSION (L1 REGULARIZATION)")
print("="*60)

# Hyperparameter tuning for Lasso
lasso_alphas = np.logspace(-3, 1, 20)
lasso_grid = GridSearchCV(Lasso(max_iter=2000), {'alpha': lasso_alphas}, cv=5, scoring='r2')
lasso_grid.fit(X_train_scaled, y_train)
best_alpha_lasso = lasso_grid.best_params_['alpha']

print(f"Best Lasso alpha: {best_alpha_lasso:.4f}")

# Train Lasso model
lasso_model = Lasso(alpha=best_alpha_lasso, max_iter=2000)
lasso_model.fit(X_train_scaled, y_train)

# Predictions
y_train_pred_lasso = lasso_model.predict(X_train_scaled)
y_test_pred_lasso = lasso_model.predict(X_test_scaled)

# Performance metrics
train_r2_lasso = r2_score(y_train, y_train_pred_lasso)
test_r2_lasso = r2_score(y_test, y_test_pred_lasso)
train_rmse_lasso = np.sqrt(mean_squared_error(y_train, y_train_pred_lasso))
test_rmse_lasso = np.sqrt(mean_squared_error(y_test, y_test_pred_lasso))
cv_scores_lasso = cross_val_score(lasso_model, X_train_scaled, y_train, cv=5, scoring='r2')

print(f"Training R²: {train_r2_lasso:.4f}")
print(f"Test R²: {test_r2_lasso:.4f}")
print(f"Training RMSE: {train_rmse_lasso:.4f}")
print(f"Test RMSE: {test_rmse_lasso:.4f}")
print(f"Cross-validation R² (mean ± std): {cv_scores_lasso.mean():.4f} ± {cv_scores_lasso.std():.4f}")

# Store results
models['Lasso Regression'] = {
    'model': lasso_model,
    'predictions_train': y_train_pred_lasso,
    'predictions_test': y_test_pred_lasso,
    'alpha': best_alpha_lasso
}

results_summary.append({
    'Method': 'Lasso Regression',
    'Train R²': train_r2_lasso,
    'Test R²': test_r2_lasso,
    'Train RMSE': train_rmse_lasso,
    'Test RMSE': test_rmse_lasso,
    'CV R² Mean': cv_scores_lasso.mean(),
    'CV R² Std': cv_scores_lasso.std()
})


# Lasso feature selection analysis
print("LASSO FEATURE SELECTION ANALYSIS:")
lasso_coef_df = pd.DataFrame({
    'Feature': feature_columns,
    'Coefficient': lasso_model.coef_,
    'Abs_Coefficient': np.abs(lasso_model.coef_)
}).sort_values('Abs_Coefficient', ascending=False)

selected_features = lasso_coef_df[lasso_coef_df['Coefficient'] != 0]
eliminated_features = lasso_coef_df[lasso_coef_df['Coefficient'] == 0]

print(f"Features selected by Lasso: {len(selected_features)} out of {len(feature_columns)}")
print("\nSelected features:")
print(selected_features)

if len(eliminated_features) > 0:
    print(f"\nFeatures eliminated by Lasso: {len(eliminated_features)}")
    print(eliminated_features['Feature'].tolist())


## 5. Model Comparison Summary


# Summary comparison
print("="*60)
print("MODEL COMPARISON SUMMARY")
print("="*60)

comparison_df = pd.DataFrame(results_summary)
print(comparison_df.round(4))

# Best model identification
best_test_r2 = max([r['Test R²'] for r in results_summary])
best_model_name = [r['Method'] for r in results_summary if r['Test R²'] == best_test_r2][0]
print(f"\nBest performing model (Test R²): {best_model_name}")

# Overfitting analysis
print("\nOverfitting Analysis (Train R² - Test R²):")
for result in results_summary:
    method = result['Method']
    train_r2 = result['Train R²']
    test_r2 = result['Test R²']
    overfitting_score = train_r2 - test_r2
    print(f"{method}: {overfitting_score:.4f}")
    
print("\nNote: Lower overfitting score indicates better generalization")


## 6. Comprehensive Visualizations


# Model comparison visualization
plt.figure(figsize=(20, 15))

# Actual vs Predicted for all models
for i, (model_name, model_data) in enumerate(models.items()):
    # Training set
    plt.subplot(3, 3, i*3 + 1)
    plt.scatter(y_train, model_data['predictions_train'], alpha=0.6, s=50, color='blue')
    plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)
    plt.xlabel('Actual')
    plt.ylabel('Predicted')
    train_r2 = r2_score(y_train, model_data['predictions_train'])
    plt.title(f'{model_name} - Training\nR² = {train_r2:.3f}')
    plt.grid(True, alpha=0.3)
    
    # Test set
    plt.subplot(3, 3, i*3 + 2)
    plt.scatter(y_test, model_data['predictions_test'], alpha=0.6, s=50, color='green')
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    plt.xlabel('Actual')
    plt.ylabel('Predicted')
    test_r2 = r2_score(y_test, model_data['predictions_test'])
    plt.title(f'{model_name} - Test\nR² = {test_r2:.3f}')
    plt.grid(True, alpha=0.3)
    
    # Residuals
    plt.subplot(3, 3, i*3 + 3)
    residuals = y_test - model_data['predictions_test']
    plt.hist(residuals, bins=20, alpha=0.7, edgecolor='black')
    plt.xlabel('Residuals')
    plt.ylabel('Frequency')
    plt.title(f'{model_name} - Residuals')
    plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('updated_models_comparison.png', dpi=300, bbox_inches='tight')
plt.show()


# Performance comparison charts
plt.figure(figsize=(15, 10))

methods = [r['Method'] for r in results_summary]
train_r2s = [r['Train R²'] for r in results_summary]
test_r2s = [r['Test R²'] for r in results_summary]
cv_means = [r['CV R² Mean'] for r in results_summary]

x = np.arange(len(methods))
width = 0.25

# R² comparison
plt.subplot(2, 2, 1)
plt.bar(x - width, train_r2s, width, label='Train R²', alpha=0.8)
plt.bar(x, test_r2s, width, label='Test R²', alpha=0.8)
plt.bar(x + width, cv_means, width, label='CV R² Mean', alpha=0.8)
plt.xlabel('Method')
plt.ylabel('R² Score')
plt.title('R² Comparison Across Methods')
plt.xticks(x, methods, rotation=45)
plt.legend()
plt.grid(True, alpha=0.3)

# RMSE comparison
train_rmses = [r['Train RMSE'] for r in results_summary]
test_rmses = [r['Test RMSE'] for r in results_summary]

plt.subplot(2, 2, 2)
plt.bar(x - width/2, train_rmses, width, label='Train RMSE', alpha=0.8)
plt.bar(x + width/2, test_rmses, width, label='Test RMSE', alpha=0.8)
plt.xlabel('Method')
plt.ylabel('RMSE')
plt.title('RMSE Comparison Across Methods')
plt.xticks(x, methods, rotation=45)
plt.legend()
plt.grid(True, alpha=0.3)

# Cross-validation with error bars
plt.subplot(2, 2, 3)
cv_stds = [r['CV R² Std'] for r in results_summary]
plt.bar(x, cv_means, yerr=cv_stds, capsize=5, alpha=0.8)
plt.xlabel('Method')
plt.ylabel('CV R² Score')
plt.title('Cross-Validation R² (with std)')
plt.xticks(x, methods, rotation=45)
plt.grid(True, alpha=0.3)

# Overfitting comparison
plt.subplot(2, 2, 4)
overfitting_scores = [r['Train R²'] - r['Test R²'] for r in results_summary]
colors = ['red' if score > 0.1 else 'orange' if score > 0.05 else 'green' for score in overfitting_scores]
plt.bar(x, overfitting_scores, color=colors, alpha=0.8)
plt.xlabel('Method')
plt.ylabel('Overfitting Score (Train R² - Test R²)')
plt.title('Overfitting Analysis')
plt.xticks(x, methods, rotation=45)
plt.axhline(y=0.1, color='red', linestyle='--', alpha=0.5, label='High overfitting')
plt.axhline(y=0.05, color='orange', linestyle='--', alpha=0.5, label='Moderate overfitting')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('updated_performance_comparison.png', dpi=300, bbox_inches='tight')
plt.show()


## 7. Feature Importance Comparison


# Feature coefficients comparison
plt.figure(figsize=(15, 8))

# Create coefficient comparison dataframe
coef_comparison = pd.DataFrame({
    'Feature': feature_columns,
    'Linear': lr_model.coef_,
    'Ridge': ridge_model.coef_,
    'Lasso': lasso_model.coef_
})

# Plot coefficients for each method
x_pos = np.arange(len(feature_columns))
width = 0.25

plt.bar(x_pos - width, coef_comparison['Linear'], width, label='Linear Regression', alpha=0.8)
plt.bar(x_pos, coef_comparison['Ridge'], width, label='Ridge Regression', alpha=0.8)
plt.bar(x_pos + width, coef_comparison['Lasso'], width, label='Lasso Regression', alpha=0.8)

plt.xlabel('Features')
plt.ylabel('Coefficient Value')
plt.title('Feature Coefficients Comparison Across Methods')
plt.xticks(x_pos, feature_columns, rotation=45, ha='right')
plt.legend()
plt.grid(True, alpha=0.3)
plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)

plt.tight_layout()
plt.savefig('updated_feature_coefficients.png', dpi=300, bbox_inches='tight')
plt.show()

print("\nCoefficient Comparison:")
print(coef_comparison.round(4))
