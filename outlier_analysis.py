#!/usr/bin/env python3
"""
Detailed Outlier Analysis Script
This script provides detailed analysis of outliers and their impact on the dataset.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load the dataset"""
    return pd.read_csv('clean_new_test_output_ads_v3.csv', index_col=0)

def analyze_extreme_outliers(df):
    """Analyze the most extreme outliers in key columns"""
    print("🔍 EXTREME OUTLIER ANALYSIS")
    print("=" * 50)
    
    # Focus on key columns that showed significant outliers
    key_columns = [
        'ABI MS Promo Uplift - rel',
        'ABI Coverage', 
        'ABI Rounded',
        'ABI Base W_Distribution',
        'ABI_Promo_W_W_Distribution'
    ]
    
    for col in key_columns:
        print(f"\n📊 {col}:")
        
        # Calculate Z-scores
        z_scores = np.abs(stats.zscore(df[col]))
        
        # Find extreme outliers (Z-score > 3)
        extreme_outliers = df[z_scores > 3]
        
        if len(extreme_outliers) > 0:
            print(f"  🚨 Extreme outliers (Z-score > 3): {len(extreme_outliers)}")
            print(f"  📈 Max value: {df[col].max():.3f}")
            print(f"  📉 Min value: {df[col].min():.3f}")
            print(f"  📊 Mean: {df[col].mean():.3f}")
            print(f"  📊 Median: {df[col].median():.3f}")
            print(f"  📊 Std: {df[col].std():.3f}")
            
            # Show top 5 extreme values
            top_outliers = extreme_outliers.nlargest(5, col)
            print(f"  🔝 Top 5 extreme values:")
            for idx, row in top_outliers.iterrows():
                print(f"    Index {idx}: {row[col]:.3f} (Z-score: {z_scores[idx]:.2f})")
        else:
            print(f"  ✅ No extreme outliers (Z-score > 3)")

def analyze_multivariate_outliers(df):
    """Analyze multivariate outliers using Mahalanobis distance"""
    print("\n🔍 MULTIVARIATE OUTLIER ANALYSIS")
    print("=" * 50)
    
    # Select numeric columns for multivariate analysis
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    
    # Remove columns with missing values for this analysis
    clean_cols = [col for col in numeric_cols if df[col].isnull().sum() == 0]
    
    print(f"Using {len(clean_cols)} columns for multivariate analysis")
    
    # Calculate Mahalanobis distance
    df_clean = df[clean_cols].copy()
    
    # Standardize the data
    from sklearn.preprocessing import StandardScaler
    scaler = StandardScaler()
    df_scaled = pd.DataFrame(scaler.fit_transform(df_clean), 
                           columns=clean_cols, 
                           index=df_clean.index)
    
    # Calculate covariance matrix
    cov_matrix = np.cov(df_scaled.T)
    
    # Calculate Mahalanobis distance for each point
    try:
        inv_cov_matrix = np.linalg.inv(cov_matrix)
        mahal_distances = []
        
        for i, row in df_scaled.iterrows():
            diff = row.values - df_scaled.mean().values
            mahal_dist = np.sqrt(diff.T @ inv_cov_matrix @ diff)
            mahal_distances.append(mahal_dist)
        
        df['mahalanobis_distance'] = mahal_distances
        
        # Identify multivariate outliers (using chi-square distribution)
        threshold = np.sqrt(stats.chi2.ppf(0.975, df_scaled.shape[1]))  # 97.5% confidence
        multivariate_outliers = df[df['mahalanobis_distance'] > threshold]
        
        print(f"🎯 Multivariate outliers (threshold: {threshold:.2f}): {len(multivariate_outliers)}")
        print(f"📊 Percentage: {len(multivariate_outliers)/len(df)*100:.2f}%")
        
        if len(multivariate_outliers) > 0:
            print(f"🔝 Top 10 multivariate outliers:")
            top_multivariate = multivariate_outliers.nlargest(10, 'mahalanobis_distance')
            for idx, row in top_multivariate.iterrows():
                print(f"  Index {idx}: Mahalanobis distance = {row['mahalanobis_distance']:.2f}")
        
        return multivariate_outliers
        
    except np.linalg.LinAlgError:
        print("❌ Could not calculate Mahalanobis distance (singular covariance matrix)")
        return pd.DataFrame()

def analyze_business_logic_outliers(df):
    """Analyze outliers from a business logic perspective"""
    print("\n🔍 BUSINESS LOGIC OUTLIER ANALYSIS")
    print("=" * 50)
    
    # Check for impossible or suspicious values
    print("🚨 Checking for impossible/suspicious values:")
    
    # Coverage should be between 0 and 1
    invalid_coverage = df[(df['ABI Coverage'] < 0) | (df['ABI Coverage'] > 1)]
    print(f"  Invalid coverage values (< 0 or > 1): {len(invalid_coverage)}")
    
    # Promo uplift should be positive (generally)
    negative_uplift = df[df['ABI MS Promo Uplift - rel'] < 0]
    print(f"  Negative promo uplift: {len(negative_uplift)}")
    
    # Very high promo uplift (> 50% might be suspicious)
    very_high_uplift = df[df['ABI MS Promo Uplift - rel'] > 50]
    print(f"  Very high promo uplift (> 50%): {len(very_high_uplift)}")
    
    # Duration should be reasonable (let's say > 0 and < 60 days)
    invalid_duration = df[(df['ABI_Duration_Days'] <= 0) | (df['ABI_Duration_Days'] > 60)]
    print(f"  Invalid duration (≤ 0 or > 60 days): {len(invalid_duration)}")
    
    # Temperature should be reasonable (let's say between -20 and 50 Celsius)
    invalid_temp = df[(df['Avg Temp'] < -20) | (df['Avg Temp'] > 50)]
    print(f"  Invalid temperature (< -20°C or > 50°C): {len(invalid_temp)}")
    
    return {
        'invalid_coverage': invalid_coverage,
        'negative_uplift': negative_uplift,
        'very_high_uplift': very_high_uplift,
        'invalid_duration': invalid_duration,
        'invalid_temp': invalid_temp
    }

def create_outlier_impact_analysis(df):
    """Analyze the impact of outliers on key statistics"""
    print("\n🔍 OUTLIER IMPACT ANALYSIS")
    print("=" * 50)
    
    key_columns = ['ABI MS Promo Uplift - rel', 'ABI Coverage', 'ABI Base W_Distribution']
    
    for col in key_columns:
        print(f"\n📊 Impact analysis for {col}:")
        
        # Original statistics
        original_mean = df[col].mean()
        original_std = df[col].std()
        original_median = df[col].median()
        
        # Remove outliers using IQR method
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        df_no_outliers = df[(df[col] >= lower_bound) & (df[col] <= upper_bound)]
        
        # Statistics without outliers
        clean_mean = df_no_outliers[col].mean()
        clean_std = df_no_outliers[col].std()
        clean_median = df_no_outliers[col].median()
        
        # Calculate impact
        mean_change = ((clean_mean - original_mean) / original_mean) * 100
        std_change = ((clean_std - original_std) / original_std) * 100
        median_change = ((clean_median - original_median) / original_median) * 100
        
        print(f"  📈 Mean: {original_mean:.3f} → {clean_mean:.3f} ({mean_change:+.2f}%)")
        print(f"  📊 Std: {original_std:.3f} → {clean_std:.3f} ({std_change:+.2f}%)")
        print(f"  📊 Median: {original_median:.3f} → {clean_median:.3f} ({median_change:+.2f}%)")
        print(f"  🗑️ Removed: {len(df) - len(df_no_outliers)} rows ({((len(df) - len(df_no_outliers))/len(df)*100):.2f}%)")

def main():
    """Main function"""
    print("🚀 DETAILED OUTLIER ANALYSIS")
    print("=" * 60)
    
    # Load data
    df = load_data()
    
    # Perform different types of outlier analysis
    analyze_extreme_outliers(df)
    multivariate_outliers = analyze_multivariate_outliers(df)
    business_outliers = analyze_business_logic_outliers(df)
    create_outlier_impact_analysis(df)
    
    print("\n" + "=" * 60)
    print("✅ DETAILED ANALYSIS COMPLETE")
    print("=" * 60)
    
    return df, multivariate_outliers, business_outliers

if __name__ == "__main__":
    df, multivariate_outliers, business_outliers = main()
