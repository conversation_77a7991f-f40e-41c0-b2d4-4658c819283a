#!/usr/bin/env python3
"""
Outlier Removal Strategies
This script implements different outlier removal strategies and compares their effectiveness.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest
from sklearn.covariance import EllipticEnvelope
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load the dataset"""
    return pd.read_csv('clean_new_test_output_ads_v3.csv', index_col=0)

class OutlierRemovalStrategies:
    """Class containing different outlier removal strategies"""
    
    def __init__(self, df):
        self.df = df.copy()
        self.numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        # Remove columns with missing values for some methods
        self.clean_numeric_cols = [col for col in self.numeric_cols if df[col].isnull().sum() == 0]
    
    def iqr_method(self, multiplier=1.5):
        """Remove outliers using IQR method"""
        print(f"🔧 IQR Method (multiplier: {multiplier})")
        df_clean = self.df.copy()
        removed_indices = set()
        
        for col in self.numeric_cols:
            if self.df[col].isnull().sum() > 0:
                continue
                
            Q1 = self.df[col].quantile(0.25)
            Q3 = self.df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - multiplier * IQR
            upper_bound = Q3 + multiplier * IQR
            
            outliers = self.df[(self.df[col] < lower_bound) | (self.df[col] > upper_bound)]
            removed_indices.update(outliers.index)
        
        df_clean = df_clean.drop(removed_indices)
        print(f"  📊 Removed {len(removed_indices)} rows ({len(removed_indices)/len(self.df)*100:.2f}%)")
        print(f"  📊 Remaining: {len(df_clean)} rows")
        
        return df_clean, removed_indices
    
    def zscore_method(self, threshold=3):
        """Remove outliers using Z-score method"""
        print(f"🔧 Z-Score Method (threshold: {threshold})")
        df_clean = self.df.copy()
        removed_indices = set()
        
        for col in self.numeric_cols:
            if self.df[col].isnull().sum() > 0:
                continue
                
            z_scores = np.abs(stats.zscore(self.df[col]))
            outliers = self.df[z_scores > threshold]
            removed_indices.update(outliers.index)
        
        df_clean = df_clean.drop(removed_indices)
        print(f"  📊 Removed {len(removed_indices)} rows ({len(removed_indices)/len(self.df)*100:.2f}%)")
        print(f"  📊 Remaining: {len(df_clean)} rows")
        
        return df_clean, removed_indices
    
    def isolation_forest_method(self, contamination=0.1):
        """Remove outliers using Isolation Forest"""
        print(f"🔧 Isolation Forest Method (contamination: {contamination})")
        
        # Use only clean numeric columns
        df_numeric = self.df[self.clean_numeric_cols].copy()
        
        # Standardize the data
        scaler = StandardScaler()
        df_scaled = scaler.fit_transform(df_numeric)
        
        # Apply Isolation Forest
        iso_forest = IsolationForest(contamination=contamination, random_state=42)
        outlier_labels = iso_forest.fit_predict(df_scaled)
        
        # -1 indicates outlier, 1 indicates normal
        outlier_indices = self.df.index[outlier_labels == -1]
        df_clean = self.df.drop(outlier_indices)
        
        print(f"  📊 Removed {len(outlier_indices)} rows ({len(outlier_indices)/len(self.df)*100:.2f}%)")
        print(f"  📊 Remaining: {len(df_clean)} rows")
        
        return df_clean, set(outlier_indices)
    
    def elliptic_envelope_method(self, contamination=0.1):
        """Remove outliers using Elliptic Envelope (Robust Covariance)"""
        print(f"🔧 Elliptic Envelope Method (contamination: {contamination})")
        
        # Use only clean numeric columns
        df_numeric = self.df[self.clean_numeric_cols].copy()
        
        # Standardize the data
        scaler = StandardScaler()
        df_scaled = scaler.fit_transform(df_numeric)
        
        # Apply Elliptic Envelope
        elliptic_env = EllipticEnvelope(contamination=contamination, random_state=42)
        outlier_labels = elliptic_env.fit_predict(df_scaled)
        
        # -1 indicates outlier, 1 indicates normal
        outlier_indices = self.df.index[outlier_labels == -1]
        df_clean = self.df.drop(outlier_indices)
        
        print(f"  📊 Removed {len(outlier_indices)} rows ({len(outlier_indices)/len(self.df)*100:.2f}%)")
        print(f"  📊 Remaining: {len(df_clean)} rows")
        
        return df_clean, set(outlier_indices)
    
    def business_logic_method(self):
        """Remove outliers based on business logic"""
        print("🔧 Business Logic Method")
        df_clean = self.df.copy()
        removed_indices = set()
        
        # Remove rows with very high promo uplift (> 50% seems unrealistic)
        very_high_uplift = self.df[self.df['ABI MS Promo Uplift - rel'] > 50]
        removed_indices.update(very_high_uplift.index)
        print(f"  🚨 Removed {len(very_high_uplift)} rows with promo uplift > 50%")
        
        # Remove rows with very low coverage (< 0.1 might indicate data quality issues)
        very_low_coverage = self.df[self.df['ABI Coverage'] < 0.1]
        removed_indices.update(very_low_coverage.index)
        print(f"  🚨 Removed {len(very_low_coverage)} rows with coverage < 0.1")
        
        # Remove rows with extreme duration (> 25 days might be unusual)
        extreme_duration = self.df[self.df['ABI_Duration_Days'] > 25]
        removed_indices.update(extreme_duration.index)
        print(f"  🚨 Removed {len(extreme_duration)} rows with duration > 25 days")
        
        df_clean = df_clean.drop(removed_indices)
        print(f"  📊 Total removed: {len(removed_indices)} rows ({len(removed_indices)/len(self.df)*100:.2f}%)")
        print(f"  📊 Remaining: {len(df_clean)} rows")
        
        return df_clean, removed_indices
    
    def hybrid_method(self):
        """Combine multiple methods for robust outlier removal"""
        print("🔧 Hybrid Method (Business Logic + Modified IQR)")
        
        # Start with business logic
        df_clean, business_removed = self.business_logic_method()
        
        # Apply modified IQR (more conservative) to remaining data
        print("\n  🔄 Applying modified IQR to remaining data...")
        outlier_remover = OutlierRemovalStrategies(df_clean)
        df_final, iqr_removed = outlier_remover.iqr_method(multiplier=2.0)  # More conservative
        
        total_removed = business_removed.union(iqr_removed)
        
        print(f"\n  📊 Total removed: {len(total_removed)} rows ({len(total_removed)/len(self.df)*100:.2f}%)")
        print(f"  📊 Final dataset: {len(df_final)} rows")
        
        return df_final, total_removed

def compare_methods(df):
    """Compare different outlier removal methods"""
    print("🔍 COMPARING OUTLIER REMOVAL METHODS")
    print("=" * 60)
    
    outlier_remover = OutlierRemovalStrategies(df)
    results = {}
    
    # Test different methods
    methods = [
        ('IQR (1.5)', lambda: outlier_remover.iqr_method(1.5)),
        ('IQR (2.0)', lambda: outlier_remover.iqr_method(2.0)),
        ('Z-Score (3)', lambda: outlier_remover.zscore_method(3)),
        ('Z-Score (2.5)', lambda: outlier_remover.zscore_method(2.5)),
        ('Isolation Forest (0.05)', lambda: outlier_remover.isolation_forest_method(0.05)),
        ('Isolation Forest (0.1)', lambda: outlier_remover.isolation_forest_method(0.1)),
        ('Elliptic Envelope (0.05)', lambda: outlier_remover.elliptic_envelope_method(0.05)),
        ('Business Logic', lambda: outlier_remover.business_logic_method()),
        ('Hybrid', lambda: outlier_remover.hybrid_method())
    ]
    
    for method_name, method_func in methods:
        print(f"\n{method_name}:")
        try:
            df_clean, removed_indices = method_func()
            
            # Calculate impact on key statistics
            key_col = 'ABI MS Promo Uplift - rel'
            original_mean = df[key_col].mean()
            clean_mean = df_clean[key_col].mean()
            mean_change = ((clean_mean - original_mean) / original_mean) * 100
            
            results[method_name] = {
                'rows_removed': len(removed_indices),
                'percentage_removed': len(removed_indices) / len(df) * 100,
                'rows_remaining': len(df_clean),
                'mean_change': mean_change,
                'removed_indices': removed_indices
            }
            
            print(f"  📊 Mean change in {key_col}: {mean_change:+.2f}%")
            
        except Exception as e:
            print(f"  ❌ Error: {str(e)}")
            results[method_name] = None
    
    return results

def create_comparison_summary(results):
    """Create a summary comparison of all methods"""
    print("\n" + "=" * 60)
    print("📋 OUTLIER REMOVAL METHODS COMPARISON")
    print("=" * 60)
    
    print(f"{'Method':<25} {'Removed':<8} {'%':<6} {'Remaining':<10} {'Mean Δ%':<8}")
    print("-" * 60)
    
    for method_name, result in results.items():
        if result is not None:
            print(f"{method_name:<25} {result['rows_removed']:<8} "
                  f"{result['percentage_removed']:<6.1f} {result['rows_remaining']:<10} "
                  f"{result['mean_change']:<+8.1f}")
        else:
            print(f"{method_name:<25} {'ERROR':<8}")

def save_cleaned_datasets(df, results):
    """Save cleaned datasets from different methods"""
    print("\n💾 SAVING CLEANED DATASETS")
    print("=" * 40)
    
    # Save original for reference
    df.to_csv('original_dataset.csv')
    print("📁 Saved: original_dataset.csv")
    
    # Save results from key methods
    key_methods = ['Business Logic', 'Hybrid', 'IQR (2.0)', 'Z-Score (3)']
    
    for method_name in key_methods:
        if method_name in results and results[method_name] is not None:
            removed_indices = results[method_name]['removed_indices']
            df_clean = df.drop(removed_indices)
            
            filename = f"cleaned_{method_name.lower().replace(' ', '_').replace('(', '').replace(')', '')}.csv"
            df_clean.to_csv(filename)
            print(f"📁 Saved: {filename}")

def main():
    """Main function"""
    print("🚀 OUTLIER REMOVAL STRATEGY ANALYSIS")
    print("=" * 60)
    
    # Load data
    df = load_data()
    print(f"📊 Original dataset: {len(df)} rows, {len(df.columns)} columns")
    
    # Compare methods
    results = compare_methods(df)
    
    # Create summary
    create_comparison_summary(results)
    
    # Save cleaned datasets
    save_cleaned_datasets(df, results)
    
    print("\n✅ ANALYSIS COMPLETE!")
    return df, results

if __name__ == "__main__":
    df, results = main()
